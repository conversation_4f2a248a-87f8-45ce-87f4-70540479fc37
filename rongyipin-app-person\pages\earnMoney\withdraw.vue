<template>
  <view class="container">
    <!-- 头部导航栏 -->
    <u-navbar :autoBack="true" title="提现" :is-fixed="true" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <!-- 零钱余额 -->
    <view class="balance-section">
      <view class="section-title">零钱余额（元）</view>
      <view class="balance-amount">{{ balance }}</view>
      <view class="detail-btn" @click="toBalanceDetail">余额明细</view>
    </view>

    <!-- 提现方式 -->
    <view class="section">
      <view class="section-title">提现方式</view>
      <view class="method-item">
        <view class="method-info">
          <text class="method-label">{{ bank.payment_addr }}</text>
          <text class="account">{{ bank.payment_account }}</text>
        </view>
        <view class="method-check">
          <text @click="openBankList">更换银行卡</text>
        </view>
      </view>

      <!-- 更换银行卡弹框 -->
      <u-popup :show="showBankList" mode="center" border-radius="16" width="90%">
        <view class="bank-list-popup">
          <view class="popup-header">
            <text class="popup-title">选择银行卡</text>
            <u-icon name="close" size="24" @click="showBankList = false"></u-icon>
          </view>

          <view class="bank-list">
            <view 
              class="bank-item" 
              v-for="(bank, index) in bankList" 
              :key="index"
              @click="selectBank(bank)"
            >
              <view class="bank-info">
                <image class="bank-logo" :src="bank.logo" mode="aspectFit"></image>
                <text class="bank-name">{{ bank.payment_addr }}</text>
                <text class="bank-number">{{ bank.payment_account }}</text>
              </view>
              <view class="bank-check" v-if="selectedBank === bank">
                <u-icon name="checkmark" size="20" color="#2979ff"></u-icon>
              </view>
            </view>

           
          </view>

          <view class="popup-footer">
             <view class="add-bank-btn" @click="navigateToAddBank">
              添加银行卡
            </view>
            <u-button 
              type="primary" 
              shape="circle" 
              @click="confirmBankSelection"
            >
              确定
            </u-button>
          </view>
        </view>
      </u-popup>
    </view>

    <!-- 提现金额 -->
    <!-- <view class="section">
      <view class="section-title">提现金额</view>
      
      <view class="amount-options">
        <view 
          class="amount-option" 
          v-for="amount in fixedAmounts" 
          :key="amount"
          :class="{ active: selectedAmount === amount && !showCustomModal }"
          @click="selectAmount(amount)"
        >
          {{ amount }}元
        </view>
      </view>
      
      <view 
        class="custom-amount" 
        :class="{ active: showCustomModal }"
        @click="showCustomAmountInput"
      >
        自定义金额
      </view>
    </view> -->

    <!-- 确认提现按钮 -->
    <view class="submit-btn-container">
      <u-button 
        type="primary" 
        shape="circle" 
        @click="handleWithdraw"
        
      >
        去提现
      </u-button>
    </view>

    <!-- 自定义金额弹窗 - 使用u-popup -->
    <u-popup :show="showCustomModal" mode="center" border-radius="16" width="80%">
      <view class="custom-amount-popup">
        <view class="popup-header">
          <text class="popup-title">请输入提现金额</text>
          <u-icon name="close" size="24" @click="showCustomModal = false"></u-icon>
        </view>
        
        <view class="popup-content">
          <!-- 修改输入框容器样式 -->
          <view class="input-container">
            <u-input 
              v-model="customAmount" 
              type="digit" 
              placeholder="请输入提现金额"
              :border="true"
              :focus="true"
              clearable
              placeholder-style="color:#ccc"
            />
          </view>
          <view class="balance-hint">当前余额：{{ balance }}元</view>
          
          <view class="amount-buttons">
            <view 
              class="quick-amount" 
              v-for="amt in [100, 200, 500]" 
              :key="amt"
              @click="customAmount = amt"
            >
              {{ amt }}元
            </view>
          </view>
        </view>
        
        <view class="popup-footer">
          <u-button 
            type="primary" 
            shape="circle" 
            @click="confirmCustomAmount"
            :disabled="!customAmount || customAmount <= 0"
          >
            确定
          </u-button>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import { rider } from '@/utils/api.js';
export default {
  data() {
    return {
      bank: {}, // 默认银行卡信息
      balance: 100,
      withdrawMethod: 'alipay',
      methods: [
        { value: '1', label: '支付宝提现', account: '123智库2' },
        { value: '2', label: '银行卡提现', account: 'B1' },
      ],
      fixedAmounts: [10, 20, 50],
      selectedAmount: null,
      showCustomModal: false,
      customAmount: '',
      showBankList: false, // 控制弹框显示
      bankList: [], // 银行卡列表
      selectedBank: null // 选中的银行卡
    };
  },
  computed: {
    canWithdraw() {
      return this.withdrawMethod && (this.selectedAmount !== null || (this.showCustomModal && this.customAmount > 0));
    },
    withdrawAmount() {
      return this.showCustomModal ? parseFloat(this.customAmount) : this.selectedAmount;
    }
  },
  onLoad(options) {
    this.balance = options.val;
    this.getWithdrawMethods();
  },
  methods: {
    getWithdrawMethods(){
         rider.withdrawalAccount().then(res => {
             uni.hideLoading();
          console.log('提现账号列表',res);
          if (res.code === 200) { 
            res.data.map(item=>{
                if(item.is_moren){
                    this.bank=item;
                }
            })
            this.bankList=res.data;
            
          }
        }).catch(err => {
          console.log(err);
        });
    },
    toBalanceDetail() {
      console.log("已进入余额明细页面");
      uni.navigateTo({
        url: '/pages/earnMoney/balanceDetail'
      });
    },
    selectMethod(method) {
      this.withdrawMethod = method;
      if (this.withdrawMethod === '2') {
        uni.navigateTo({
          url: '/pages/earnMoney/addbank'
        });
      } else if (this.withdrawMethod === '1') {
        uni.navigateTo({
          url: '/pages/earnMoney/addalipay'
        });
      }
    },
    selectAmount(amount) {
      this.selectedAmount = amount;
      this.showCustomModal = false;
    },
    showCustomAmountInput() {
      console.log("自定义金额输入框已显示");
      this.showCustomModal = true;
      this.customAmount = '';
    },
    confirmCustomAmount() {
      if (!this.customAmount || isNaN(this.customAmount) || this.customAmount <= 0) {
        uni.showToast({ title: '请输入有效的金额', icon: 'none' });
        return;
      }
      const amount = parseFloat(this.customAmount);
      if (amount > this.balance) {
        uni.showToast({ title: '提现金额不能超过余额', icon: 'none' });
        return;
      }
      this.selectedAmount = null;
      this.showCustomModal = false;
    },
    handleWithdraw() {
         uni.navigateTo({
          url: '/pages/earnMoney/withdrawMoney?balance=' + this.balance+'&account_id='+this.bank.id
        });
    //   if (!this.canWithdraw) return;
    //   const params = {
    //     method: this.withdrawMethod,
    //     amount: this.withdrawAmount,
    //     type: 3
    //   };
    //   console.log('提现参数:', params);
    //   this.balance -= this.withdrawAmount;
    //   uni.showToast({ title: '提现成功' });
    //   console.log('提现信息:', {
    //     method: this.withdrawMethod,
    //     amount: this.withdrawAmount,
    //     time: new Date().toLocaleString()
    //   });
    //   this.selectedAmount = null;
    //   this.showCustomModal = false;
    //   this.customAmount = '';
    },
    openBankList() {
        this.selectedBank=this.bank;
      this.showBankList = true;
    //   this.getBankList(); // 获取银行卡列表
    },
    // getBankList() {
    //   // 模拟获取银行卡列表数据
    //   this.bankList = [
    //     { id: 1, name: '工商银行', number: '尾号1111', logo: '/static/bank_logo/ICBC.png' },
    //     { id: 2, name: '建设银行', number: '尾号2222', logo: '/static/bank_logo/CCB.png' }
    //   ];
    // },
    selectBank(bank) {
      this.selectedBank = bank;
    },
    navigateToAddBank() {
      uni.navigateTo({
        url: '/pages/earnMoney/addbank'
      });
    },
    confirmBankSelection() {
      if (!this.selectedBank) {
        uni.showToast({ title: '请选择银行卡', icon: 'none' });
        return;
      }
      this.showBankList = false;
      // 更新当前选中的银行卡信息
      this.bank = this.selectedBank;
       rider.withdrawalAccountMoren({id:this.bank.id}).then(res => {
             uni.hideLoading();
         
          if (res.code === 200) { 
            uni.showToast({
              title: '设置成功',
              icon: ''
            });
            this.showBankList=false;
          }
        }).catch(err => {
          console.log(err);
        });
      
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.balance-section {
  position: relative;
  background: linear-gradient(90deg, #FF973A, #FF6A00);
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  color: white;
  text-align: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 30rpx;
  color: #333;
}

.balance-amount {
  font-size: 60rpx;
  font-weight: bold;
  margin: 20rpx 0;
}

.detail-btn {
  font-size: 28rpx;
  background: #fff;
  color: #ff0000;
  display: inline-block;
  position: absolute;
  top: 30%;
  right: 0;
  border-radius: 18rpx 0 0 18rpx;
  padding: 6rpx 16rpx;
}

.section {
  background-color: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.method-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
}

.method-info {
  display: flex;
  flex-direction: column;
}

.method-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.account {
  font-size: 24rpx;
  color: #999;
}

.method-check {
  width: 150rpx;
  color: #ff0000;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-options {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.amount-option {
  width: calc((100% - 50rpx) / 3);
  text-align: center;
  padding: 20rpx 0;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  font-size: 28rpx;
  
  &.active {
    border-color: #2979ff;
    color: #2979ff;
    background-color: #ecf5ff;
  }
}

.custom-amount {
  padding: 20rpx;
  border: 2rpx dashed #e0e0e0;
  border-radius: 10rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  
  &.active {
    border-color: #2979ff;
    color: #2979ff;
    background-color: #ecf5ff;
  }
}

.submit-btn-container {
  padding: 40rpx 30rpx 60rpx;
}

/* 确保弹框宽度固定 */
.custom-amount-popup {
  padding: 30rpx;
  width: 100%; /* 确保宽度填满父容器 */
  box-sizing: border-box; /* 包含padding在宽度内 */
}

/* 输入框容器固定样式 */
.input-container {
  width: 100%;
  position: relative;
  overflow: hidden; /* 防止内部元素影响外部布局 */
}

/* 修改u-input内部样式 */
::v-deep .u-input {
  width: 100% !important;
  padding-right: 80rpx !important; /* 为清除按钮预留空间 */
}

::v-deep .u-input__clear {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  width: 40rpx !important;
  height: 40rpx !important;
  margin: 0 !important;
}

/* 确保输入框文本不会溢出 */
::v-deep .u-input__input {
  width: calc(100% - 80rpx) !important;
  overflow: hidden;
  text-overflow: ellipsis;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.custom-amount-input {
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
  width: 100%;
  // 关键修改：固定输入框样式，防止清空按钮出现时改变宽度
  :deep(.u-input__content) {
    display: flex;
    align-items: center;
  }
  
  :deep(.u-input__content__field-wrapper) {
    flex: 1;
    display: flex;
    align-items: center;
  }
  
  :deep(.u-input__content__field-wrapper__field) {
    flex: 1;
  }
  
  :deep(.u-input__content__clear) {
    flex-shrink: 0;
    margin-left: 10rpx;
  }
}

.balance-hint {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.amount-buttons {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40rpx;
}

.quick-amount {
  padding: 15rpx 25rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.popup-footer {
  display: flex;
  justify-content: center;
}

// 添加全局样式以确保清空按钮不影响布局
::v-deep .u-input__content__clear {
  flex-shrink: 0;
  margin-left: 10rpx;
  position: absolute;
  right: 10rpx;
}
::v-deep .u-popup__content{
    width: 80%;
}
.bank-list-popup {
  padding: 30rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;

  .popup-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.bank-list {
  margin-bottom: 30rpx;
}

.bank-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 10rpx;
  margin-bottom: 20rpx;

  .bank-info {
    display: flex;
    align-items: center;

    .bank-logo {
      width: 40rpx;
      height: 40rpx;
      margin-right: 10rpx;
    }

    .bank-name {
      font-size: 28rpx;
      color: #333;
      margin-right: 10rpx;
    }

    .bank-number {
      font-size: 24rpx;
      color: #666;
    }
  }

  .bank-check {
    width: 36rpx;
    height: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.add-bank-btn {
  padding: 20rpx;
  border: 2rpx dashed #e0e0e0;
  border-radius: 10rpx;
  text-align: center;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 30rpx;
  width: 50%;
    margin-right: 10%;
}
</style>