{"id": "uni-upgrade-center", "displayName": "升级中心 uni-upgrade-center - Admin", "version": "0.6.0", "description": "uni升级中心 - 后台管理系统", "keywords": ["uniCloud", "admin", "update", "升级", "wgt"], "repository": "https://gitee.com/dcloud/uni-upgrade-center/tree/master/uni_modules/uni-upgrade-center", "engines": {"HBuilderX": "^3.3.10"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "", "type": "unicloud-admin"}, "uni_modules": {"dependencies": ["uni-data-checkbox", "uni-data-picker", "uni-dateformat", "uni-easyinput", "uni-file-picker", "uni-forms", "uni-icons", "uni-pagination", "uni-table"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}