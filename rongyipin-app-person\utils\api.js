// /**
//  * API接口管理
//  */
import request from "./request"

// 用户相关接口
export const userApi = {
  // 登录
  login: (data) => request.post("/login/login", data),
  // 获取验证码 
  Captcha: (data) => request.post("/login/phonesend", data),
  //重置密码
  resetPwd: (data) => request.post("/login/resetpwd", data),
  //充值密码校验验证码
  phoneCheck: (data) => request.post("/login/phoneCheck", data),
  //修改个人信息
  userSave: (data) => request.post("/my/userSave", data),
  //统计数据
  UserDataCount: () => request.get("/My/UserDataCount"),
  //我看过
  views: (data) => request.post("/job/views",data),
  //报名 
  addJobApply: (data) => request.post("/job/addJobApply",data),
   //报名 list
  jobApplyList: (data) => request.post("/job/jobApplyList",data),
  //已投简历列表
  JobPostionList: (data) => request.get("/my/JobPostionList",data),
  //岗位收藏列表
  collectJobList: (data) => request.post("/my/collectJobList",data),
  //积分任务
  pointTask: () => request.get("/my/pointTask"),
  //余额充值列表
  getRechargeList: () => request.get("/recharge/getRechargeList"),
  //余额充值
  balanceRecharge: (data) => request.post("/recharge/balanceRecharge",data),
  //获取系统相关参数
  getSysConfig: () => request.get("/common/getSysConfig"),
}

//选择职位接口
export const jobApi = {
  // 获取职位列表
  getJobList: () => request.get("/common/getJobType"),
  //获取搜索名称列表
  getSearchJobNameList : (params) => request.post('/job/getSearchJobNameList',params),
  //公司搜索
  searchCompany: (params) => request.post('/Company/searchCompany',params),
   //获取职位类型技能字典
  getJobTypeSkill: (params) => request.post('/common/getJobTypeSkill',params),
  //获取行业字典数据
  getCompanyIndustry :(params) => request.post('/common/getCompanyIndustry',params),
  //根据市code获取区列表
  getAreaByCityId:(params) => request.post('/common/getAreaByCityId',params),
  //获取字典数据
  getAllDictionary:(params) => request.post('/common/getAllDictionary',params),
  //岗位详情
  joblist: (params) => request.post('/job/joblist',params),
  //岗位列表
  getJobListByCompany:(params) => request.post('/job/getJobListByCompany',params),
  //添加求职意向详情
  getUserResumeList:(params) => request.post('/Resume/getUserResumeList',params),
  //编辑求职意向
  updateExpect:(params) => request.post('/Resume/updateExpect',params),
  //添加求职意向
  addExpect:(params) => request.post('/Resume/addExpect',params),
  //删除求职意向
  UserResumeDelete:(params) => request.post('/Resume/UserResumeDelete',params),
   //添加在线简历
  add:(params) => request.post('/Resume/add',params),
  //编辑在线简历
  update:(params) => request.post('/Resume/update',params),
  //技能证书三级
  cert:(params) => request.post('/my/cert',params),
  //删除在线简历
 del:(params) => request.post('/Resume/del',params),
 //浏览过岗位
 addJobView:(params) => request.post('/job/addJobView',params),
 //推荐职位列表
 recommendJobList:(params) => request.post('/job/recommendJobList',params),
  //推荐职位 被推荐人列表
 recommendJobPersonList:(params) => request.post('/job/recommendJobPersonList',params),
 //推荐职位 添加被推荐人
 addRecommendJobPerson:(params) => request.post('/job/addRecommendJobPerson',params),
}
//社区
export const communityApi = {
  //社区推荐列表
  getCommunityList:(params) => request.post('/community/getCommunityList',params),
  //社区关注列表
  myFollowArticle:(params) => request.post('/community/myFollowArticle',params),
  //社区文章详情
  getCommunityListInfo:(params) => request.post('/community/getCommunityListInfo',params),
  //社区收藏、关注 点赞
  mark:(params) => request.post('/community/mark',params),
   //社区个人信息
  user:(params) => request.post('/community/user',params),
  //我发布的社区数据
  myCommunityArticle:(params) => request.post('/community/myCommunityArticle',params),
  //他人发布的数据
  getUserCommunityArticle:(params) => request.post('/community/getUserCommunityArticle',params),
  //修改社区用户信息
  usersave:(params) => request.post('/community/usersave',params),
  //评论回复
  addComment:(params) => request.post('/community/addComment',params),
  //添加数据  发布数据
  addCommunityArticle:(params) => request.post('/community/addCommunityArticle',params),
  
}

//用户基础信息
export const userinfoApi = { 
  // 用户求职意向
  getUserResumeExpect:(params) => request.post('/Resume/getUserResumeExpect',params),
  // 用户基础信息
  userinfo:(params) => request.post('/my/userinfo',params),
  //学校字典
  getschools: () => request.get("/common/getschools"),

}
//发布页接口
export const dictApi = {
  // 获取字典数据
  getAllDictionary: () => request.get("/common/getAllDictionary"),
  //获取用户信息
  getUserInfo: () => request.get("/login/getUserInfo"),
  //确认发布接口
  postjobInsert: (data) => request.post("/job/jobInsert", data),
}
// // 地图定位接口
export const homeApi = {
    getMap: (params) => request.get("/map/ws/place/v1/suggestion", params),
    getCityLists: (params) => request.get("/map/ws/location/v1/ip", params,),
    getcitylate: () => request.get("/common/getarea"),
    getSurroun: (params)=>request.get("/map/ws/place/v1/search", params),
      //逆地址解析
    getGeocoder: (params)=>request.get("/map/ws/geocoder/v1", params)
}

export const getUserResumeExpect = (params) => request.post('/Resume/getUserResumeExpect',params)
//添加求职意向
export const addExpect = (params) => request.post('/Resume/addExpect',params)

// export const getSearchJobNameList = (params) => request.post('/job/getSearchJobNameList',params)


//企业认证接口
export const fileApi ={
  //上传接口
  postUpload: (params) => request.post("/common/upload", params),
  //获取行业字典
  getCompany: ()=>request.get("/common/getCompanyIndustry", ),
  //企业认证
  getCompanySave: (params)=>request.post("/company/companySave", params),
}

//获取用户企业基本信息
export const priseApi= {
  getCompanyInfoe: () => request.get("/company/getCompanyInfo"),
  //获取用户基本信息
  getUserInfo: () => request.get("/user/getUserInfo"),
}

//聊天相关接口
export const chatApi = {
  //获取聊天历史记录
  // getChatHistory: (params) => request.post("/chat/getChatHistory", params),
  // //发送消息
  // sendMessage: (params) => request.post("/chat/sendMessage", params),
  // //获取会话列表
  // getConversationList: () => request.get("/chat/getConversationList"),
  // //标记消息已读
  // markMessageRead: (params) => request.post("/chat/markMessageRead", params),
}

//获取企业职位列表
export const positionsApi= {
  postJobList: (params) => request.post("/job/jobList",params),
  //上架职位
  postJobStatus: (params) => request.post("/job/jobStatus",params),
  //获取详情
  postJobInfo: (params) => request.post("/job/getJobInfo",params),
  //修改接口
  postJobUpdate: (params) => request.post("/job/jobUpdate",params),
}

//已报名列表
export const applyApi= {
  //获取已报名候选人列表
  getJobApply: (params) => request.post("/talent/getJobApplyTalentList",params),
  //获取附近求职者列表
  getRecommend: (params) => request.post("/talent/getNearTalentList",params),
  //获取看过我求职者列表
  getJobView: (params) => request.post("/talent/getJobViewTalentList",params),

  //详情
  getTalent: (params) => request.post("/talent/getTalentResumeInfo",params),
  //修改已报名人才标记
  changeApply: (params) => request.post("/talent/changeApplyTalentStatus",params),
}

//已报名列表
export const realApi= { 
  //获取实名认证
  getverify: (params) => request.post("/user/verifyIdentity",params),
}

//聊天
export const chat ={
  chatList:(params)=>request.post('/message/list',params),
  //聊天列表
  chatInfo:(params)=>request.post('/message/info', params),
  //交换信息
  checkExchange: (params) => request.get('/message/checkExchange', params),
  //更新已读状态
  update: (params) => request.post('/message/update', params)
}
export const rider ={
  //骑手认证接口
  riderApply:(params)=>request.post('/my/riderApply', params),
  //查看骑手审核状态接口
  riderApplyList:(params)=>request.post('/my/riderApplyList', params),
  //提交意见反馈
  feedback:(params)=>request.post('/my/feedback', params),
  //反馈历史列表
  feedbackList:(params)=>request.post('/my/feedbackList', params),
  //申诉
  addRecommendJobAppeal:(params)=>request.post('/job/addRecommendJobAppeal', params),
  //用户等级详情
  MySingIn: () => request.get('/UserSignIn/MySingIn'),
  //经验任务
  expTask: () => request.post('/my/expTask'),
  //签到
  checkIn: () => request.get('/UserSignIn/checkIn'),
  //全部提现记录
  withdrawalLog: () => request.get('/my/withdrawalLog'),
  //我的提现记录
  withdrawalList:() => request.get('/my/withdrawalList'),
  //提交提现申请
  withdrawalApply: (params) => request.post('/my/withdrawalApply',params),
  //提现账号绑定
  withdrawalAccountAdd: (params) => request.post('/my/withdrawalAccountAdd',params),
  //提现账号列表
  withdrawalAccount:() => request.get('/my/withdrawalAccount'),
  //提现账号切换默认
  withdrawalAccountMoren: (params) => request.post('/my/withdrawalAccountMoren',params),
}
//收藏
export const collect ={
  //收藏职位列表
  collectList:(params)=>request.post('/my/collectJobList', params),
  //收藏公司列表
  collectCompanyList:(params)=>request.post('/my/collectCompanyList', params),
  //收藏职位
  collectCompany:(params)=>request.post('/my/collectCompany', params),
  collectCompanyDel:(params)=>request.post('/my/collectCompanyDel', params),
}

//附件简历
export const filelate ={
  //附件上传
  ReumeAttachmentlist:(params)=>request.post('/Resume/ReumeAttachmentlist', params),
  saveResumeAttachment:(params)=>request.post('/Resume/saveResumeAttachment', params),
  delResumeAttachment:(params)=>request.post('/Resume/delResumeAttachment', params),
}
//在线简历
export const onlineResume ={
  //个人 在线简历查询
  resume:(params)=>request.post('/Resume/resume', params),
 
}
//面试列表
export const interview ={
  //面试列表
  interview:(params)=>request.post('/my/interview', params),
  //面试详情
  InterviewInfo:(params)=>request.get('/my/InterviewInfo', params),
  //面试签到
  CheckIn:(params)=>request.get('/my/CheckIn', params),
}
//发票
export const invoice ={
  //发票订单列表
 getOrderList:()=>request.get('/invoice/getOrderList'),
 //开票记录列表
 getInvoiceList:()=>request.get('/invoice/getInvoiceList'),
 //申请开票
 applyInvoice:(params)=>request.post('/invoice/applyInvoice', params),
}
//简历展示
export const resume ={
  //简历展示
  ReumeAttachmentInfo:(params)=>request.get('/Resume/ReumeAttachmentInfo', params),
}

// // 统一导出
// export default {
//   user: userApi,
//   home: homeApi,
//   discover: discoverApi,
//   message: messageApi,
// }
