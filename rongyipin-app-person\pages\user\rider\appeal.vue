<template>
    <view class="feedback-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="申诉" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 表单内容 -->
        <view class="form-container">
            <!-- 描述输入区 -->
            <view class="form-section">
                <view class="label-row">
                    <text class="label-text">描述</text>
                    <text class="required-star">*</text>
                </view>
                <view class="textarea-container">
                    <textarea class="feedback-textarea" v-model="formData.description"
                        placeholder="请描述您遇到的相关问题，我们会尽快帮您解决（必填，10-500字内）" :maxlength="500" :show-count="true"
                        auto-height></textarea>
                    <view class="char-count">{{ formData.description.length }}/500</view>
                </view>
            </view>

            <!-- 上传图片区 -->
            <view class="form-section">
                <view class="label-row">
                    <text class="label-text">上传图片</text>
                </view>
                <view class="upload-tip">
                    <u-icon name="info-circle" size="34" color="#999"></u-icon>
                    <text class="tip-text">为更快解决您的问题，请您上传问题截图，支持：png、jpg、jpeg格式，(图片大小请控制10M)</text>
                </view>

                <view class="upload-container">
                    <!-- 已上传的图片 -->
                    <view v-for="(image, index) in uploadedImages" :key="index" class="image-item">
                        <image :src="image.url" class="uploaded-image" mode="aspectFill"></image>
                        <view class="delete-btn" @click="deleteImage(index)">
                            <u-icon name="close" size="12" color="#fff"></u-icon>
                        </view>
                    </view>

                    <!-- 上传按钮 -->
                    <view v-if="uploadedImages.length < 9" class="upload-btn" @click="chooseImage">
                        <view class="plus-icon">+</view>
                    </view>
                </view>
                <view class="upload-count">{{ uploadedImages.length }}/9</view>
            </view>

            <!-- 联系方式区 -->
            <view class="form-section">
                <view class="label-row">
                    <text class="label-text">联系方式</text>
                    <text class="required-star">*</text>
                </view>
                <view class="contact-container">
                    <input class="contact-input" :value="maskedContact" placeholder="电话号码" type="text" readonly />
                    <view class="modify-btn" @click="modifyContact">
                        <text class="modify-text">修改</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部操作区 -->
        <view class="bottom-section">
            <view class="history-btn" @click="viewHistory">
                <u-icon name="clock" size="34" color="#666"></u-icon>
                <text class="history-text">反馈历史</text>
            </view>

            <view class="submit-btn" :class="{ 'active': canSubmit }" @click="submitFeedback">
                <text class="submit-text">提交</text>
            </view>
        </view>
    </view>
</template>

<script>
import { rider } from "@/utils/api"
export default {
    data() {
        return {
            formData: {
                description: '', // 问题描述
                contact: '' // 联系方式
            },
            uploadedImages: [], // 已上传的图片
            upload: []
        }
    },
    computed: {
        // 是否可以提交
        canSubmit() {
            return this.formData.description.trim().length >= 1 &&
                this.formData.description.trim().length <= 500 &&
                this.formData.contact.trim().length > 0
        },
        maskedContact() {
            const contact = this.formData.contact;
            // 做一个简单的校验，确保 contact 是一个有效的字符串且长度足够
            if (contact && typeof contact === 'string' && contact.length === 11) {
                // 使用字符串截取方法来拼接
                // contact.substring(0, 3) -> "135"
                // contact.substring(7)    -> "5555"
                return contact.substring(0, 3) + '****' + contact.substring(7);
            }
            // 如果格式不对，直接返回原始值
            return contact;
        }
    },
    onLoad(options) {
        this.id=options.id;
        this.formData.contact = uni.getStorageSync('userInfo').telephone
        // 页面加载时的初始化
        this.initPage()
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack()
        },

        // 初始化页面
        initPage() {
            // 可以在这里获取用户的联系方式等信息
            console.log('反馈页面初始化')
        },

        // 选择图片
        async chooseImage() {
            // 1. 计算还可以选择几张图片
            const remainingCount = 9 - this.uploadedImages.length;
            if (remainingCount <= 0) {
                uni.showToast({
                    title: '最多只能上传9张图片',
                    icon: 'none'
                });
                return;
            }

            // 2. 调用 uni.chooseImage 选择图片
            uni.chooseImage({
                count: remainingCount,
                sizeType: ['compressed'], // 建议使用压缩图，上传更快
                sourceType: ['album', 'camera'],
                success: (res) => {
                    // res.tempFiles 包含了文件路径和大小，比用 uni.getFileInfo 更高效
                    const tempFiles = res.tempFiles;
                    // this.uploadedImages.push(...results);
                    // 过滤掉超过10M的图片
                    const validFiles = tempFiles.filter(file => {
                        if (file.size > 10 * 1024 * 1024) {
                            uni.showToast({
                                title: `图片 ${file.path.substring(file.path.lastIndexOf('/') + 1)} 大小超过10M`,
                                icon: 'none',
                                duration: 3000
                            });
                            return false; // 不包含这个文件
                        }
                        return true; // 保留这个文件
                    });

                    if (validFiles.length > 0) {
                        console.log('合法文件列表：', validFiles);
                        // 如果有合法文件，执行上传
                        this.uploadFiles(validFiles);
                    }
                },
                fail: (err) => {
                    // 用户取消选择等情况
                    if (err.errMsg !== 'chooseImage:fail cancel') {
                        console.error('选择图片失败:', err);
                    }
                }
            });
        },

        async uploadFiles(files) {
            // 3. 显示统一的加载提示
            uni.showLoading({
                title: `正在上传 ${files.length} 张图片...`,
                mask: true // 防止用户在上传时进行其他操作
            });
            console.log(files);
            this.uploadedImages.push({
                url: files[0].path,
                size: files[0].size
            })
            try {
                // 4. 创建一个包含所有上传任务的 Promise 数组
                const uploadPromises = files.map(file => {
                    return new Promise((resolve, reject) => {
                        // #ifdef APP-PLUS
                        uni.uploadFile({
                            url: 'http://8.130.152.121:82/jobapi/common/upload', // 替换为你自己的上传地址
                            filePath: filePath,
                            name: 'file', // 根据后端字段设定
                            formData: {
                                type: 4
                            },
                            success: (uploadFileRes) => {
                                 const data = JSON.parse(uploadFileRes.data);

                                // 根据你的接口返回格式判断是否成功
                                // 假设 code 为 0 代表成功
                                if (data.code === 200) {
                                    // resolve Promise 并返回接口中的 data 部分
                                    resolve(data.data);
                                    console.log(data.data.url)
                                    this.upload.push(data.data.url)
                                    console.log(this.upload)
                                } else {
                                    // 如果接口返回错误信息，则 reject
                                    reject(new Error(data.msg || '上传失败'));
                                }
                            },
                            fail: (err) => {
                                uni.hideLoading()
                                uni.showToast({ title: '上传失败', icon: 'none' })
                                console.error('上传失败:', err)
                            }
                        })
                        // #endif
                        uni.uploadFile({
                            // 这是你的后端接口地址
                            url: '/jobapi/common/upload', // !! 重要：请替换为你的完整接口地址
                            filePath: file.path,
                            name: 'file', // 后端接收文件的字段名，请根据你的后端API文档修改

                            // 如果需要，可以添加额外的表单数据
                            formData: {
                                type: 4
                            },

                            // 如果接口需要认证，请设置请求头

                            success: (uploadRes) => {
                                // uni.uploadFile 返回的 data 是字符串，需要解析
                                const data = JSON.parse(uploadRes.data);

                                // 根据你的接口返回格式判断是否成功
                                // 假设 code 为 0 代表成功
                                if (data.code === 200) {
                                    // resolve Promise 并返回接口中的 data 部分
                                    resolve(data.data);
                                    console.log(data.data.url)
                                    this.upload.push(data.data.url)
                                    console.log(this.upload)
                                } else {
                                    // 如果接口返回错误信息，则 reject
                                    reject(new Error(data.msg || '上传失败'));
                                }
                            },
                            fail: (err) => {
                                // 网络问题或接口调用失败
                                reject(new Error('上传接口调用失败'));
                                console.error('uni.uploadFile 失败:', err);
                            }
                        });
                    });
                });

                // 5. 使用 Promise.all 等待所有图片上传完成
                const results = await Promise.all(uploadPromises);
                console.log('所有图片上传结果:', results);
                // 6. 将所有成功返回的结果一次性添加到 uploadedImages 数组

                // 隐藏加载提示
                uni.hideLoading();
                uni.showToast({
                    title: '上传成功',
                    icon: 'success'
                });

            } catch (error) {
                // 7. 如果任何一个上传失败，则进入 catch 块
                uni.hideLoading();
                uni.showToast({
                    title: error.message || '有图片上传失败，请重试',
                    icon: 'none'
                });
                console.error('上传过程中发生错误:', error);
            }
        },

        // 删除图片
        deleteImage(index) {
            this.uploadedImages.splice(index, 1)
        },

        // 修改联系方式
        modifyContact() {
            uni.showModal({
                title: '修改联系方式',
                editable: true,
                placeholderText: '请输入手机号码',
                success: (res) => {
                    if (res.confirm && res.content) {
                        // 简单的手机号验证
                        const phoneRegex = /^1[3-9]\d{9}$/
                        if (phoneRegex.test(res.content)) {
                            this.formData.contact = res.content
                        } else {
                            uni.showToast({
                                title: '请输入正确的手机号',
                                icon: 'none'
                            })
                        }
                    }
                }
            })
        },

        // 查看反馈历史
        viewHistory() {
            console.log('查看反馈历史')
            uni.navigateTo({
                url: './feedHistory'
            })
        },

        // 提交反馈
        async submitFeedback() {
            if (!this.canSubmit) {
                uni.showToast({
                    title: '请完善必填信息',
                    icon: 'none'
                })
                return
            }

            // 显示加载状态
            uni.showLoading({
                title: '提交中...'
            })

            try {
                // 模拟提交延迟
                const params = {
                    job_person_id:this.id,
                    content: this.formData.description,
                    phone: this.formData.contact,
                    pic: this.upload
                }
                let res = await rider.addRecommendJobAppeal(params)
                console.log(res)
                if (res.code == 200) {
                    uni.showToast({
                        title: '申诉已提交',
                        icon: 'none'
                    })
                    this.resetForm();
                    uni.navigateBack();
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                }
                uni.hideLoading()
            } catch (error) {
                uni.hideLoading()
                console.error('提交反馈失败:', error)
                uni.showToast({
                    title: '提交失败，请重试',
                    icon: 'none'
                })
            }
        },

        // 重置表单
        resetForm() {
            this.formData.description = ''
            this.uploadedImages = []
            // 联系方式保留，不清空
        }
    }
}
</script>

<style lang="scss" scoped>
.feedback-page {
    min-height: 100vh;
    background: linear-gradient(180deg, #E8F8F5 0%, #F8F8F8 100%);
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
/* 表单容器 */
.form-container {
    flex: 1;
    // margin-top: 88rpx;
    padding: 30rpx;
    padding-bottom: 160rpx;
    /* 为底部按钮留出空间 */
}

/* 表单区块 */
.form-section {
    margin-bottom: 40rpx;
}

.label-row {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .label-text {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
    }

    .required-star {
        color: #ff4444;
        font-size: 28rpx;
        margin-left: 8rpx;
    }
}

/* 文本域容器 */
.textarea-container {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    position: relative;
}

.feedback-textarea {
    width: 100%;
    min-height: 200rpx;
    font-size: 26rpx;
    color: #333;
    line-height: 1.6;
    border: none;
    outline: none;
    resize: none;

    &::placeholder {
        color: #999;
        font-size: 24rpx;
    }
}

.char-count {
    position: absolute;
    bottom: 20rpx;
    right: 30rpx;
    font-size: 22rpx;
    color: #999;
}

/* 上传提示 */
.upload-tip {
    display: flex;
    align-items: flex-start;
    gap: 8rpx;
    margin-bottom: 20rpx;
    padding: 20rpx;
    background-color: #f8f9fa;
    border-radius: 12rpx;

    .tip-text {
        font-size: 22rpx;
        color: #666;
        line-height: 1.5;
        flex: 1;
    }
}

/* 上传容器 */
.upload-container {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
    margin-bottom: 10rpx;
}

.image-item {
    position: relative;
    width: 160rpx;
    height: 160rpx;
}

.uploaded-image {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
    background-color: #f5f5f5;
}

.delete-btn {
    position: absolute;
    top: -10rpx;
    right: -10rpx;
    width: 40rpx;
    height: 40rpx;
    background-color: #ff4444;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.upload-btn {
    width: 160rpx;
    height: 160rpx;
    border: 2rpx dashed #ddd;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fafafa;

    .plus-icon {
        font-size: 60rpx;
        color: #999;
        font-weight: 300;
    }
}

.upload-count {
    text-align: right;
    font-size: 22rpx;
    color: #999;
}

/* 联系方式 */
.contact-container {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 0 30rpx;
    height: 100rpx;
}

.contact-input {
    flex: 1;
    font-size: 26rpx;
    color: #333;
    border: none;
    outline: none;

    &::placeholder {
        color: #999;
    }
}

.modify-btn {
    padding: 16rpx 24rpx;
    background-color: #14B19E;
    border-radius: 30rpx;

    .modify-text {
        font-size: 24rpx;
        color: #fff;
    }
}

/* 底部操作区 */
.bottom-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
}

.history-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;

    .history-text {
        font-size: 24rpx;
        color: #666;
    }
}

.submit-btn {
    background-color: #ccc;
    border-radius: 50rpx;
    padding: 20rpx 60rpx;
    transition: all 0.3s ease;

    &.active {
        background-color: #14B19E;
    }

    .submit-text {
        font-size: 28rpx;
        color: #fff;
        font-weight: 500;
    }
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>
