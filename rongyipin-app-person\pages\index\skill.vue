<template>
	<view class="skill-select-page">
		<!-- 顶部导航 -->
		<!-- <view class="header">
			<u-navbar
				:border-bottom="true"
				left-icon="arrow-left"
				@left-click="goBack"
			></u-navbar>
		</view> -->

		<!-- 职位类型选择 -->
		<!-- <view class="job-type-section">
			<view class="section-title">
				<text class="title-text">选择职位类型</text>
				<text class="title-desc">不同职位对应不同技能</text>
			</view>
			<view class="job-type-tabs">
				<view 
					v-for="(jobType, index) in jobTypes" 
					:key="index"
					class="job-type-tab"
					:class="{ 'active': currentJobTypeIndex === index }"
					@click="selectJobType(index)"
				>
					<text class="job-type-text">{{ jobType.name }}</text>
				</view>
			</view>
		</view> -->

		<!-- 技能分类 -->
		<view class="skill-categories" v-if="currentSkillCategories.length>0">
			<view 
				v-for="(category, categoryIndex) in currentSkillCategories" 
				:key="categoryIndex"
				class="skill-category"
			>
				<!-- 分类标题 -->
				<view class="category-header">
					<text class="category-name">{{ category.name }}</text>
					<view class="selection-mode">
						<text class="mode-text">{{ category.multiple ? '多选' : '单选' }}</text>
						<u-icon 
							:name="category.multiple ? 'checkmark-circle' : 'radio-button-on'" 
							size="20" 
							:color="category.multiple ? '#19be6b' : '#02bdc4'"
						></u-icon>
					</view>
				</view>

				<!-- 技能标签列表 -->
				<view class="skills-grid">
					<view 
						v-for="(skill, skillIndex) in category.children" 
						:key="skillIndex"
						class="skill-tag"
						:class="{ 
							'selected': isSkillSelected(category.id, skill.id),
							'single-mode': !category.multiple 
						}"
						@click="selectSkill(category, skill)"
					>
						<text class="skill-text">{{ skill.name }}</text>
						<view v-if="skill.level" class="skill-level">
							<text class="level-text">{{ skill.level }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="loading" v-else>
                        <u-loading-icon></u-loading-icon>
                        加载中
                    </view>
		<!-- 已选择的技能 -->
		<!-- <view v-if="selectedSkills.length > 0" class="selected-skills">
			<view class="selected-header">
				<text class="selected-title">已选择技能 ({{ selectedSkills.length }})</text>
				<text class="clear-btn" @click="clearAllSkills">清空</text>
			</view>
			<scroll-view scroll-x class="selected-scroll">
				<view class="selected-skills-list">
					<view 
						v-for="(skill, index) in selectedSkills" 
						:key="index"
						class="selected-skill-item"
					>
						<text class="selected-skill-name">{{ skill.name }}</text>
						<view v-if="skill.level" class="selected-skill-level">
							<text class="selected-level-text">{{ skill.level }}</text>
						</view>
						<u-icon 
							name="close" 
							size="16" 
							color="#909399"
							@click="removeSkill(skill)"
						></u-icon>
					</view>
				</view>
			</scroll-view>
		</view> -->

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<view class="action-buttons">
				<u-button 
					type="default" 
					@click="resetSelection"
					custom-style="width: 30%; height: 80rpx; margin-right: 20rpx;"
				>
					重置
				</u-button>
				<u-button 
					type="primary" 
					:disabled="selectedSkills.length === 0"
					@click="confirmSelection"
					custom-style="width: 65%; height: 80rpx;"
				>
					确认选择 ({{ selectedSkills.length }})
				</u-button>
			</view>
		</view>
	</view>
</template>

<script>
import { jobApi } from "@/utils/api"
export default {
	data() {
		return {
            jobId: null,
			currentJobTypeIndex: 0, // 当前选中的职位类型
			selectedSkills: [], // 已选择的技能
			currentSkillCategories:[],
			// 职位类型数据
			jobTypes: [
				
			],
			
			// 技能分类数据
			skillCategories: {}
		}
	},
    computed: {
		// 当前职位类型对应的技能分类
		// currentSkillCategories() {
		// 	const currentJobType = this.jobTypes[this.currentJobTypeIndex];
		// 	return this.skillCategories[currentJobType.id] || [];
		// }
	},
    created() {
        // this.initDefaultCategory(job_class_id);
    },
    // onLoad(options){
    //     this.jobId = options.job_class_id;
    //     console.log(this.jobId, 'this.jobId')
    // },
	methods: {
        async initDefaultCategory(job_class_id) {
            await jobApi.getJobTypeSkill({job_class_id:job_class_id}).then((res)=>{
				uni.hideLoading();
				if(res.code == 200){
					res.data.map(item=>{
                   
                        item["multiple"] = true
                   
                })
                console.log('jineng:', res.data);
				this.currentSkillCategories = res.data
                   
                }else{
					
					uni.showToast({
						title: res.msg,
						icon: 'none'
					});
				}
                
			})
        },
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 选择职位类型
		selectJobType(index) {
			if (this.currentJobTypeIndex === index) return;

			// 切换职位类型时清空已选择的技能
			this.selectedSkills = [];
			this.currentJobTypeIndex = index;
		},

		// 选择技能
		selectSkill(category, skill) {
			const isSelected = this.isSkillSelected(category.id, skill.id);

			if (category.multiple) {
				// 多选模式
				if (isSelected) {
					this.removeSkill(skill);
				} else {
					this.addSkill(category, skill);
				}
			} else {
				// 单选模式
				if (isSelected) {
					this.removeSkill(skill);
				} else {
					// 先移除同分类下的其他技能
					this.removeSkillsByCategory(category.id);
					// 再添加当前技能
					this.addSkill(category, skill);
				}
			}
		},

		// 添加技能
		addSkill(category, skill) {
			const skillWithCategory = {
				...skill,
				categoryId: category.id,
				categoryName: category.name,
				multiple: category.multiple
			};
			this.selectedSkills.push(skillWithCategory);
		},

		// 检查技能是否已选择
		isSkillSelected(categoryId, skillId) {
			return this.selectedSkills.some(skill =>
				skill.categoryId === categoryId && skill.id === skillId
			);
		},

		// 移除技能
		removeSkill(skill) {
			const index = this.selectedSkills.findIndex(selected =>
				selected.categoryId === skill.categoryId && selected.id === skill.id
			);
			if (index > -1) {
				this.selectedSkills.splice(index, 1);
			}
		},

		// 移除指定分类下的所有技能
		removeSkillsByCategory(categoryId) {
			this.selectedSkills = this.selectedSkills.filter(skill =>
				skill.categoryId !== categoryId
			);
		},

		// 清空所有技能
		clearAllSkills() {
			this.selectedSkills = [];
		},

		// 重置选择
		resetSelection() {
			this.clearAllSkills();
			// uni.showModal({
			// 	title: '确认重置',
			// 	content: '确定要清空所有已选择的技能吗？',
			// 	success: (res) => {
			// 		if (res.confirm) {
						
			// 		}
			// 	}
			// });
		},

		// 确认选择
		confirmSelection() {
			if (this.selectedSkills.length === 0) {
				uni.showToast({
					title: '请至少选择一个技能',
					icon: 'none'
				});
				return;
			}

			// 按分类整理选择的技能
			const skillsByCategory = {};
			this.selectedSkills.forEach(skill => {
				if (!skillsByCategory[skill.categoryId]) {
					skillsByCategory[skill.categoryId] = {
						categoryName: skill.categoryName,
						multiple: skill.multiple,
						skills: []
					};
				}
				skillsByCategory[skill.categoryId].skills.push({
					id: skill.id,
					name: skill.name,
					level: skill.level
				});
			});

			// console.log('选择的技能:', {
			// 	jobType: this.jobTypes[this.currentJobTypeIndex],
			// 	skills: skillsByCategory,
			// 	totalCount: this.selectedSkills.length
			// });
			this.$emit('skilldatafunc',this.selectedSkills)
            console.log('您选择了技能标签',this.selectedSkills);
            // uni.setStorageSync('skills',this.selectedSkills)
			// uni.navigateTo({
			// 	url:'./addinfo'
			// })
			// uni.showModal({
			// 	title: '确认选择',
			// 	content: `您选择了 ${this.selectedSkills.length} 个技能标签`,
			// 	success: (res) => {
			// 		if (res.confirm) {
			// 			// 可以通过事件或者页面参数传递选择的技能数据
			// 			uni.navigateBack({
			// 				delta: 1
			// 			});
			// 		}
			// 	}
			// });
		}
	}
    }
</script>

<style lang="scss" scoped>
.skill-select-page {
	background-color: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

/* 顶部导航 */
.header {
	background-color: #fff;
	border-bottom: 1rpx solid #ebeef5;
}

/* 职位类型选择区域 */
.job-type-section {
	background-color: #fff;
	margin-bottom: 20rpx;
}

.section-title {
	padding: 30rpx 30rpx 20rpx;

	.title-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #303133;
		display: block;
		margin-bottom: 8rpx;
	}

	.title-desc {
		font-size: 24rpx;
		color: #909399;
	}
}

.job-type-tabs {
	display: flex;
	padding: 0 30rpx 30rpx;
	gap: 20rpx;
	flex-wrap: wrap;
}

.job-type-tab {
	padding: 2rpx 20rpx 8rpx;
	background-color: #f8f9fa;
	border-radius: 40rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
    line-height: 32rpx;

	&.active {
		background-color: #e3f2fd;
		border-color: #02bdc4;

		.job-type-text {
			color: #02bdc4;
			font-weight: 600;
            
		}
	}

	&:active {
		transform: scale(0.95);
	}
}

.job-type-text {
	font-size: 28rpx;
	color: #606266;
    font-size: 22rpx;
}

/* 技能分类区域 */
.skill-categories {
	height: calc(100vh - 270rpx);
	overflow-y: auto;
    margin-top: 20rpx;
	padding: 0 30rpx;
}
.loading{
	text-align: center;

}

.skill-category {
	background-color: #fff;
	border-radius: 20rpx;
	margin-bottom: 30rpx;
	padding: 30rpx;
}

.category-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30rpx;
}

.category-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #303133;
}

.selection-mode {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.mode-text {
	font-size: 24rpx;
	color: #909399;
}

/* 技能标签网格 */
.skills-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

.skill-tag {
	position: relative;
	padding: 2rpx 20rpx 4rpx;
	background-color: #f8f9fa;
	border-radius: 40rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	min-width: 120rpx;
    font-size: 22rpx;

	&.selected {
		background-color: #e3f2fd;
		border-color: #02bdc4;

		.skill-text {
			color: #02bdc4;
            font-size: 22rpx;
		}

		.skill-level {
			background-color: #02bdc4;

			.level-text {
				color: #fff;
                font-size: 20rpx;
			}
		}
	}

	&.single-mode.selected {
		background-color: #fff2e8;
		border-color: #ff9500;
        
		.skill-text {
			color: #ff9500;
            font-size: 22rpx;
		}

		.skill-level {
			background-color: #ff9500;
		}
	}

	&:active {
		transform: scale(0.95);
	}
}

.skill-text {
	font-size: 22rpx;
	color: #606266;
}

.skill-level {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	background-color: #909399;
	border-radius: 20rpx;
	padding: 2rpx 4rpx;
	min-width: 60rpx;
	text-align: center;
    font-size: 20rpx;
}

.level-text {
	font-size: 20rpx;
	color: #fff;
	transform: scale(0.9);
}

/* 已选择技能区域 */
.selected-skills {
	background-color: #fff;
	border-top: 1rpx solid #ebeef5;
	position: fixed;
	bottom: 120rpx;
	left: 0;
	right: 0;
	max-height: 200rpx;
	z-index: 100;
}

.selected-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx 10rpx;
}

.selected-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #303133;
}

.clear-btn {
	font-size: 26rpx;
	color: #02bdc4;
}

.selected-scroll {
	height: 120rpx;
}

.selected-skills-list {
	display: flex;
	padding: 0 30rpx 20rpx;
	gap: 20rpx;
	white-space: nowrap;
}

.selected-skill-item {
	position: relative;
	display: flex;
	align-items: center;
	padding: 6rpx 14rpx 12rpx 14rpx;
	background-color: #e3f2fd;
	border-radius: 30rpx;
	gap: 10rpx;
	flex-shrink: 0;
}

.selected-skill-name {
	font-size: 22rpx;
	color: #02bdc4;
	white-space: nowrap;
}

.selected-skill-level {
	background-color: #02bdc4;
	border-radius: 16rpx;
	padding: 0rpx 8rpx 6rpx;
    line-height: 30rpx;
}

.selected-level-text {
	font-size: 18rpx;
	color: #fff;
	transform: scale(0.8);
}

/* 底部操作区域 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 30rpx;
	border-top: 1rpx solid #ebeef5;
	z-index: 999;
}

.action-buttons {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
</style>
