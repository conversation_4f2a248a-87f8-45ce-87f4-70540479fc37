
var isReady=false;var onReadyCallbacks=[];
var isServiceReady=false;var onServiceReadyCallbacks=[];
var __uniConfig = {"pages":["pages/login/login","pages/login/loginres","pages/login/respassword","pages/index/index","pages/index/choseCity","pages/index/mapnear","pages/index/industry","pages/index/skill","pages/index/positionhope","pages/index/addzhiwei","pages/index/addinfo","pages/index/jobinfo","pages/index/search","pages/user/user","pages/user/userinfo","pages/community/community","pages/community/communityInfo","pages/community/searchCommunity","pages/community/publish","pages/community/publishsucceed","pages/community/mypage","pages/community/myinfo","pages/community/myname","pages/community/homepage","pages/earnMoney/earnMoney","pages/earnMoney/withdraw","pages/earnMoney/balanceDetail","pages/earnMoney/addalipay","pages/earnMoney/withdrawMoney","pages/earnMoney/addbank","pages/message/message","pages/chat/chat","pages/authentication/index","pages/authentication/positioning","pages/authentication/adress","pages/audits/index","pages/user/setting/index","pages/user/usercontont/InterviewList","pages/user/usercontont/InterviewDetail","pages/user/realName","pages/user/rider/authentication","pages/user/rider/screenshots","pages/user/rider/workcity","pages/user/workInfo","pages/user/rider/recommend","pages/user/rider/recommendList","pages/user/other/invoicing","pages/user/other/feedback","pages/user/other/collection","pages/user/other/integral","pages/user/other/application","pages/user/setting/security","pages/user/userinfo/index","pages/user/userinfo/emitPhone","pages/user/userinfo/emitWatch","pages/user/userinfo/name","pages/user/usercontont/jobApplyCount","pages/user/usercontont/collectCount","pages/user/usercontont/anAuditionCount","pages/user/usercontont/TraceList","pages/user/setting/mobilePhone","pages/user/setting/emitPassword","pages/user/rider/cationNull","pages/user/rider/recommendPerson","pages/user/rider/appeal","pages/user/rider/cationsucces","pages/user/rider/cationreso","pages/user/resume/chmentResume","pages/user/resume/vaResume","pages/user/resume/editPersonalInfo","pages/user/resume/personAdvantage","pages/user/resume/experience","pages/user/resume/projectExperience","pages/user/resume/education","pages/user/resume/skillAdvantage","pages/user/resume/certificate","pages/user/other/feedHistory","pages/user/resume/onlineResume","uni_modules/uni-feedback/pages/opendb-feedback/opendb-feedback","uni_modules/uni-id-pages/pages/userinfo/userinfo","uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify","uni_modules/uni-id-pages/pages/login/login-withoutpwd","uni_modules/uni-id-pages/pages/login/login-withpwd","uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate","uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile","uni_modules/uni-id-pages/pages/login/login-smscode","uni_modules/uni-id-pages/pages/register/register","uni_modules/uni-id-pages/pages/retrieve/retrieve","uni_modules/uni-id-pages/pages/common/webview/webview","uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd","uni_modules/uni-id-pages/pages/register/register-by-email","uni_modules/uni-id-pages/pages/retrieve/retrieve-by-email","uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd","uni_modules/uni-id-pages/pages/userinfo/cropImage/cropImage","uni_modules/uni-id-pages/pages/register/register-admin"],"window":{"navigationBarTextStyle":"black","navigationBarTitleText":"uni-starter","navigationBarBackgroundColor":"#FFFFFF","backgroundColor":"#F8F8F8","enablePullDownRefresh":false,"rpxCalcMaxDeviceWidth":375,"rpxCalcBaseDeviceWidth":375},"tabBar":{"color":"#969ca1","selectedColor":"#15CD7B","borderStyle":"black","list":[{"pagePath":"pages/index/index","text":"首页","iconPath":"static/tabbar/home.png","selectedIconPath":"static/tabbar/home-active.png"},{"pagePath":"pages/community/community","text":"社区","iconPath":"static/tabbar/square.png","selectedIconPath":"static/tabbar/square-active.png"},{"pagePath":"pages/earnMoney/earnMoney","text":"赚现金","iconPath":"static/tabbar/message.png","selectedIconPath":"static/tabbar/message-active.png"},{"pagePath":"pages/message/message","text":"消息","iconPath":"static/tabbar/message.png","selectedIconPath":"static/tabbar/message-active.png"},{"pagePath":"pages/user/user","text":"我的","iconPath":"static/tabbar/mine.png","selectedIconPath":"static/tabbar/mine-active.png"}]},"darkmode":false,"nvueCompiler":"uni-app","nvueStyleCompiler":"uni-app","renderer":"auto","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":false},"appname":"容翼聘","compilerVersion":"4.75","entryPagePath":"pages/login/login","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000}};
var __uniRoutes = [{"path":"/pages/login/login","meta":{"isQuit":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/login/loginres","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/login/respassword","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/index","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/choseCity","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/mapnear","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/industry","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/skill","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/positionhope","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/addzhiwei","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/addinfo","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/jobinfo","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/index/search","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/user/user","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/user/userinfo","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/community/community","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/community/communityInfo","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/community/searchCommunity","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/community/publish","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/community/publishsucceed","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/community/mypage","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/community/myinfo","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/community/myname","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/community/homepage","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/earnMoney/earnMoney","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/earnMoney/withdraw","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/earnMoney/balanceDetail","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/earnMoney/addalipay","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/earnMoney/withdrawMoney","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/earnMoney/addbank","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/message/message","meta":{"isQuit":true,"isTabBar":true},"window":{"navigationStyle":"custom"}},{"path":"/pages/chat/chat","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/authentication/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/authentication/positioning","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/authentication/adress","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/audits/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/setting/index","meta":{},"window":{"navigationStyle":"custom"}},{"path":"/pages/user/usercontont/InterviewList","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/usercontont/InterviewDetail","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/realName","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/authentication","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/screenshots","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/workcity","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/workInfo","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/recommend","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/recommendList","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/other/invoicing","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/other/feedback","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/other/collection","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/other/integral","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/other/application","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/setting/security","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/userinfo/index","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/userinfo/emitPhone","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/userinfo/emitWatch","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/userinfo/name","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/usercontont/jobApplyCount","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/usercontont/collectCount","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/usercontont/anAuditionCount","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/usercontont/TraceList","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/setting/mobilePhone","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/setting/emitPassword","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/cationNull","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/recommendPerson","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/appeal","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/cationsucces","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/rider/cationreso","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/chmentResume","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/vaResume","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/editPersonalInfo","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/personAdvantage","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/experience","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/projectExperience","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/education","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/skillAdvantage","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/certificate","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/other/feedHistory","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/pages/user/resume/onlineResume","meta":{},"window":{"navigationStyle":"custom","enablePullDownRefresh":false}},{"path":"/uni_modules/uni-feedback/pages/opendb-feedback/opendb-feedback","meta":{},"window":{"navigationBarTitleText":"意见反馈","enablePullDownRefresh":false}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/userinfo","meta":{},"window":{"navigationBarTitleText":"个人资料"}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/realname-verify/realname-verify","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":"实名认证"}},{"path":"/uni_modules/uni-id-pages/pages/login/login-withoutpwd","meta":{},"window":{}},{"path":"/uni_modules/uni-id-pages/pages/login/login-withpwd","meta":{},"window":{}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/deactivate/deactivate","meta":{},"window":{"navigationBarTitleText":"注销账号"}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/bind-mobile/bind-mobile","meta":{},"window":{"navigationBarTitleText":"绑定手机号码"}},{"path":"/uni_modules/uni-id-pages/pages/login/login-smscode","meta":{},"window":{"navigationBarTitleText":"手机验证码登录"}},{"path":"/uni_modules/uni-id-pages/pages/register/register","meta":{},"window":{"navigationBarTitleText":"注册"}},{"path":"/uni_modules/uni-id-pages/pages/retrieve/retrieve","meta":{},"window":{"navigationBarTitleText":"重置密码"}},{"path":"/uni_modules/uni-id-pages/pages/common/webview/webview","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":""}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/change_pwd/change_pwd","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":"修改密码"}},{"path":"/uni_modules/uni-id-pages/pages/register/register-by-email","meta":{},"window":{"navigationBarTitleText":"邮箱验证码注册"}},{"path":"/uni_modules/uni-id-pages/pages/retrieve/retrieve-by-email","meta":{},"window":{"navigationBarTitleText":"通过邮箱重置密码"}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/set-pwd/set-pwd","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":"设置密码"}},{"path":"/uni_modules/uni-id-pages/pages/userinfo/cropImage/cropImage","meta":{},"window":{}},{"path":"/uni_modules/uni-id-pages/pages/register/register-admin","meta":{},"window":{"enablePullDownRefresh":false,"navigationBarTitleText":"注册管理员账号"}}];
__uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
__uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:Math.round(f/20)})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:void 0,window:void 0,document:void 0,frames:void 0,self:void 0,location:void 0,navigator:void 0,localStorage:void 0,history:void 0,Caches:void 0,screen:void 0,alert:void 0,confirm:void 0,prompt:void 0,fetch:void 0,XMLHttpRequest:void 0,WebSocket:void 0,webkit:void 0,print:void 0}}}});
