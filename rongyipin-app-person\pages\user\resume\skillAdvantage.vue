<template>
    <view class="wechat-edit-container">
        <!-- 导航栏 -->
        <view class="navbar">
            <view class="nav-left" @click="goBack">
                <text class="nav-icon">‹</text>
            </view>
            <view class="nav-title">
                <text class="title-text">专业技能</text>
            </view>
            <view class="nav-right"></view>
        </view>

        <!-- 内容区域 -->
        <view class="content">


            <!-- 输入区域 -->
            <view class="input-section">

                <view class="input-wrapper">
                    <u--textarea v-model="postContent" placeholder="请输入。。。" count maxlength="500" height="200"></u--textarea>
                    <!-- <input class="wechat-input" type="text" v-model="wechatId" placeholder="请输入。。。" :focus="true" /> -->
                </view>
            </view>

            <!-- 底部按钮 -->
            <view class="bottom-section">
                <view class="save-btn" @click="saveWechatId">
                    <text class="btn-text">保存</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { userApi } from "@/utils/api"
export default {
    data() {
        return {
          postContent:"",
        }
    },
    onLoad() {
         // 获取用户信息
        const userInfo = uni.getStorageSync('userInfo');
        console.log("用户信息",userInfo);
        this.postContent=userInfo.skill_advantage?userInfo.skill_advantage:"";
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },

        // 保存微信号
        async saveWechatId() {
            const res = await userApi.userSave({ skill_advantage: this.postContent.trim() })
            uni.hideLoading();
            if (res.code == 200) {
                let userInfo = uni.getStorageSync('userInfo') || {};
                userInfo.skill_advantage = this.postContent.trim();
                uni.setStorageSync('userInfo', userInfo);
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                });

                // 延迟返回上一页
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            } else {
                uni.showToast({
                    title: res.msg || '保存失败，请重试',
                    icon: 'none'
                });
            }
        },

       
    }
}
</script>

<style lang="scss" scoped>
.wechat-edit-container {
    min-height: 100vh;
    background: #f8f9fa;
}

/* 导航栏样式 */
.navbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 88rpx;
    padding: 0 30rpx;
    background: #ffffff;
    border-bottom: 1rpx solid #f0f0f0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-left,
.nav-right {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-icon {
    font-size: 40rpx;
    color: #333333;
    font-weight: 500;
}

.nav-title {
    flex: 1;
    text-align: center;
}

.title-text {
    font-size: 32rpx;
    color: #333333;
    font-weight: 500;
}

.save-text {
    font-size: 28rpx;
    color: #4ECDC4;
    font-weight: 500;
}

/* 内容区域 */
.content {
    flex: 1;
    padding: 40rpx 30rpx;
}

/* 提示信息区域 */
.tip-section {
    margin-bottom: 40rpx;
    padding: 0 10rpx;
}

.tip-text {
    font-size: 26rpx;
    color: #999999;
    line-height: 36rpx;
}

/* 输入区域 */
.input-section {
    background: #ffffff;
    border-radius: 16rpx;
    padding: 40rpx 30rpx;
    margin-bottom: 40rpx;
}

.input-label {
    margin-bottom: 20rpx;
}

.label-text {
    font-size: 28rpx;
    color: #666666;
    line-height: 40rpx;
}

.input-wrapper {
    border-bottom: 1rpx solid #f0f0f0;
    padding-bottom: 20rpx;
}

.wechat-input {
    width: 100%;
    font-size: 32rpx;
    color: #333333;
    line-height: 44rpx;
    padding: 0;
    border: none;
    outline: none;
    background: transparent;
}

.wechat-input::placeholder {
    color: #cccccc;
}

/* 底部区域 */
.bottom-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 40rpx 30rpx;
    padding-bottom: calc(40rpx + env(safe-area-inset-bottom));
    background: #f8f9fa;
}

.save-btn {
    width: 100%;
    height: 88rpx;
    background: #4ECDC4;
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(78, 205, 196, 0.3);
}

.btn-text {
    font-size: 32rpx;
    color: #ffffff;
    font-weight: 500;
}

/* 响应式适配 */
@media screen and (max-width: 750rpx) {
    .navbar {
        height: 80rpx;
        padding: 0 24rpx;
    }

    .content {
        padding: 32rpx 24rpx;
    }

    .input-section {
        padding: 32rpx 24rpx;
    }

    .bottom-section {
        padding: 32rpx 24rpx;
        padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
    }
}
</style>