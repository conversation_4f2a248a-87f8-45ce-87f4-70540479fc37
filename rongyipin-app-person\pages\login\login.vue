<template>
	<view class="login-container">
		<!-- 顶部Logo和标题区域 -->
		<view class="header-section">
			<view class="logo-area">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
				<text class="app-name">荣翼聘，解锁高薪兼职</text>
			</view>
			<!-- <view class="login-title">
				<text class="title-text">{{ isPhoneLogin ? '手机号验证码登录' : '密码登录' }}</text>
			</view> -->
			<view class="subtitle" v-if="isPhoneLogin">
				<text class="subtitle-text">未注册过的手机号验证后自动登录</text>
			</view>
		</view>

		<!-- 登录表单区域 -->
		<view class="form-section">
			<!-- 第一个输入框 -->
			<view class="input-group">
				<view class="input-row">
					<text class="country-code" v-if="isPhoneLogin">+86</text>
					<view class="divider" v-if="isPhoneLogin">|</view>
					<input class="input-field" :placeholder="isPhoneLogin ? '请输入手机号码' : '请输入账号/手机号'"
						:type="isPhoneLogin ? 'number' : 'text'" v-model="phone" :maxlength="isPhoneLogin ? 11 : 20" />
				</view>
			</view>

			<!-- 第二个输入框 -->
			<view class="input-group">
				<view class="input-row">
					<!-- 验证码输入 -->
					<input v-if="isPhoneLogin" class="input-field-full" type="number" v-model="sms"
						placeholder="请输入短信验证码" maxlength="6" />
					<!-- 密码输入 -->
					<input v-else class="input-field-full" :type="showPassword ? 'text' : 'password'"
						v-model="passwords" placeholder="请输入密码" />
					<view class="divider">|</view>
					<view class="action-btn">
						<!-- 手机号登录：获取验证码 -->
						<text v-if="isPhoneLogin" @click="validateSms" class="verify-btn"
							:class="{ 'disabled': countdown > 0 }">
							{{ countdown > 0 ? `${countdown}s后重新获取` : '获取验证码' }}
						</text>
						<!-- 密码登录：小眼睛 -->
						<view v-else class="eye-btn" @click="togglePassword">
							<u-icon :name="showPassword ? 'eye' : 'eye-off'" size="40" color="#999"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 忘记密码链接 (密码登录模式) -->
			<view class="forget-password-section" v-if="!isPhoneLogin">
				<text class="forget-link" @click="goToForgetPassword">忘记密码？</text>
			</view>

			<!-- 协议勾选 -->
			<view class="agreement-section">
				<view class="checkbox-row" @click="toggleAgreement">
					<view class="checkbox" :class="{ 'checked': checkeds }">
						<u-icon v-if="checkeds" name="checkmark" size="12" color="#fff"></u-icon>
					</view>
					<text class="agreement-text">
						已阅读并同意<text class="link-text">用户协议</text>和<text class="link-text">隐私协议</text>
					</text>
				</view>
			</view>

			<!-- 登录按钮 -->
			<view class="login-btn-section">
				<view class="login-btn" @click="handleLogin">
					<text class="login-btn-text">登录</text>
				</view>
			</view>

			<!-- 底部切换登录方式 -->
			<view class="switch-section">
				<text class="switch-title">其他登录方式</text>
				<view class="switch-btn" @click="toggleLoginType">
					<view class="switch-icon">
						<u-icon v-if="isPhoneLogin" name="lock" size="20" color="#fff"></u-icon>
						<u-icon v-else name="phone" size="20" color="#fff"></u-icon>
					</view>
					<text class="switch-text">{{ isPhoneLogin ? '密码登录' : '手机号登录' }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { userApi, userinfoApi } from '@/utils/api.js'

export default {
	data() {
		return {
			phone: '',
			sms: '',
			isPhoneLogin: true, // true: 手机号登录, false: 密码登录
			checkeds: false, // 用于验证是否勾选同意协议
			passwords: '',
			showPassword: false, // 是否显示密码
			countdown: 0, // 倒计时秒数
			timer: null // 定时器
		}
	},
	watch: {
		// 监听phone变量变化，过滤中文
		phone(newVal, oldVal) {
			// 只在密码登录模式下过滤中文
			if (!this.isPhoneLogin && newVal) {
				const filteredValue = newVal.replace(/[\u4e00-\u9fa5]/g, '');
				if (newVal !== filteredValue) {
					this.phone = filteredValue;
					uni.showToast({
						title: '账号不能包含中文',
						icon: 'none',
						duration: 1500
					});
				}
			}
		}
	},
	methods: {
		// 返回上一页
		onClickLeft() {
			uni.navigateBack();
		},

		// 验证手机号格式
		validatePhone() {
			// 手机号登录模式才验证手机号格式
			if (this.isPhoneLogin && !/^1[3-9]\d{9}$/.test(this.phone)) {
				uni.showToast({
					title: '请输入正确手机号',
					icon: 'none',
					duration: 2000
				});
				return false;
			}
			return true;
		},

		// 验证账号格式（密码登录模式）
		validateAccount() {
			// 检查是否包含中文
			if (/[\u4e00-\u9fa5]/.test(this.phone)) {
				uni.showToast({
					title: '账号不能包含中文',
					icon: 'none',
					duration: 2000
				});
				return false;
			}
			// 检查是否为空
			if (!this.phone) {
				uni.showToast({
					title: '请输入账号',
					icon: 'none',
					duration: 2000
				});
				return false;
			}
			return true;
		},

		// 获取验证码
		validateSms() {
			if (!this.validatePhone()) return;
			if (this.countdown > 0) return; // 倒计时未结束不允许重复点击

			// 调用API获取验证码
			userApi.Captcha({
				event: 'joblogin',
				phone: this.phone,
			}).then(res => {
				if (res.code === 200) {
					// 开始倒计时
					this.countdown = 60;
					this.timer = setInterval(() => {
						this.countdown--;
						if (this.countdown <= 0) {
							clearInterval(this.timer);
						}
					}, 1000);
					uni.showToast({
						title: res.msg,
						icon: 'success',
						duration: 2000
					});
				} else {
					uni.showToast({
						title: res.msg || '获取验证码失败',
						icon: 'error',
						duration: 2000
					});
				}
			}).catch(err => {
				uni.showToast({
					title: '网络错误，请重试',
					icon: 'error',
					duration: 2000
				});
			});
		},

		// 验证协议是否勾选
		validateAgreement() {
			if (!this.checkeds) {
				uni.showToast({
					title: '请同意用户协议和隐私协议',
					icon: 'none',
					duration: 2000
				});
				return false;
			}
			return true;
		},

		// 切换协议勾选状态
		toggleAgreement() {
			this.checkeds = !this.checkeds;
		},

		// 切换密码显示/隐藏
		togglePassword() {
			this.showPassword = !this.showPassword;
		},
		// 处理登录
		handleLogin() {
			if (!this.validateAgreement()) {
				return;
			}

			if (this.isPhoneLogin) {
				// 手机号登录验证
				if (!this.validatePhone()) {
					return;
				}
				// 手机号验证码登录
				if (!this.sms) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				userApi.login({
					logintype: 1,
					mobile: this.phone,
					code: this.sms
				}).then(res => {
					if (res.code == 200) {
						uni.setStorageSync("token", res.data.token);
						uni.showToast({
							title: res.msg,
							icon: 'success',
							duration: 2000
						});
						userinfoApi.userinfo().then(ress => {
							if (ress.code == 200) {
								uni.setStorageSync("userInfo", ress.data);
								if (ress.data.username) {
									userinfoApi.getUserResumeExpect().then(date => {
										uni.hideLoading();
										if (date.data.length == 0) {
											uni.reLaunch({
												url: '/pages/index/addinfo?work=no',
											})
										} else {
											uni.reLaunch({
												url: '/pages/index/index',
											})
										}
									}).catch(err => {
										uni.hideLoading();
										console.log(err);
									})
									uni.hideLoading()
									// 	//   this.surrounding(res.result.location.lat, res.result.location.lng)
									// })
								} else {
									uni.hideLoading()
									uni.reLaunch({
										url: '/pages/user/userinfo',
									})
								}
							} else {
								uni.showToast({
									title: ress.msg,
									icon: 'error',
									duration: 2000
								})
							}
						})

						// setTimeout(() => {
						// 	uni.reLaunch({
						// 		url: "/pages/user/user"
						// 	})
						// }, 1000)
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'error',
							duration: 2000
						});
					}
				}).catch(() => {
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'error',
						duration: 2000
					});
				});
			} else {
				// 密码登录验证
				if (!this.validateAccount()) {
					return;
				}

				if (!this.passwords) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				userApi.login({
					logintype: 2,
					username: this.phone,
					password: this.passwords
				}).then(res => {
					if (res.code == 200) {
						uni.setStorageSync("token", res.data.token);
						uni.showToast({
							title: res.msg,
							icon: 'success',
							duration: 2000
						});
						userinfoApi.userinfo().then(ress => {
							if (ress.code == 200) {
								uni.setStorageSync("userInfo", ress.data);
								if (ress.data.username) {
									userinfoApi.getUserResumeExpect().then(date => {
										uni.hideLoading();
										if (date.data.length == 0) {
											uni.reLaunch({
												url: '/pages/index/addinfo?work=no',
											})
										} else {
											uni.reLaunch({
												url: '/pages/index/index',
											})
										}
									}).catch(err => {
										uni.hideLoading();
										console.log(err);
									})
									uni.hideLoading()
									// 	//   this.surrounding(res.result.location.lat, res.result.location.lng)
									// })
								} else {
									uni.hideLoading()
									uni.reLaunch({
										url: '/pages/user/userinfo',
									})
								}
							} else {
								uni.showToast({
									title: ress.msg,
									icon: 'error',
									duration: 2000
								})
							}
						})
					} else {
						uni.showToast({
							title: res.msg,
							// icon: 'error',
							duration: 2000
						});
					}
				}).catch(() => {
					uni.showToast({
						title: '登录失败，请重试',
						icon: 'error',
						duration: 2000
					});
				});
			}
		},

		// 切换登录方式
		toggleLoginType() {
			this.isPhoneLogin = !this.isPhoneLogin;
			// 清空输入内容
			this.phone = ''
			this.sms = '';
			this.passwords = '';
			this.showPassword = false;
			// 清除倒计时
			if (this.timer) {
				clearInterval(this.timer);
				this.countdown = 0;
			}
		},

		// 跳转到忘记密码页面
		goToForgetPassword() {
			uni.navigateTo({
				url: './loginres'
			});
		}
	},
	beforeDestroy() {
		// 清理定时器
		if (this.timer) {
			clearInterval(this.timer);
		}
	}
}
</script>

<style scoped>
.login-container {
	min-height: 100vh;
	/* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
	display: flex;
	flex-direction: column;
	position: relative;
}

/* 顶部Logo和标题区域 */
.header-section {
	padding: 120rpx 60rpx 0rpx;
	text-align: center;
	color: #333;
}

.logo-area {
	display: flex;
	align-items: center;
	justify-content: center;
	/* margin-bottom: 60rpx; */
}

.logo {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	margin-right: 20rpx;
}

.app-name {
	font-size: 36rpx;
	font-weight: bold;
	color: black;
}

.login-title {
	margin-bottom: 20rpx;
}

.title-text {
	font-size: 48rpx;
	font-weight: bold;
	color: black;
}

.subtitle {
	opacity: 0.8;
}

.subtitle-text {
	font-size: 24rpx;
	color: black;
	margin-left: 100rpx;
}

/* 登录表单区域 */
.form-section {
	flex: 1;
	background: white;
	border-radius: 40rpx 40rpx 0 0;
	padding: 80rpx 60rpx 80rpx;
	margin-top: auto;
	display: flex;
	flex-direction: column;
}

.input-group {
	margin-bottom: 40rpx;
}

.input-row {
	display: flex;
	align-items: center;
	border-bottom: 2rpx solid #f0f0f0;
	padding-bottom: 20rpx;
	min-height: 80rpx;
}

.country-code {
	font-size: 30rpx;
	color: #333;
	margin-right: 20rpx;
	min-width: 60rpx;
}

.divider {
	color: #ccc;
	margin: 0 20rpx;
	font-size: 28rpx;
}

.input-field,
.input-field-full,
.input-field-single {
	flex: 1;
	font-size: 30rpx;
	color: #333;
	border: none;
	outline: none;
	background: transparent;
}

.input-field-full {
	margin-right: 20rpx;
}

.input-field-single {
	width: 100%;
}

.action-btn {

	text-align: right;
}

.verify-btn {
	font-size: 26rpx;
	color: #00E698;
	font-weight: 500;
	/* min-width: 200rpx; */
}

.verify-btn.disabled {
	color: #ccc;
}

.forget-btn {
	font-size: 26rpx;
	color: #666;
}

.eye-btn {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

/* 忘记密码链接区域 */
.forget-password-section {
	text-align: right;
	margin-bottom: 40rpx;
}

.forget-link {
	font-size: 26rpx;
	color: #00E698;
	cursor: pointer;
}

/* 协议勾选区域 */
.agreement-section {
	margin-bottom: 60rpx;
}

.checkbox-row {
	display: flex;
	align-items: center;
}

.checkbox {
	width: 32rpx;
	height: 32rpx;
	border: 2rpx solid #ddd;
	border-radius: 50%;
	margin-right: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s;
}

.checkbox.checked {
	background-color: #00E698;
	border-color: #00E698;
}

.agreement-text {
	font-size: 24rpx;
	color: #666;
	line-height: 1.5;
}

.link-text {
	color: #00E698;
}

/* 登录按钮 */
.login-btn-section {
	margin-bottom: 40rpx;
}

.login-btn {
	background: linear-gradient(135deg, #00E698, #00D084);
	border-radius: 50rpx;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 24rpx rgba(0, 230, 152, 0.3);
}

.login-btn-text {
	font-size: 32rpx;
	color: white;
	font-weight: bold;
}

/* 底部切换登录方式 */
.switch-section {
	margin-top: auto;
	padding-top: 60rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.switch-title {
	font-size: 24rpx;
	color: #999;
	margin-bottom: 30rpx;
}

.switch-btn {
	display: flex;
	flex-direction: column;
	align-items: center;
	cursor: pointer;
}

.switch-icon {
	width: 80rpx;
	height: 80rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 16rpx;
	box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.switch-text {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}
</style>