# WebSocket 全局监听使用说明

## 概述

本项目实现了 WebSocket 全局监听功能，用于实时更新 `chat.chatList()` 接口数据。当有新的聊天消息或聊天列表发生变化时，会自动触发列表刷新。

## 架构设计

### 1. WebSocket 服务 (`utils/websocket.js`)
- 单例模式的 WebSocket 管理类
- 自动重连机制
- 事件监听器管理
- 消息解析和分发

### 2. 全局初始化 (`App.vue`)
- 应用启动时自动初始化 WebSocket 连接
- 应用显示/隐藏时的连接状态管理
- 基于用户登录状态的连接控制

### 3. 页面级监听 (`pages/message/message.vue`)
- 监听特定的 WebSocket 事件
- 自动刷新聊天列表数据
- 页面卸载时清理监听器

## 使用方法

### 1. 基本使用

```javascript
import webSocketService from '@/utils/websocket.js'

// 监听聊天列表更新
webSocketService.on('chatListUpdate', (data) => {
    console.log('聊天列表更新:', data)
    // 重新加载聊天列表
    this.loadChatList()
})

// 监听私聊消息
webSocketService.on('private_msg', (data) => {
    console.log('收到新消息:', data)
    // 处理新消息
})
```

### 2. 在页面中使用

```javascript
export default {
    onLoad() {
        // 添加监听器
        webSocketService.on('chatListUpdate', this.handleChatUpdate)
    },
    
    onUnload() {
        // 移除监听器
        webSocketService.off('chatListUpdate', this.handleChatUpdate)
    },
    
    methods: {
        handleChatUpdate(data) {
            // 处理聊天更新
            this.refreshChatList()
        }
    }
}
```

## 支持的事件类型

### 1. `chatListUpdate`
- 触发时机：聊天列表发生变化时
- 数据格式：根据后端定义
- 建议操作：重新调用 `chat.chatList()` 接口

### 2. `private_msg`
- 触发时机：收到新的私聊消息时
- 数据格式：
  ```javascript
  {
      "event": "private_msg",
      "data": {
          "to": "用户ID",
          "content": "消息内容",
          "type": "text",
          "timestamp": "时间戳"
      }
  }
  ```

### 3. `connected`
- 触发时机：WebSocket 连接成功时
- 数据格式：无
- 建议操作：可以在此时发送心跳或初始化数据

## 配置说明

### WebSocket 服务器配置
```javascript
// utils/websocket.js
this.serverUrl = 'ws://*************/'; // WebSocket 服务器地址
```

### 认证配置
- 使用 `Authorization` 头部传递 token
- Token 从 `uni.getStorageSync('token')` 获取

### 重连配置
```javascript
this.maxReconnectAttempts = 5;     // 最大重连次数
this.reconnectInterval = 3000;     // 重连间隔(毫秒)
```

## 最佳实践

### 1. 监听器管理
- 在页面 `onLoad` 时添加监听器
- 在页面 `onUnload` 时移除监听器
- 避免重复添加相同的监听器

### 2. 错误处理
- 监听器回调中要有错误处理
- 网络异常时的降级方案
- 用户友好的错误提示

### 3. 性能优化
- 避免频繁的接口调用
- 合理的防抖/节流机制
- 必要时使用本地缓存

## 故障排查

### 1. 连接失败
- 检查服务器地址是否正确
- 检查 token 是否有效
- 检查网络连接状态

### 2. 消息接收异常
- 检查消息格式是否正确
- 检查事件名称是否匹配
- 查看控制台错误日志

### 3. 自动重连失败
- 检查重连次数是否达到上限
- 检查服务器是否正常运行
- 考虑手动重新初始化连接

## 示例代码

完整的消息页面实现请参考 `pages/message/message.vue` 文件。
