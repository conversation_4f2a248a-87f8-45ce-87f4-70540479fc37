<template>
    <view class="application-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="申请开票" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 开票总额 -->
        <view class="amount-section">
            <view class="amount-card">
                <text class="amount-label">开票总额</text>
                <text class="amount-value">¥ {{totalAmount}}</text>
            </view>
        </view>

        <!-- 表单区域 -->
        <view class="form-section">
            <!-- 抬头类型 -->
            <view class="form-item">
                <text class="form-label">抬头类型</text>
                <view class="radio-group">
                    <view class="radio-item" @click="selectType('company')">
                        <view class="radio-btn" :class="{ 'active': invoiceType === 'company' }">
                            <view v-if="invoiceType === 'company'" class="radio-checked"></view>
                        </view>
                        <text class="radio-text">企业单位</text>
                    </view>
                    <view class="radio-item" @click="selectType('personal')">
                        <view class="radio-btn" :class="{ 'active': invoiceType === 'personal' }">
                            <view v-if="invoiceType === 'personal'" class="radio-checked"></view>
                        </view>
                        <text class="radio-text">个人/非企业单位</text>
                    </view>
                </view>
            </view>

            <!-- 公司抬头 -->
            <view class="form-item">
                <text class="form-label">{{invoiceType=='company'?'公司抬头':'抬头名称'}}</text>
                <view class="input-wrapper">
                    <input
                        class="form-input"
                        v-model="formData.company_name"
                        placeholder="必填"
                        placeholder-class="placeholder-style"
                    />
                    <!-- <text class="required-mark">选择</text> -->
                </view>
            </view>

            <!-- 公司税号 -->
            <view class="form-item" v-if="invoiceType=='company'">
                <text class="form-label">公司税号</text>
                <view class="input-wrapper">
                    <input
                        class="form-input"
                        v-model="formData.taxpayer_id"
                        placeholder="必填"
                        placeholder-class="placeholder-style"
                    />
                </view>
            </view>

            <!-- 公司地址 -->
            <view class="form-item" v-if="invoiceType=='company'">
                <text class="form-label">公司地址</text>
                <view class="input-wrapper">
                    <input
                        class="form-input"
                        v-model="formData.address"
                        placeholder="选填"
                        placeholder-class="placeholder-style"
                    />
                </view>
            </view>

            <!-- 公司电话 -->
            <view class="form-item" v-if="invoiceType=='company'">
                <text class="form-label">公司电话</text>
                <view class="input-wrapper">
                    <input
                        class="form-input"
                        v-model="formData.phone"
                        placeholder="选填"
                        placeholder-class="placeholder-style"
                    />
                </view>
            </view>

            <!-- 开户银行 -->
            <view class="form-item" v-if="invoiceType=='company'">
                <text class="form-label">开户银行</text>
                <view class="input-wrapper">
                    <input
                        class="form-input"
                        v-model="formData.bank_name"
                        placeholder="选填"
                        placeholder-class="placeholder-style"
                    />
                </view>
            </view>

            <!-- 开户账号 -->
            <view class="form-item" v-if="invoiceType=='company'">
                <text class="form-label">开户账号</text>
                <view class="input-wrapper">
                    <input
                        class="form-input"
                        v-model="formData.bank_account"
                        placeholder="选填"
                        placeholder-class="placeholder-style"
                    />
                </view>
            </view>

            <!-- 电子邮箱 -->
            <view class="form-item special-item">
                <text class="form-label">电子邮箱</text>
                <view class="input-wrapper">
                    <input
                        class="form-input"
                        v-model="formData.email"
                        placeholder="必填"
                        placeholder-class="placeholder-style"
                    />
                </view>
            </view>

            <!-- 手机号码 -->
            <view class="form-item">
                <text class="form-label">手机号码</text>
                <view class="input-wrapper">
                    <input
                        class="form-input"
                        v-model="formData.telephone"
                        placeholder="选填"
                        placeholder-class="placeholder-style"
                    />
                </view>
            </view>
        </view>

        <!-- 提交按钮 -->
        <view class="submit-section">
            <view class="submit-btn" @click="submitApplication">
                <text class="submit-text">提交</text>
            </view>
        </view>
    </view>
</template>

<script>
import { invoice } from '@/utils/api.js'

export default {
    data() {
        return {
            invoiceType: 'company', // 发票类型：company-企业单位，personal-个人/非企业单位
            totalAmount: 11.90, // 开票总额
            formData: {
                company_name: '', // 公司抬头
                taxpayer_id: '', // 公司税号
                address: '', // 公司地址
                phone: '', // 公司电话
                bank_name: '', // 开户银行
                bank_account: '', // 开户账号
                email: '', // 电子邮箱
                telephone: '', // 手机号码
               
            },
             invoiceList:'',
        }
    },
    onLoad(options) { 
        this.invoiceList = options.invoiceList;
        this.totalAmount=options.totalAmount;
    },
    methods: {
        // 选择发票类型
        selectType(type) {
            this.invoiceType = type
        },

        // 验证表单
        validateForm() {
            const { company_name, taxpayer_id, email, telephone } = this.formData

            // 必填项验证
            if (!company_name.trim()) {
                uni.showToast({
                    title: '请填写公司抬头',
                    icon: 'none'
                })
                return false
            }

            if (this.invoiceType === 'company'&&!taxpayer_id.trim()) {
                uni.showToast({
                    title: '请填写公司税号',
                    icon: 'none'
                })
                return false
            }

            // // 邮箱和手机号至少填写一项
            // if (!email.trim() && !telephone.trim()) {
            //     uni.showToast({
            //         title: '电子邮箱和手机号码至少填写一项',
            //         icon: 'none'
            //     })
            //     return false
            // }

            if (!email.trim()) {
                uni.showToast({
                    title: '请填写邮箱',
                    icon: 'none'
                })
                return false
            }
            // 邮箱格式验证
            if (email.trim()) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
                if (!emailRegex.test(email)) {
                    uni.showToast({
                        title: '请输入正确的邮箱格式',
                        icon: 'none'
                    })
                    return false
                }
            }

            // // 手机号格式验证
            // if (telephone.trim()) {
            //     const phoneRegex = /^1[3-9]\d{9}$/
            //     if (!phoneRegex.test(telephone)) {
            //         uni.showToast({
            //             title: '请输入正确的手机号码',
            //             icon: 'none'
            //         })
            //         return false
            //     }
            // }

            return true
        },

        // 提交申请
        async submitApplication() {
            if (!this.validateForm()) {
                return
            }
            const params = {
                order_ids:this.invoiceList,
                type:this.invoiceType=='company'?2:1,
                ...this.formData,
                price:this.totalAmount
            }
           await invoice.applyInvoice(params).then(res=>{
                uni.hideLoading();
                if(res.code==200){
                   uni.showModal({
                        title: '提交成功',
                        content: '您的开票申请已提交，我们将在3-5个工作日内处理完成',
                        showCancel: false,
                        success: () => {
                            // 返回上一页
                            uni.navigateBack()
                        }
                    })
                }else{
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                }
                 
            })
            // uni.showLoading({
            //     title: '提交中...'
            // })

            // 模拟提交过程
            // setTimeout(() => {
            //     uni.hideLoading()

            //     // 构建提交数据
            //     const submitData = {
            //         invoiceType: this.invoiceType,
            //         totalAmount: this.totalAmount,
            //         ...this.formData
            //     }

            //     console.log('提交开票申请:', submitData)

            //     uni.showModal({
            //         title: '提交成功',
            //         content: '您的开票申请已提交，我们将在3-5个工作日内处理完成',
            //         showCancel: false,
            //         success: () => {
            //             // 返回上一页
            //             uni.navigateBack()
            //         }
            //     })
            // }, 1500)
        }
    }
}
</script>

<style lang="scss" scoped>
.application-page {
    min-height: 100vh;
    background: linear-gradient(180deg, #E8F8F5 0%, #F8F8F8 100%);
    padding-bottom: 40rpx;
}

/* 开票总额 */
.amount-section {
    margin: 88rpx 15rpx 30rpx;
}

.amount-card {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.amount-label {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
}

.amount-value {
    font-size: 32rpx;
    color: #333;
    font-weight: 600;
}

/* 表单区域 */
.form-section {
    margin: 0 15rpx 30rpx;
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.form-item {
    display: flex;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }

    &.special-item {
        border-bottom: 2rpx solid #14B19E;
    }
}

.form-label {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    width: 160rpx;
    flex-shrink: 0;
}

.input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.form-input {
    flex: 1;
    font-size: 28rpx;
    color: #333;
    text-align: right;
}

.placeholder-style {
    color: #999;
    font-size: 28rpx;
}

.required-mark {
    font-size: 28rpx;
    color: #999;
    margin-left: 20rpx;
}

/* 单选按钮组 */
.radio-group {
    display: flex;
    gap: 40rpx;
    flex: 1;
    justify-content: flex-end;
}

.radio-item {
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.radio-btn {
    width: 36rpx;
    height: 36rpx;
    border: 2rpx solid #ddd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    &.active {
        border-color: #14B19E;
    }
}

.radio-checked {
    width: 20rpx;
    height: 20rpx;
    background-color: #14B19E;
    border-radius: 50%;
}

.radio-text {
    font-size: 28rpx;
    color: #333;
}

/* 提交按钮 */
.submit-section {
    margin: 0 30rpx;
    padding-bottom: 40rpx;
}

.submit-btn {
    background-color: #666;
    border-radius: 50rpx;
    padding: 24rpx;
    text-align: center;
}

.submit-text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 600;
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>
