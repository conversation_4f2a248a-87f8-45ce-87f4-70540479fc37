{"bsonType": "object", "required": ["user_id"], "properties": {"_id": {"description": "ID，系统自动生成"}, "user_id": {"bsonType": "string", "description": "用户id，参考uni-id-users表"}, "ua": {"bsonType": "string", "description": "userAgent"}, "uuid": {"bsonType": "string", "description": "设备唯一标识(需要加密存储)"}, "os_name": {"bsonType": "string", "description": "ios|android|windows|mac|linux "}, "os_version": {"bsonType": "string", "description": "操作系统版本号 "}, "os_language": {"bsonType": "string", "description": "操作系统语言 "}, "os_theme": {"bsonType": "string", "description": "操作系统主题 light|dark"}, "vendor": {"bsonType": "string", "description": "设备厂商"}, "push_clientid": {"bsonType": "string", "description": "推送设备客户端标识"}, "imei": {"bsonType": "string", "description": "国际移动设备识别码IMEI(International Mobile Equipment Identity)"}, "oaid": {"bsonType": "string", "description": "移动智能设备标识公共服务平台提供的匿名设备标识符(OAID)"}, "idfa": {"bsonType": "string", "description": "iOS平台配置应用使用广告标识(IDFA)"}, "model": {"bsonType": "string", "description": "设备型号"}, "platform": {"bsonType": "string", "description": "平台类型"}, "create_date": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}, "last_active_date": {"bsonType": "timestamp", "description": "最后登录时间"}, "last_active_ip": {"bsonType": "string", "description": "最后登录IP"}}, "version": "0.0.1"}