<template>
	<view class="page-container">
		<!-- 左上角返回箭头 -->
		<view class="nav-back" @click="goBack">
			 <u-navbar height="44px" title="认证" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed backgroundColor="#e8f9fe">
            </u-navbar>
		</view>

		<!-- 
			主内容区域 
			使用 flex: 1; 占据所有可用空间，将底部按钮推到底部
		-->
		<view class="main-content">
			<!-- 插图：哭泣的猫 -->
			<image 
				src="@/static/app/my/failure.png" 
				class="illustration" 
				mode="aspectFit"
			></image>
			
			<!-- 状态文字 -->
			<view class="status-text">
				您的审核未通过
			</view>
            
            <!-- (可选) 可以在这里显示失败原因 -->
            <view class="failure-reason">失败原因:{{ reason }}</view>
		</view>

		<!-- 底部操作区域 -->
		<view class="footer">
			<button class="submit-button" @click="reAuthenticate">重新认证</button>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			// 可以从上个页面或API获取失败原因
			// reason: '身份证照片不清晰' 
		};
	},
	onLoad(options) {
		// 从上个页面获取失败原因
		this.reason = options.reason;
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},
		// 跳转到重新认证流程
		reAuthenticate() {
			console.log("跳转到重新认证页面...");
			// 通常是跳转回认证流程的首页
			uni.redirectTo({
				url: '/pages/user/rider/authentication' 
			});
		}
	}
};
</script>

<style lang="scss" scoped>
// 页面整体容器
.page-container {
	// 关键的 flex 纵向布局，实现页脚吸底
	display: flex;
	flex-direction: column;
	
	min-height: 100vh;
	background: linear-gradient(to bottom, #e8f9fe, #f5fef3);
	position: relative;
	box-sizing: border-box;
}

// 左上角返回按钮 (样式保持统一)
.nav-back {
	position: absolute;
	top: var(--status-bar-height, 20px);
	left: 10rpx;
	padding: 20rpx;
	z-index: 10;
}
.arrow-icon {
	font-size: 44rpx;
	color: #333;
	font-weight: bold;
}

// 主要内容区域
.main-content {
	// 核心：占据所有剩余空间，将 footer 推向底部
	flex: 1;
	
	// 内部使用 flex 实现内容的垂直水平居中
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	padding: 0 40rpx;
}

// 插图样式
.illustration {
	width: 400rpx;
	height: 400rpx;
	margin-bottom: 50rpx;
}

// 状态文字样式
.status-text {
	font-size: 28rpx;
	color: #666666; // 失败状态使用中性灰色
	text-align: center;
}

// (可选) 失败原因的样式
.failure-reason {
    margin-top: 20rpx;
    font-size: 26rpx;
    color: #999;
}

// 底部固定区域 (样式保持统一)
.footer {
	padding: 20rpx 40rpx;
	// 适配 iPhone 等设备的底部安全区域
	padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	background-color: transparent;
}

// 提交按钮 (样式保持统一)
.submit-button {
	width: 100%;
	background-color: #48c9b0; // 沿用主题行动色
	color: #ffffff;
	font-size: 32rpx;
	font-weight: bold;
	border-radius: 20rpx;
	height: 96rpx;
	line-height: 96rpx;
	border: none;
	
	&::after {
		border: none;
	}
	
	&:active {
		background-color: #40b39e;
	}
}
::v-deep .u-navbar__content {
    background:  #e8f9fe !important;
}
</style>