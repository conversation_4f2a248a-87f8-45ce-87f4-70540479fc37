<template>
    <view class="page-container">
        <!-- 
			左上角返回箭头 
			设计图中的箭头带有一个小闪电图标，这通常需要使用SVG或字体图标。
			此处用一个标准的 view 模拟，您可以轻松替换为 <u-icon> 或自定义图标。
		-->
        <view class="navbar">
            <u-navbar height="44px" title="认证" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed 
                backgroundColor="#e8f9fe">
            </u-navbar>
        </view>

        <!-- 居中的内容区域 -->
        <view class="content-wrapper">
            <!-- 插图 -->
            <image src="@/static/app/my/padiing.png" class="illustration" mode="aspectFit"></image>

            <!-- 状态文字 -->
            <text class="status-text">您的骑手认证正在审核中...</text>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            // 这个页面通常是静态展示，但可以预留数据位置
            navbarStyle: {
                background: 'linear-gradient(to bottom, #e8f9fe, #f5fef3)'
            }
        };
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        }
    }
};
</script>

<style lang="scss" scoped>
// 页面整体容器
.page-container {
    // 使用 flex 布局实现垂直和水平居中，这是此布局的核心
    display: flex;
    justify-content: center;
    align-items: center;

    // 设置高度为100%视窗高度，确保居中在整个屏幕
    min-height: 100vh;

    // 设置和设计图一致的柔和渐变背景
    background: linear-gradient(to bottom, #e8f9fe, #f5fef3);

    // 设置 position: relative; 以便绝对定位子元素
    position: relative;
    box-sizing: border-box;
}

// 左上角返回按钮
.nav-back {
    position: absolute;
    // 适配不同机型的状态栏高度
    top: var(--status-bar-height, 20px);
    left: 10rpx;
    padding: 20rpx;
    // 增加点击区域大小，提升用户体验
    z-index: 10;
}

.arrow-icon {
    font-size: 44rpx;
    color: #333;
    font-weight: bold;
}

// 居中的内容包裹器
.content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

// 插图样式
.illustration {
    width: 400rpx;
    height: 400rpx;
    margin-bottom: 40rpx; // 图片和文字之间的间距
}

// 状态文字样式
.status-text {
    font-size: 28rpx;
    color: #666666;
    letter-spacing: 1rpx; // 增加轻微的字间距，提升质感
}

::v-deep .u-navbar__content {
    background:  #e8f9fe !important;
}
</style>