{"name": "容翼聘", "appid": "__UNI__E0574AE", "description": "云端一体应用快速开发基本项目模版", "versionName": "1.1.3", "versionCode": 400, "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Geolocation": {}, "Maps": {}, "Payment": {}}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>"]}, "ios": {"dSYMs": false, "privacyDescription": {"NSPhotoLibraryUsageDescription": "需要访问相册以选择文件"}}, "sdkConfigs": {"push": {"unipush": null}, "geolocation": {"tencent": {"__platform__": ["ios", "android"], "apikey_ios": "FEVBZ-S3ARJ-SKRFR-XDQ7W-BFS52-R6BFA", "apikey_android": "FEVBZ-S3ARJ-SKRFR-XDQ7W-BFS52-R6BFA"}, "system": {"__platform__": ["ios", "android"]}}, "maps": {"amap": {"name": "com.rongyipinapp.standardsdk", "appkey_ios": "80a5cc32c09b83a60cb10a8c12a6d5a3", "appkey_android": "80a5cc32c09b83a60cb10a8c12a6d5a3"}}, "payment": {"alipay": {"__platform__": ["ios", "android"]}}}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true, "optimization": {"subPackages": true}}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "2", "h5": {"devServer": {"port": 8081, "disableHostCheck": true, "proxy": {"/jobapi/map": {"target": "https://apis.map.qq.com/", "changeOrigin": true, "secure": false, "pathRewrite": {"^/jobapi/map": ""}, "headers": {"Referer": "https://apis.map.qq.com/", "User-Agent": "Mozilla/5.0"}}, "/jobapi": {"target": "http://*************:82", "changeOrigin": true, "secure": false, "pathRewrite": {"^/jobapi": "/jobapi"}}, "/socket.io": {"target": "http://*************:82", "changeOrigin": true, "secure": false, "ws": true, "pathRewrite": {"^/socket.io": ""}}}}, "sdkConfigs": {"maps": {"tencent": {"key": "SNHBZ-NNOLB-2MOUZ-NJOLJ-QYMBK-KEBKZ"}}}}}