<template>
    <view class="real-name-container">
        <!-- 标题和警告 -->
        <!-- <view class="nav-bar"> -->
            <!-- <u-navbar height="44px" bgColor="#fff" :autoBack="false" leftIcon="arrow-left" leftIconColor="#333"
                leftIconSize="28" @leftClick="goBack" fixed placeholder></u-navbar> -->
        <!-- </view> -->
        <u-navbar :autoBack="true" title="" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
        <view class="header">
            <view class="warning">
                <text class="icon">!</text>
                <text class="title">实名认证</text>
            </view>
            <text class="description">
                为了保护您的账号安全，同时根据国家法律法规要求， 需要验证您<br />
                真实的身份信息。该信息仅用于身份核验等账号保护场景，平台将严格<br />
                按照法律规定保护您的个人信息
            </text>
        </view>

        <!-- 输入框区域 -->
        <view class="form">
            <view class="form-item">
                <text class="label"><span class="required">*</span>您的姓名</text>
                <input class="input" type="text" placeholder="请输入" v-model="name" />
            </view>
            <view class="form-item">
                <text class="label"><span class="required">*</span>身份证号</text>
                <input class="input" type="text" placeholder="请输入" v-model="idCard" />
            </view>
        </view>

        <!-- 底部说明和按钮 -->
        <view class="footer">
            <view class="footer-tip">
                <text class="tip-link" @click="contactService">遇到问题？联系客服</text>
            </view>
            <button class="submit-btn" :disabled="!isFormValid" @click="submit">
                完成
            </button>
        </view>
    </view>
</template>

<script>
import { realApi } from "@/utils/api"
export default {
    data() {
        return {
            name: '',
            idCard: ''
        };
    },
    computed: {
        isFormValid() {
            return this.name.trim() !== '' && this.idCard.trim() !== '';
        }
    },
    methods: {
        goBack() {
            uni.navigateBack()
        },
        contactService() {
            // 跳转客服页面或弹窗
            uni.navigateTo({ url: '/pages/service/service' });
        },
        async submit() {
            const idCardRegex = /^(^[1-9]\d{5}(18|19|20)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}(\d|X|x)$)$/;
            if (!this.isFormValid) {
                uni.showToast({
                    title: '请填写完整信息',
                    icon: 'none'
                });
                return;
            }
            if (!idCardRegex.test(this.idCard)) {
                uni.showToast({
                    title: '身份证号格式不正确',
                    icon: 'none'
                });
                return;
            }
            const params = {
                name: this.name,
                identity: this.idCard
            }
            let res = await realApi.getverify(params)
            console.log(res, 'res')
            if (res.code == 200) {

                let userInfo = uni.getStorageSync('userInfo') || {}
                userInfo.is_real = 1 // 或你需要的值
                uni.setStorageSync('userInfo', userInfo)
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
                uni.switchTab({
                    url: '/pages/user/user'
                })
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }
        }
    }
}
</script>

<style scoped>
.real-name-container {
    background: #fff;
    /* height: 100vh; */
    padding: 0 0 40rpx 0;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.nav-bar {
    height: 100rpx;
}

.header {
    margin-top: 48rpx;
    margin-bottom: 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.warning {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 18rpx;
}

.icon {
    display: inline-block;
    width: 32rpx;
    height: 32rpx;
    background: #ff4d4f;
    color: #fff;
    border-radius: 50%;
    text-align: center;
    line-height: 32rpx;
    font-size: 26rpx;
    font-weight: bold;
    margin-right: 10rpx;
}

.title {
    font-size: 36rpx;
    font-weight: bold;
    color: #222;
}

.description {
    font-size: 25rpx;
    color: #666;
    line-height: 1.7;
    text-align: center;
    margin-top: 0;
    margin-bottom: 0;
    letter-spacing: 0.5rpx;
    padding: 0rpx 10rpx;
}

.form {
    width: 93vw;
    margin: 0 auto;
    margin-top: 32rpx;
}

.form-item {
    margin-bottom: 34rpx;
    border-bottom: 1px solid #eaeaea;
    padding-bottom: 8rpx;
    display: flex;
    align-items: center;
    margin-top: 30rpx;
}

.label {
    font-size: 26rpx;
    color: #222;
    width: 180rpx;
    flex-shrink: 0;
}

.required {
    color: #ff4d4f;
    margin-right: 4rpx;
}

.input {
    flex: 1;
    font-size: 26rpx;
    color: #333;
    border: none;
    outline: none;
    background: transparent;
    padding: 12rpx 0;
}

.input::placeholder {
    color: #bbb;
}

.footer {
    width: 100%;
    position: fixed;
    left: 0;
    bottom: 0;
    background: #fff;
    padding-bottom: env(safe-area-inset-bottom);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 10;
}

.footer-tip {
    width: 100%;
    text-align: left;
    margin: 24rpx 0 12rpx 8vw;
}

.tip-link {
    color: #02bdc6;
    font-size: 24rpx;
    text-decoration: underline;
}

.submit-btn {
    width: 90vw;
    margin: 0 auto 32rpx auto;
    padding: 22rpx 0;
    font-size: 30rpx;
    color: #fff;
    background: #bbb;
    border: none;
    border-radius: 10rpx;
    font-weight: bold;
    letter-spacing: 2rpx;
    transition: background 0.2s;
    background-color: #02bdc6;
}

.submit-btn:enabled {
    background: #02bdc6;
    color: #fff;
}

.submit-btn:disabled {
    background: #e0e0e0;
    color: #999;
}
</style>