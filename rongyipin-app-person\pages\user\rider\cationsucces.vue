<template>
    <view class="page-container">
        <!-- 左上角返回箭头 -->
        <view class="navbar">
            <u-navbar height="44px" title="认证" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed backgroundColor="#e8f9fe">
            </u-navbar>
        </view>

        <!-- 
			主内容区域 
			使用 flex: 1; 让它占据所有可用空间，从而将底部按钮推到底部
		-->
        <view class="main-content">
            <!-- 插图 -->
            <image src="@/static/app/my/succes.png" class="illustration" mode="aspectFit"></image>

            <!-- 状态文字 -->
            <view class="status-text">
                骑手身份认证通过，马上参与职位推荐领取现金奖励
            </view>
        </view>

        <!-- 底部操作区域 -->
        <view class="footer">
            <button class="submit-button" @click="goToRecommendation">去推荐</button>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            // 页面数据
        };
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack();
        },
        // 跳转到推荐页面
        goToRecommendation() {
            console.log("跳转到推荐页面...");
            // 使用 uni.navigateTo 或 uni.redirectTo 跳转到目标页面
            uni.navigateTo({
            	url: './recommend' 
            });
        }
    }
};
</script>

<style lang="scss" scoped>
// 页面整体容器
.page-container {
    // 使用 flex 纵向布局，是实现底部固定布局的关键
    display: flex;
    flex-direction: column;

    min-height: 100vh;
    background: linear-gradient(to bottom, #e8f9fe, #f5fef3);
    position: relative;
    box-sizing: border-box;
}

// 左上角返回按钮 (与上个页面样式一致)
.nav-back {
    position: absolute;
    top: var(--status-bar-height, 20px);
    left: 10rpx;
    padding: 20rpx;
    z-index: 10;
}

.arrow-icon {
    font-size: 44rpx;
    color: #333;
    font-weight: bold;
}

// 主要内容区域
.main-content {
    // 核心：占据所有剩余空间，将 footer 推向底部
    flex: 1;

    // 内部依然使用 flex 实现内容的垂直水平居中
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 40rpx; // 左右留出边距
}

// 插图样式
.illustration {
    width: 400rpx;
    height: 400rpx;
    margin-bottom: 50rpx; // 图片和文字之间的间距
}

// 状态文字样式
.status-text {
    font-size: 28rpx;
    color: #666666;
    text-align: center;
    line-height: 1.6;
    max-width: 85%; // 限制最大宽度，防止文字过长贴近屏幕边缘
}

// 底部固定区域
.footer {
    // flex-shrink: 0; // 防止在内容过多时被压缩
    padding: 20rpx 40rpx;
    // 适配 iPhone 等设备的底部安全区域
    padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
    padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
    background-color: transparent; // 背景透明，融入页面渐变色
}

// 提交按钮
.submit-button {
    width: 100%;
    background-color: #48c9b0; // 从设计图中提取的青色
    color: #ffffff;
    font-size: 32rpx;
    font-weight: bold;
    border-radius: 20rpx;
    height: 96rpx;
    line-height: 96rpx;
    border: none; // 移除默认边框

    // 移除 button 在小程序中的默认边框
    &::after {
        border: none;
    }

    // 按钮按下的效果
    &:active {
        background-color: #40b39e;
    }
}
::v-deep .u-navbar__content {
    background:  #e8f9fe !important;
}
</style>