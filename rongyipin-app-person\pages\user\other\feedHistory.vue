<template>
	<view class="feedback-history-page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<u-navbar height="44px" title="反馈历史" :autoBack="true" :leftIconSize="30" 
				:leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
			</u-navbar>
		</view>

		<!-- 内容区域 -->
		<scroll-view class="content-container" scroll-y refresher-enabled
			:refresher-triggered="loading" @refresherrefresh="onRefresh">
			<!-- 提示信息 -->
			<view class="tip-text">
				仅展示6个月内的反馈记录
			</view>

			<!-- 反馈列表 -->
			<view v-if="feedbackList.length > 0" class="feedback-list">
				<view v-for="(item, index) in feedbackList" :key="item.id" class="feedback-item" @click="viewFeedbackDetail(item)">
					<view class="feedback-content">
						<view class="title-row">
							<text class="feedback-title">{{ item.content }}</text>
							<view class="status-tag" :class="getStatusClass(item.reply)">
								<text class="status-text">{{ getStatusText(item.is_reply) }}</text>
							</view>
						</view>
						<text class="feedback-time">{{ item.create_at  }}</text>
					</view>
					<view class="feedback-arrow">
						<u-icon name="arrow-right" size="16" color="#999"></u-icon>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-else class="empty-state">
				<view class="empty-icon">
					<u-icon name="file-text" size="80" color="#E5E5E5"></u-icon>
				</view>
				<text class="empty-text">暂无反馈记录</text>
				<text class="empty-desc">您还没有提交过任何反馈</text>
			</view>
		</scroll-view>
	</view>
</template>

<script>
import { rider } from '@/utils/api.js'

export default {
	data() {
		return {
			feedbackList: [
				{
					id: 1,
					title: '描述xxxxxxxxxxxxxx',
					createTime: '2025-06-06 15:00',
					status: 'pending' // pending: 处理中, completed: 已完成, replied: 已回复
				},
				{
					id: 2,
					title: '应用闪退问题反馈',
					createTime: '2025-05-28 10:30',
					status: 'replied'
				},
				{
					id: 3,
					title: '职位搜索功能建议',
					createTime: '2025-05-15 14:20',
					status: 'completed'
				}
			],
			loading: false,
            page:1,
            size:10,
		}
	},
	onLoad() {
		this.loadFeedbackHistory()
	}, 
	methods: {
		// 加载反馈历史
		async loadFeedbackHistory() {
			try {
				uni.showLoading({
					title: '加载中...'
				})
				
				// 调用API获取反馈历史
				const response = await rider.feedbackList({page:this.page,size:this.size})
				if (response.code === 200) {
				    this.feedbackList = response.data.data || []
				}else{
                    uni.showToast({
						title: response.message,
						icon: 'none'
					})
                }
                console.log(response);
				
				// 模拟数据
				setTimeout(() => {
					uni.hideLoading()
				}, 500)
				
			} catch (error) {
				console.error('加载反馈历史失败:', error)
				uni.hideLoading()
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				})
			}
		},

		// 查看反馈详情
		viewFeedbackDetail(item) {
			console.log('查看反馈详情:', item)
			// 可以跳转到详情页面
			// uni.navigateTo({
			//     url: `/pages/user/other/feedbackDetail?id=${item.id}`
			// })
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				'0': '未处理',
				'1': '已完成',
				'replied': '已回复'
			}
			return statusMap[status] || '未知'
		},

		// 获取状态样式类
		getStatusClass(status) {
            if (status === 'replied') {
				return 'status-replied'
			}else if(0){
                return 'status-' + 'pending'
            }else if(1){
                return 'status-' + 'completed'
            }
			return `status-${status}`
		},

		// 下拉刷新
		async onRefresh() {
			this.loading = true
			try {
				await this.loadFeedbackHistory()
			} finally {
				this.loading = false
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.feedback-history-page {
	min-height: 100vh;
	background-color: #E8F8F5;
}

/* 顶部导航栏 */
.navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: #E8F8F5;
}

/* 内容容器 */
.content-container {
	height: calc(100vh - 88rpx);
	padding: 30rpx;
	box-sizing: border-box;
}

/* 提示文字 */
.tip-text {
	font-size: 28rpx;
	color: #999;
	text-align: center;
	margin-bottom: 40rpx;
}

/* 反馈列表 */
.feedback-list {
	.feedback-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
		transition: all 0.2s ease;

		.feedback-content {
			flex: 1;

			.title-row {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 12rpx;

				.feedback-title {
					font-size: 32rpx;
					color: #333;
					font-weight: 500;
					line-height: 1.4;
					flex: 1;
					margin-right: 20rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.status-tag {
					padding: 8rpx 16rpx;
					border-radius: 20rpx;
					font-size: 22rpx;

					.status-text {
						font-size: 22rpx;
						font-weight: 500;
					}

					&.status-pending {
						background-color: #FFF3E0;
						.status-text {
							color: #FF9800;
						}
					}

					&.status-completed {
						background-color: #E8F5E8;
						.status-text {
							color: #4CAF50;
						}
					}

					&.status-replied {
						background-color: #E3F2FD;
						.status-text {
							color: #2196F3;
						}
					}
				}
			}

			.feedback-time {
				font-size: 26rpx;
				color: #999;
			}
		}

		.feedback-arrow {
			margin-left: 20rpx;
		}

		&:active {
			background-color: #f8f8f8;
			transform: scale(0.98);
		}
	}
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;

	.empty-icon {
		margin-bottom: 30rpx;
	}

	.empty-text {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 16rpx;
	}

	.empty-desc {
		font-size: 28rpx;
		color: #999;
		line-height: 1.5;
	}
}
</style>
