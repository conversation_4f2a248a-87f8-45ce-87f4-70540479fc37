/**
 * PDF Canvas 渲染工具类
 * 用于在不同平台上将PDF渲染到Canvas
 */

class PDFCanvasRenderer {
    constructor(options = {}) {
        this.canvasId = options.canvasId || 'pdfCanvas'
        this.context = null
        this.pdfDoc = null
        this.currentPage = 1
        this.totalPages = 0
        this.scale = options.scale || 1
        this.pageCache = new Map() // 页面缓存
        
        // 平台检测
        this.platform = this.detectPlatform()
        
        // 初始化Canvas上下文
        this.initCanvas()
    }

    // 检测平台
    detectPlatform() {
        // #ifdef H5
        return 'h5'
        // #endif
        
        // #ifdef APP-PLUS
        return 'app'
        // #endif
        
        // #ifdef MP-WEIXIN
        return 'weixin'
        // #endif
        
        return 'unknown'
    }

    // 初始化Canvas
    initCanvas() {
        try {
            if (this.platform === 'h5') {
                // H5端直接获取Canvas元素
                // #ifdef H5
                const canvas = document.getElementById(this.canvasId)
                if (canvas) {
                    this.context = canvas.getContext('2d')
                }
                // #endif
            } else {
                // uni-app平台使用createCanvasContext
                this.context = uni.createCanvasContext(this.canvasId)
            }
        } catch (error) {
            console.error('Canvas初始化失败:', error)
        }
    }

    // 加载PDF文档
    async loadPDF(pdfUrl) {
        try {
            console.log('开始加载PDF:', pdfUrl)
            
            if (this.platform === 'h5') {
                return await this.loadPDFForH5(pdfUrl)
            } else if (this.platform === 'app') {
                return await this.loadPDFForApp(pdfUrl)
            } else if (this.platform === 'weixin') {
                return await this.loadPDFForWeixin(pdfUrl)
            }
            
            throw new Error('不支持的平台')
            
        } catch (error) {
            console.error('PDF加载失败:', error)
            throw error
        }
    }

    // H5端加载PDF
    async loadPDFForH5(pdfUrl) {
        // #ifdef H5
        try {
            // 检查PDF.js是否可用
            if (typeof pdfjsLib === 'undefined') {
                throw new Error('PDF.js库未加载')
            }

            // 设置PDF.js配置
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'

            // 加载PDF文档
            const loadingTask = pdfjsLib.getDocument({
                url: pdfUrl,
                cMapUrl: 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/cmaps/',
                cMapPacked: true
            })

            this.pdfDoc = await loadingTask.promise
            this.totalPages = this.pdfDoc.numPages

            console.log(`PDF加载成功，共${this.totalPages}页`)
            return {
                success: true,
                totalPages: this.totalPages
            }

        } catch (error) {
            console.error('H5端PDF加载失败:', error)
            throw new Error('PDF文档加载失败: ' + error.message)
        }
        // #endif
    }

    // APP端加载PDF
    async loadPDFForApp(pdfUrl) {
        // #ifdef APP-PLUS
        try {
            // APP端需要先下载PDF文件
            const downloadResult = await this.downloadFile(pdfUrl)
            
            // 由于uni-app原生不支持PDF解析，这里使用备用方案
            // 可以调用原生插件或后端API来处理PDF
            
            // 模拟PDF信息
            this.totalPages = 1
            
            // 创建一个占位页面
            await this.createPlaceholderPage(downloadResult.tempFilePath)
            
            return {
                success: true,
                totalPages: this.totalPages,
                filePath: downloadResult.tempFilePath
            }
            
        } catch (error) {
            console.error('APP端PDF加载失败:', error)
            throw new Error('PDF文档处理失败: ' + error.message)
        }
        // #endif
    }

    // 微信小程序加载PDF
    async loadPDFForWeixin(pdfUrl) {
        // #ifdef MP-WEIXIN
        try {
            // 微信小程序的限制较多，使用简化方案
            this.totalPages = 1
            
            // 创建占位页面
            await this.createPlaceholderPage(pdfUrl)
            
            return {
                success: true,
                totalPages: this.totalPages
            }
            
        } catch (error) {
            console.error('微信小程序PDF加载失败:', error)
            throw new Error('PDF文档处理失败: ' + error.message)
        }
        // #endif
    }

    // 下载文件
    downloadFile(url) {
        return new Promise((resolve, reject) => {
            uni.downloadFile({
                url: url,
                success: (res) => {
                    if (res.statusCode === 200) {
                        resolve(res)
                    } else {
                        reject(new Error('文件下载失败'))
                    }
                },
                fail: (error) => {
                    reject(new Error('文件下载失败: ' + error.errMsg))
                }
            })
        })
    }

    // 创建占位页面
    async createPlaceholderPage(filePath) {
        if (!this.context) return

        const canvas = this.getCanvasElement()
        const width = canvas ? canvas.width || 400 : 400
        const height = canvas ? canvas.height || 600 : 600

        // 设置背景
        this.context.setFillStyle('#ffffff')
        this.context.fillRect(0, 0, width, height)

        // 绘制边框
        this.context.setStrokeStyle('#e5e5e5')
        this.context.setLineWidth(2)
        this.context.strokeRect(10, 10, width - 20, height - 20)

        // 绘制PDF图标和文字
        this.context.setFillStyle('#666666')
        this.context.setFontSize(18)
        this.context.setTextAlign('center')

        const centerX = width / 2
        const centerY = height / 2

        this.context.fillText('PDF 文档', centerX, centerY - 40)
        this.context.setFontSize(14)
        this.context.fillText('点击下载查看完整内容', centerX, centerY)
        this.context.fillText(filePath ? '文件已下载到本地' : '在线文档', centerX, centerY + 30)

        // 绘制到Canvas
        if (this.platform === 'h5') {
            // H5端不需要调用draw
        } else {
            this.context.draw()
        }
    }

    // 渲染指定页面
    async renderPage(pageNum, options = {}) {
        try {
            if (!this.pdfDoc) {
                throw new Error('PDF文档未加载')
            }

            if (pageNum < 1 || pageNum > this.totalPages) {
                throw new Error('页面号超出范围')
            }

            // 检查缓存
            const cacheKey = `${pageNum}_${this.scale}`
            if (this.pageCache.has(cacheKey) && !options.forceRender) {
                console.log('使用缓存页面:', pageNum)
                return this.pageCache.get(cacheKey)
            }

            console.log('渲染页面:', pageNum)

            if (this.platform === 'h5') {
                return await this.renderPageForH5(pageNum, options)
            } else {
                return await this.renderPageForApp(pageNum, options)
            }

        } catch (error) {
            console.error('页面渲染失败:', error)
            throw error
        }
    }

    // H5端渲染页面
    async renderPageForH5(pageNum, options = {}) {
        // #ifdef H5
        try {
            const page = await this.pdfDoc.getPage(pageNum)
            const viewport = page.getViewport({ scale: this.scale })

            const canvas = this.getCanvasElement()
            if (!canvas) {
                throw new Error('Canvas元素未找到')
            }

            // 设置Canvas尺寸
            canvas.width = viewport.width
            canvas.height = viewport.height
            canvas.style.width = viewport.width + 'px'
            canvas.style.height = viewport.height + 'px'

            // 渲染页面
            const renderContext = {
                canvasContext: this.context,
                viewport: viewport
            }

            await page.render(renderContext).promise

            this.currentPage = pageNum

            // 缓存渲染结果
            const cacheKey = `${pageNum}_${this.scale}`
            this.pageCache.set(cacheKey, {
                width: viewport.width,
                height: viewport.height,
                pageNum: pageNum
            })

            return {
                success: true,
                width: viewport.width,
                height: viewport.height,
                pageNum: pageNum
            }

        } catch (error) {
            throw new Error('H5页面渲染失败: ' + error.message)
        }
        // #endif
    }

    // APP端渲染页面
    async renderPageForApp(pageNum, options = {}) {
        try {
            // APP端使用占位符
            await this.createPlaceholderPage()

            this.currentPage = pageNum

            return {
                success: true,
                width: 400,
                height: 600,
                pageNum: pageNum
            }

        } catch (error) {
            throw new Error('APP页面渲染失败: ' + error.message)
        }
    }

    // 获取Canvas元素
    getCanvasElement() {
        // #ifdef H5
        return document.getElementById(this.canvasId)
        // #endif
        
        return null
    }

    // 设置缩放比例
    setScale(scale) {
        this.scale = Math.max(0.1, Math.min(5, scale))
        this.pageCache.clear() // 清除缓存
    }

    // 获取当前页面信息
    getCurrentPageInfo() {
        return {
            currentPage: this.currentPage,
            totalPages: this.totalPages,
            scale: this.scale,
            platform: this.platform
        }
    }

    // 清理资源
    destroy() {
        this.pageCache.clear()
        this.pdfDoc = null
        this.context = null
    }
}

export default PDFCanvasRenderer
