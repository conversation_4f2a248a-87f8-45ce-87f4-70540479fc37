<template>
	<view class="online-resume-page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<u-navbar height="44px" title="在线简历" :autoBack="true" :leftIconSize="30" 
				:leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
				<template #right>
					<view class="nav-right" @click="toggleEditMode">
						<u-icon :name="isEditMode ? 'checkmark' : 'edit-pen'" size="20" color="#333"></u-icon>
						<text class="nav-text">{{ isEditMode ? '完成' : '编辑' }}</text>
					</view>
				</template>
			</u-navbar>
		</view>

		<!-- 内容区域 -->
		<scroll-view class="content-container" scroll-y>
			<!-- 个人信息 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<u-icon name="account" size="20" color="#14B19E"></u-icon>
						<text class="section-title">个人信息</text>
					</view>
					<view v-if="isEditMode" class="edit-btn" @click="editPersonalInfo">
						<u-icon name="edit-pen" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="personal-info">
					<view class="avatar-section">
						<image class="avatar" :src="resumeData.avatar || '/static/default-avatar.png'" mode="aspectFill"></image>
						<view class="basic-info">
							<text class="name">{{ resumeData.name || '姓名' }}</text>
							<text class="age-gender">{{ resumeData.age || '年龄' }} | {{ resumeData.gender || '性别' }}</text>
						</view>
					</view>
					<view class="contact-info">
						<view class="contact-item">
							<u-icon name="phone" size="16" color="#666"></u-icon>
							<text class="contact-text">{{ resumeData.phone || '手机号码' }}</text>
						</view>
						<view class="contact-item">
							<u-icon name="email" size="16" color="#666"></u-icon>
							<text class="contact-text">{{ resumeData.email || '邮箱地址' }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 求职意向 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<u-icon name="star" size="20" color="#14B19E"></u-icon>
						<text class="section-title">求职意向</text>
					</view>
					<view v-if="isEditMode" class="edit-btn" @click="editJobIntention">
						<u-icon name="edit-pen" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="job-intention">
					<view class="intention-item">
						<text class="intention-label">期望职位：</text>
						<text class="intention-value">{{ resumeData.expectedPosition || '期望职位' }}</text>
					</view>
					<view class="intention-item">
						<text class="intention-label">期望薪资：</text>
						<text class="intention-value">{{ resumeData.expectedSalary || '期望薪资' }}</text>
					</view>
					<view class="intention-item">
						<text class="intention-label">工作地点：</text>
						<text class="intention-value">{{ resumeData.workLocation || '工作地点' }}</text>
					</view>
				</view>
			</view>

			<!-- 家庭成员 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<u-icon name="home" size="20" color="#14B19E"></u-icon>
						<text class="section-title">家庭成员</text>
					</view>
					<view v-if="isEditMode" class="edit-btn" @click="editFamilyMembers">
						<u-icon name="plus" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="family-members">
					<view v-for="(member, index) in resumeData.familyMembers" :key="index" class="family-item">
						<view class="family-info">
							<text class="family-relation">{{ member.relation }}</text>
							<text class="family-name">{{ member.name }}</text>
							<text class="family-work">{{ member.work }}</text>
						</view>
						<view v-if="isEditMode" class="item-actions">
							<u-icon name="edit-pen" size="14" color="#999" @click="editFamilyMember(index)"></u-icon>
							<u-icon name="trash" size="14" color="#ff4444" @click="deleteFamilyMember(index)"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 工作经历 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<u-icon name="briefcase" size="20" color="#14B19E"></u-icon>
						<text class="section-title">工作经历</text>
					</view>
					<view v-if="isEditMode" class="edit-btn" @click="addWorkExperience">
						<u-icon name="plus" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="work-experience">
					<view v-for="(work, index) in resumeData.workExperience" :key="index" class="work-item">
						<view class="work-header">
							<view class="work-title">
								<text class="company-name">{{ work.company }}</text>
								<text class="position-name">{{ work.position }}</text>
							</view>
							<view class="work-time">{{ work.startTime }} - {{ work.endTime }}</view>
						</view>
						<view class="work-description">{{ work.description }}</view>
						<view v-if="isEditMode" class="item-actions">
							<u-icon name="edit-pen" size="14" color="#999" @click="editWorkExperience(index)"></u-icon>
							<u-icon name="trash" size="14" color="#ff4444" @click="deleteWorkExperience(index)"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 项目经历 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<u-icon name="folder" size="20" color="#14B19E"></u-icon>
						<text class="section-title">项目经历</text>
					</view>
					<view v-if="isEditMode" class="edit-btn" @click="addProjectExperience">
						<u-icon name="plus" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="project-experience">
					<view v-for="(project, index) in resumeData.projectExperience" :key="index" class="project-item">
						<view class="project-header">
							<text class="project-name">{{ project.name }}</text>
							<text class="project-time">{{ project.time }}</text>
						</view>
						<view class="project-description">{{ project.description }}</view>
						<view v-if="isEditMode" class="item-actions">
							<u-icon name="edit-pen" size="14" color="#999" @click="editProjectExperience(index)"></u-icon>
							<u-icon name="trash" size="14" color="#ff4444" @click="deleteProjectExperience(index)"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 教育经历 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<u-icon name="school" size="20" color="#14B19E"></u-icon>
						<text class="section-title">教育经历</text>
					</view>
					<view v-if="isEditMode" class="edit-btn" @click="addEducation">
						<u-icon name="plus" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="education">
					<view v-for="(edu, index) in resumeData.education" :key="index" class="education-item">
						<view class="education-header">
							<view class="education-info">
								<text class="school-name">{{ edu.school }}</text>
								<text class="major-name">{{ edu.major }}</text>
							</view>
							<text class="education-time">{{ edu.time }}</text>
						</view>
						<view v-if="isEditMode" class="item-actions">
							<u-icon name="edit-pen" size="14" color="#999" @click="editEducation(index)"></u-icon>
							<u-icon name="trash" size="14" color="#ff4444" @click="deleteEducation(index)"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 获奖经历 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<u-icon name="medal" size="20" color="#14B19E"></u-icon>
						<text class="section-title">获奖经历</text>
					</view>
					<view v-if="isEditMode" class="edit-btn" @click="addAward">
						<u-icon name="plus" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="awards">
					<view v-for="(award, index) in resumeData.awards" :key="index" class="award-item">
						<view class="award-header">
							<text class="award-name">{{ award.name }}</text>
							<text class="award-time">{{ award.time }}</text>
						</view>
						<view class="award-description">{{ award.description }}</view>
						<view v-if="isEditMode" class="item-actions">
							<u-icon name="edit-pen" size="14" color="#999" @click="editAward(index)"></u-icon>
							<u-icon name="trash" size="14" color="#ff4444" @click="deleteAward(index)"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 专业技能 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<u-icon name="setting" size="20" color="#14B19E"></u-icon>
						<text class="section-title">专业技能</text>
					</view>
					<view v-if="isEditMode" class="edit-btn" @click="editSkills">
						<u-icon name="edit-pen" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="skills">
					<view v-for="(skill, index) in resumeData.skills" :key="index" class="skill-item">
						<text class="skill-name">{{ skill.name }}</text>
						<view class="skill-level">
							<view v-for="i in 5" :key="i" class="skill-dot" :class="{ active: i <= skill.level }"></view>
						</view>
					</view>
				</view>
			</view>

			<!-- 兴趣爱好 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<u-icon name="heart" size="20" color="#14B19E"></u-icon>
						<text class="section-title">兴趣爱好</text>
					</view>
					<view v-if="isEditMode" class="edit-btn" @click="editHobbies">
						<u-icon name="edit-pen" size="16" color="#999"></u-icon>
					</view>
				</view>
				<view class="hobbies">
					<view v-for="(hobby, index) in resumeData.hobbies" :key="index" class="hobby-tag">
						<text class="hobby-text">{{ hobby }}</text>
						<u-icon v-if="isEditMode" name="close" size="12" color="#999" @click="deleteHobby(index)"></u-icon>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			isEditMode: false,
			resumeData: {
				avatar: '',
				name: '张三',
				age: '25',
				gender: '男',
				phone: '138****8888',
				email: '<EMAIL>',
				expectedPosition: '前端开发工程师',
				expectedSalary: '8K-12K',
				workLocation: '北京市',
				familyMembers: [
					{ relation: '父亲', name: '张XX', work: '教师' },
					{ relation: '母亲', name: '李XX', work: '医生' }
				],
				workExperience: [
					{
						company: 'ABC科技有限公司',
						position: '前端开发工程师',
						startTime: '2022-03',
						endTime: '至今',
						description: '负责公司前端项目开发，参与产品需求分析，完成页面开发和优化工作。'
					}
				],
				projectExperience: [
					{
						name: '电商管理系统',
						time: '2022-06 至 2022-12',
						description: '负责电商后台管理系统的前端开发，使用Vue.js技术栈。'
					}
				],
				education: [
					{
						school: '北京大学',
						major: '计算机科学与技术',
						time: '2018-09 至 2022-06'
					}
				],
				awards: [
					{
						name: '优秀员工奖',
						time: '2023-12',
						description: '年度优秀员工，工作表现突出'
					}
				],
				skills: [
					{ name: 'JavaScript', level: 4 },
					{ name: 'Vue.js', level: 4 },
					{ name: 'React', level: 3 },
					{ name: 'Node.js', level: 3 }
				],
				hobbies: ['阅读', '运动', '旅行', '摄影']
			}
		}
	},
	onLoad() {
		this.loadResumeData()
	},
	methods: {
		// 切换编辑模式
		toggleEditMode() {
			this.isEditMode = !this.isEditMode
			if (!this.isEditMode) {
				// 退出编辑模式时保存数据
				this.saveResumeData()
			}
		},

		// 加载简历数据
		async loadResumeData() {
			try {
				// TODO: 调用API获取简历数据
				// const response = await this.$api.getResumeData()
				// this.resumeData = response.data
				console.log('加载简历数据')
			} catch (error) {
				console.error('加载简历数据失败:', error)
			}
		},

		// 保存简历数据
		async saveResumeData() {
			try {
				uni.showLoading({ title: '保存中...' })
				// TODO: 调用API保存简历数据
				// await this.$api.saveResumeData(this.resumeData)
				uni.hideLoading()
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})
			} catch (error) {
				uni.hideLoading()
				console.error('保存简历数据失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			}
		},

		// 编辑个人信息
		editPersonalInfo() {
			uni.navigateTo({
				url: '/pages/user/resume/editPersonalInfo'
			})
		},

		// 编辑求职意向
		editJobIntention() {
			uni.navigateTo({
				url: '/pages/user/resume/editJobIntention'
			})
		},

		// 编辑家庭成员
		editFamilyMembers() {
			this.showEditModal('家庭成员', 'familyMembers')
		},

		// 编辑家庭成员
		editFamilyMember(index) {
			console.log('编辑家庭成员:', index)
		},

		// 删除家庭成员
		deleteFamilyMember(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个家庭成员吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.familyMembers.splice(index, 1)
					}
				}
			})
		},

		// 添加工作经历
		addWorkExperience() {
			console.log('添加工作经历')
		},

		// 编辑工作经历
		editWorkExperience(index) {
			console.log('编辑工作经历:', index)
		},

		// 删除工作经历
		deleteWorkExperience(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条工作经历吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.workExperience.splice(index, 1)
					}
				}
			})
		},

		// 添加项目经历
		addProjectExperience() {
			console.log('添加项目经历')
		},

		// 编辑项目经历
		editProjectExperience(index) {
			console.log('编辑项目经历:', index)
		},

		// 删除项目经历
		deleteProjectExperience(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个项目经历吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.projectExperience.splice(index, 1)
					}
				}
			})
		},

		// 添加教育经历
		addEducation() {
			console.log('添加教育经历')
		},

		// 编辑教育经历
		editEducation(index) {
			console.log('编辑教育经历:', index)
		},

		// 删除教育经历
		deleteEducation(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条教育经历吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.education.splice(index, 1)
					}
				}
			})
		},

		// 添加获奖经历
		addAward() {
			console.log('添加获奖经历')
		},

		// 编辑获奖经历
		editAward(index) {
			console.log('编辑获奖经历:', index)
		},

		// 删除获奖经历
		deleteAward(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个获奖经历吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.awards.splice(index, 1)
					}
				}
			})
		},

		// 编辑专业技能
		editSkills() {
			console.log('编辑专业技能')
		},

		// 编辑兴趣爱好
		editHobbies() {
			console.log('编辑兴趣爱好')
		},

		// 删除兴趣爱好
		deleteHobby(index) {
			this.resumeData.hobbies.splice(index, 1)
		}
	}
}
</script>

<style lang="scss" scoped>
.online-resume-page {
	min-height: 100vh;
	background-color: #f8f8f8;
}

/* 顶部导航栏 */
.navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: #fff;
}

.nav-right {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.nav-text {
	font-size: 28rpx;
	color: #333;
}

/* 内容容器 */
.content-container {
	height: calc(100vh - 88rpx);
	padding: 30rpx;
	box-sizing: border-box;
}

/* 简历模块 */
.resume-section {
	background: white;
	border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.section-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background: linear-gradient(135deg, #f8fffe 0%, #f0fdfb 100%);
}

.header-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 600;
	color: #333;
}

.edit-btn {
	padding: 8rpx;
	border-radius: 8rpx;
	background: rgba(20, 177, 158, 0.1);
}

/* 个人信息 */
.personal-info {
	padding: 30rpx;
}

.avatar-section {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 30rpx;
	background: #f5f5f5;
}

.basic-info {
	flex: 1;
}

.name {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 12rpx;
}

.age-gender {
	font-size: 28rpx;
	color: #666;
}

.contact-info {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.contact-text {
	font-size: 28rpx;
	color: #666;
}

/* 求职意向 */
.job-intention {
	padding: 30rpx;
}

.intention-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.intention-label {
	font-size: 28rpx;
	color: #666;
	width: 160rpx;
}

.intention-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}

/* 家庭成员 */
.family-members {
	padding: 30rpx;
}

.family-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.family-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex: 1;
}

.family-relation {
	font-size: 26rpx;
	color: #14B19E;
	background: rgba(20, 177, 158, 0.1);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	min-width: 80rpx;
	text-align: center;
}

.family-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.family-work {
	font-size: 26rpx;
	color: #666;
}

.item-actions {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

/* 工作经历 */
.work-experience {
	padding: 30rpx;
}

.work-item {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.work-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.work-title {
	flex: 1;
}

.company-name {
	display: block;
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.position-name {
	font-size: 26rpx;
	color: #14B19E;
}

.work-time {
	font-size: 24rpx;
	color: #999;
	white-space: nowrap;
}

.work-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 项目经历 */
.project-experience {
	padding: 30rpx;
}

.project-item {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.project-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.project-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.project-time {
	font-size: 24rpx;
	color: #999;
}

.project-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 教育经历 */
.education {
	padding: 30rpx;
}

.education-item {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.education-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.education-info {
	flex: 1;
}

.school-name {
	display: block;
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.major-name {
	font-size: 26rpx;
	color: #14B19E;
}

.education-time {
	font-size: 24rpx;
	color: #999;
}

/* 获奖经历 */
.awards {
	padding: 30rpx;
}

.award-item {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.award-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.award-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.award-time {
	font-size: 24rpx;
	color: #999;
}

.award-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 专业技能 */
.skills {
	padding: 30rpx;
}

.skill-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.skill-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.skill-level {
	display: flex;
	gap: 8rpx;
}

.skill-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: #e0e0e0;

	&.active {
		background: #14B19E;
	}
}

/* 兴趣爱好 */
.hobbies {
	padding: 30rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.hobby-tag {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: rgba(20, 177, 158, 0.1);
	color: #14B19E;
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
}

.hobby-text {
	font-size: 26rpx;
	color: #14B19E;
}
</style>
