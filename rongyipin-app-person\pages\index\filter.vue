<template>
  <view class="boss-filter-page">
    <!-- 顶部导航栏 -->
    <!-- <u-navbar
       @leftClick="handleBack"
      title="筛选"
      :border-bottom="false"
      title-color="#333"
      back-icon-color="#333"
      :background="{ backgroundColor: '#fff' }"
    >
      <view slot="right" class="navbar-right" @click="handleConfirm">
        确定
      </view>
    </u-navbar> -->

    <!-- 筛选主体 -->
    <view class="filter-container">
      <!-- 左侧一级分类 -->
      <scroll-view 
        scroll-y 
        class="primary-categories" 
        :scroll-top="primaryScrollTop"
        scroll-with-animation
      >
        <view 
          v-for="(category, index) in categories" 
          :key="category.name"
          class="category-item"
          :class="{ active: activeCategoryIndex === index }"
          @click="switchCategory(index)"
        >
          {{ category.desc }}
          <u-badge 
            v-if="getSelectedCount(category) > 0" 
            :count="getSelectedCount(category)" 
            :offset="[-10, -5]" 
          />
        </view>
      </scroll-view>

      <!-- 右侧二级选项 -->
      <scroll-view 
        scroll-y 
        class="secondary-options" 
        :scroll-top="secondaryScrollTop"
        scroll-with-animation
        @scroll="handleSecondaryScroll"
      >
        <template v-for="(category, cIndex) in categories">
            
          <view 
            v-if="category.data && category.data.length" 
            :key="'category-'+category.name"
            :id="'category-'+cIndex"
            class="option-category"
          >
            <view class="option-category-title">{{category.desc}}</view>
            <!-- 单选类型 -->
            <!-- <template v-if="category.type === 'radio'"> -->
                
              <view 
                v-for="option in category.data" 
                :key="option.id"
                class="option-item"
                :class="{ selected: isOptionSelected(category, option) }"
                @click="selectOption(category, option)"
              >
                {{ option.name }}
                <!-- <u-icon 
                  v-if="isOptionSelected(category, option)" 
                  name="checkbox-mark" 
                  color="#2979ff" 
                  size="20"
                /> -->
              </view>
            <!-- </template> -->

            <!-- 多选类型 -->
            <!-- <template v-else-if="category.type === 'checkbox'">
              <view 
                v-for="option in category.data" 
                :key="option.id"
                class="option-item"
                :class="{ selected: isOptionSelected(category, option) }"
                @click="selectOption(category, option)"
              >
                {{ option.name }} -->
                <!-- <u-icon 
                  v-if="isOptionSelected(category, option)" 
                  name="checkbox-mark" 
                  color="#2979ff" 
                  size="20"
                /> -->
              <!-- </view>
            </template> -->
          </view>
        </template>

       
      </scroll-view>
    </view>
     <!-- 底部操作按钮 -->
    <view class="action-buttons">
      <u-button 
        type="primary" 
        size="medium" 
        shape="circle" 
        @click="resetCurrentCategory"
      >
        重置
      </u-button>
      <u-button 
        type="success" 
        size="medium" 
        shape="circle" 
        @click="handleConfirm"
        custom-style="margin-left: 15px;"
      >
        确定
      </u-button>
    </view>
  </view>
</template>

<script>
import {dictApi} from '@/utils/api'
export default {
  data() {
    return {
      activeCategoryIndex: 0, // 当前激活的一级分类索引
      primaryScrollTop: 0,    // 左侧滚动位置
      secondaryScrollTop: 0,  // 右侧滚动位置
      optionPositions: [],    // 记录每个分类选项的位置
      scrollTimer: null,     // 滚动节流定时器
      categories: [
        {
          id: 1,
          name: '地区',
          type: 'radio',
          options: [
            { id: 11, name: '全国' },
            { id: 12, name: '北京' },
            { id: 13, name: '上海' },
            { id: 14, name: '广州' },
            { id: 15, name: '深圳' },
            { id: 16, name: '杭州' },
            { id: 17, name: '成都' },
            { id: 18, name: '武汉' }
          ],
          selected: 11
        },
        {
          id: 2,
          name: '薪资',
          type: 'radio',
          options: [
            { id: 21, name: '不限' },
            { id: 22, name: '3k以下' },
            { id: 23, name: '3-5k' },
            { id: 24, name: '5-10k' },
            { id: 25, name: '10-15k' },
            { id: 26, name: '15-20k' },
            { id: 27, name: '20k以上' }
          ],
          selected: null
        },
        {
          id: 3,
          name: '经验',
          type: 'checkbox',
          options: [
            { id: 31, name: '不限' },
            { id: 32, name: '在校生' },
            { id: 33, name: '应届生' },
            { id: 34, name: '1年以内' },
            { id: 35, name: '1-3年' },
            { id: 36, name: '3-5年' },
            { id: 37, name: '5-10年' },
            { id: 38, name: '10年以上' }
          ],
          selected: []
        },
        {
          id: 4,
          name: '学历',
          type: 'radio',
          options: [
            { id: 41, name: '不限' },
            { id: 42, name: '大专' },
            { id: 43, name: '本科' },
            { id: 44, name: '硕士' },
            { id: 45, name: '博士' }
          ],
          selected: null
        },
        {
          id: 5,
          name: '行业',
          type: 'checkbox',
          options: [
            { id: 51, name: '互联网' },
            { id: 52, name: '金融' },
            { id: 53, name: '教育' },
            { id: 54, name: '医疗' },
            { id: 55, name: '房地产' },
            { id: 56, name: '制造业' },
            { id: 57, name: '服务业' },
            { id: 58, name: '文化娱乐' }
          ],
          selected: []
        },
        {
          id: 6,
          name: '公司规模',
          type: 'checkbox',
          options: [
            { id: 61, name: '0-20人' },
            { id: 62, name: '20-99人' },
            { id: 63, name: '100-499人' },
            { id: 64, name: '500-999人' },
            { id: 65, name: '1000-9999人' },
            { id: 66, name: '10000人以上' }
          ],
          selected: []
        },
        {
          id: 7,
          name: '融资阶段',
          type: 'radio',
          options: [
            { id: 71, name: '不限' },
            { id: 72, name: '未融资' },
            { id: 73, name: '天使轮' },
            { id: 74, name: 'A轮' },
            { id: 75, name: 'B轮' },
            { id: 76, name: 'C轮' },
            { id: 77, name: 'D轮及以上' },
            { id: 78, name: '已上市' }
          ],
          selected: null
        }
      ]
    };
  },
  
  mounted() {
    this.$nextTick(() => {
      this.calculateOptionPositions();
    });
  },
//   onLoad() {
//     this.filterData();
//   },
  
  methods: {
    
    //请求筛选数据
    async filterData(filter){
        console.log("开始请求数据")
        
         await dictApi.getAllDictionary().then(res=>{
            uni.hideLoading();
            if(res.code==200){
                 this.categories=[
                    {...res.data.where_salary,selected:filter.where_salary?filter.where_salary:0},
                    {...res.data.job_edu,selected:filter.job_edu?filter.job_edu:0},
                    {...res.data.job_exp,selected:filter.job_exp?filter.job_exp:0}
                ];

            }else{
                uni.showToast({
                    title:res.msg,
                    icon:'none'
                })
            }
           
            
            console.log(this.categories)
         })
    },
    // 切换一级分类
    switchCategory(index) {
      if (this.activeCategoryIndex === index) return;
      
      this.activeCategoryIndex = index;
      this.scrollToCategory(index);
    },
    
    // 滚动到指定分类
    scrollToCategory(index) {
      const query = uni.createSelectorQuery().in(this);
      query.select(`#category-${index}`).boundingClientRect();
      query.select('.secondary-options').boundingClientRect();
      
      query.exec(res => {
        if (res[0] && res[1]) {
          this.secondaryScrollTop = res[0].top - res[1].top + this.secondaryScrollTop;
        }
      });
    },
    
    // 处理右侧滚动
    handleSecondaryScroll(e) {
      if (this.scrollTimer) clearTimeout(this.scrollTimer);
      
      this.scrollTimer = setTimeout(() => {
        const scrollTop = e.detail.scrollTop;
        let activeIndex = 0;
        
        // 查找当前滚动位置对应的分类
        for (let i = 0; i < this.optionPositions.length; i++) {
          if (scrollTop >= this.optionPositions[i].top) {
            activeIndex = i;
          } else {
            break;
          }
        }
        
        // if (this.activeCategoryIndex !== activeIndex) {
        //   this.activeCategoryIndex = activeIndex;
        //   this.scrollPrimaryToActive();
        // }
      }, 50);
    },
    
    // 左侧滚动到当前激活项
    scrollPrimaryToActive() {
      const query = uni.createSelectorQuery().in(this);
      query.select('.primary-categories').boundingClientRect();
      query.selectAll('.category-item').boundingClientRect();
      
      query.exec(res => {
        if (res[0] && res[1]) {
          const containerHeight = res[0].height;
          const itemHeight = res[1][0].height;
          const activeItemTop = res[1][this.activeCategoryIndex].top - res[0].top;
          
          if (activeItemTop < 0) {
            this.primaryScrollTop = this.primaryScrollTop + activeItemTop;
          } else if (activeItemTop + itemHeight > containerHeight) {
            this.primaryScrollTop = this.primaryScrollTop + (activeItemTop + itemHeight - containerHeight);
          }
        }
      });
    },
    
    // 计算每个分类选项的位置
    calculateOptionPositions() {
      const query = uni.createSelectorQuery().in(this);
      query.selectAll('.option-category').boundingClientRect();
      query.select('.secondary-options').boundingClientRect();
      
      query.exec(res => {
        if (res[0] && res[1]) {
          this.optionPositions = res[0].map(item => ({
            top: item.top - res[1].top + this.secondaryScrollTop
          }));
        }
      });
    },
    
    // 选择选项
    selectOption(category, option) {
    //   if (category.type === 'radio') {
    //     category.selected = option.id;
    //   } else if (category.type === 'checkbox') {
    //     const index = category.selected.indexOf(option.id);
    //     if (index === -1) {
    //       category.selected.push(option.id);
    //     } else {
    //       category.selected.splice(index, 1);
    //     }
    //   }
    category.selected = option.id;
    category['selectedData'] = option;   

      const result = [];
      this.categories.forEach(category => {
        result.push(category.selectedData);
      });
    //   result
      console.log('选中了',result,this.categories);
    },
    
    // 判断选项是否选中
    isOptionSelected(category, option) {
    //   if (category.type === 'radio') {
    //     return category.selected === option.id;
    //   } else if (category.type === 'checkbox') {
    //     return category.selected.includes(option.id);
    //   }
    //   return false;


       return category.selected === option.id;
    },
    
    // 获取分类的选中数量
    getSelectedCount(category) {
      if (category.type === 'radio') {
        return category.selected ? 1 : 0;
      } else if (category.type === 'checkbox') {
        return category.selected.length;
      }
      return 0;
    },
    
    // 重置当前分类
    resetCurrentCategory() {
      // const category = this.categories[this.activeCategoryIndex];
      // if (category.type === 'radio') {
      //   category.selected = null;
      // } else if (category.type === 'checkbox') {
      //   category.selected = [];
      // }
       this.categories.forEach(category => {
        category.selected=0
      });
    },
    
    // 确认选择
    handleConfirm() {
      const result = {};
      this.categories.forEach(category => {
        result[category.name] = category.selected;
      });
      
      // 在实际应用中，这里可以处理筛选结果，比如跳转回上一页并携带参数
      console.log('筛选结果:', result);
      this.$emit('filterData', result);
    //   uni.$u.toast('筛选已应用');
      
      // 返回上一页
    //   uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.boss-filter-page {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: calc(100vh - 114rpx);
  background-color: #fff;
}

.navbar-right {
  padding-right: 15px;
  color: #2979ff;
  font-size: 15px;
}

.filter-container {
  display: flex;
  flex: 1;
  width: 100%;
  height: calc(100% - 300px); /* 减去导航栏高度 */
}

.primary-categories {
  width: 120px;
  height: 100%;
  background-color: #f7f7f7;
  border-right: 1px solid #eee;
}

.category-item {
  position: relative;
  padding: 16px 8px;
  font-size: 14px;
  color: #333;
  text-align: center;
  border-left: 3px solid transparent;
  
  &.active {
    background-color: #fff;
    color: #2979ff;
    border-left-color: #2979ff;
    font-weight: bold;
  }
}

.secondary-options {
  flex: 1;
  // height: 100%;
  padding: 10px 15px;
}

.option-category {
  margin-bottom: 15px;
}

.option-item {
  display: inline-block;
  padding: 8px 30px;
  margin: 0 10px 10px 0;
  font-size: 13px;
  color: #333;
  background-color: #f7f7f7;
  border-radius: 3px;
  
  &.selected {
    color: #2979ff;
    background-color: #e6f0ff;
  }
}

.action-buttons {
  display: flex;
  justify-content: center;
  padding: 15px 0 0px;
  background-color: #fff;
  height: 70px;
}
</style>