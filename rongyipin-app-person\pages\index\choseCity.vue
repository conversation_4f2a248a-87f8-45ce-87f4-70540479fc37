<template>
    <view class="container">
        <!-- <u-navbar :autoBack="true" title="选择工作城市" :titleStyle="{
          color: '#222',
          fontWeight: 'bold',
          fontSize: '36rpx'
      }" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar> -->
        <view class="search-input">

            <u--input placeholder="请输入内容" border="surround" v-model="value" @blur="change"></u--input>
        </view>
        <view class="current-city">{{ currentCity }} <u-icon size="20" name="arrow-down-fill"></u-icon></view>

        <view class="city-list-section">
            <scroll-view class="city-list" scroll-y>
                <view class="popup-container">
                    <!-- 加载中状态 -->
                    <!-- <template v-if="loading">
                  <view class="loading-container">
                      <u-loading-icon mode="circle" size="28"></u-loading-icon>
                      <text class="loading-text">加载中fdsf...</text>
                  </view>
              </template> -->
                    <!-- <template v-else> -->
                    <view class="province-list">
                        <scroll-view scroll-y>
                            <view v-for="(province, index) in provinces" :key="index" class="province-item"
                                :class="{ active: selectedProvince === index }" @click="selectProvince(index)">
                                {{ province.name }}
                            </view>
                        </scroll-view>
                    </view>
                    <view class="city-list" v-if="provinces.length > 0">
                        <scroll-view scroll-y>
                            <view v-for="(city, cIndex) in provinces[selectedProvince].children" :key="cIndex"
                                class="city-item" @click="selectCity(city)">
                                <text v-if="city.id == '110100' || city.id == '310100' || city.id == '120100' || city.id == '500100'"
                                    :class="{ checked: city === selectedCity }">
                                    全部
                                </text>
                                <text v-else :class="{ checked: city === selectedCity }">{{ city.name }}</text>
                                <!-- <u-icon v-if="city === selectedCity" name="checkmark" size="20" color="#00C2B3" /> -->

                            </view>
                            <div
                                v-if="provinces[selectedProvince].children[0].id == '110100' || provinces[selectedProvince].children[0].id == '310100' || provinces[selectedProvince].children[0].id == '120100' || provinces[selectedProvince].children[0].id == '500100'">
                                <view v-for="(city, cIndex) in provinces[selectedProvince].children[0].children"
                                    :key="cIndex" class="city-item" @click="selectCity(city)">
                                    <text :class="{ checked: city === selectedCity }">{{ city.name }}</text>
                                    <!-- <u-icon v-if="city === selectedCity" name="checkmark" size="20" color="#00C2B3" /> -->
                                </view>
                            </div>
                        </scroll-view>
                    </view>
                    <view class="popup-footer" v-else>
                        <u-loading-icon></u-loading-icon>
                        加载中
                    </view>
                    <!-- </template> -->

                </view>
                <view class="popup-footer">
                    <u-button type="primary" @click="confirmCity">完成</u-button>
                </view>
                <!-- <u-radio-group v-model="value" @change="radioGroupChange" iconSize="20">
                  <view class="city-item" v-for="(city, index) in cityList" :key="index">

                      <u-radio :name="city.title" :size="30" :iconSize="20" activeColor="#039885"></u-radio>
                      <view class="content" @click="selectCitylate(city)">
                          <p class="city-title" :class="{ active: value === city.title }">{{ city.title }}</p>
                          <p class="city-address">{{ city.address }}</p>
                      </view>

                  </view>
              </u-radio-group> -->
            </scroll-view>
        </view>
        <!-- <u-popup :show="show" :round="20" mode="bottom" @close="close" @open="open">
          <view class="popup-title">选择职位所在城市</view>
         
      </u-popup> -->
    </view>
</template>

<script>
import { homeApi } from '@/utils/api';
export default {
    data() {
        return {
            currentCity: '',
            cityList: [],
            value: '', // 当前选中的城市
            show: false,
            provinces: [],
            selectedProvince: 0,
            selectedCity: '',
            lastcity: ''
            //   loading: false,
        };
    },
    onShow() {

    },
    onLoad() {
        this.getCurrentCity()
    },
    mounted() {
        // this.getCurrentCity();
        //   this.loading = true;
    },
    methods: {
        // 根据城市名查找对应的省份索引和城市对象
        findProvinceAndCity(cityName) {
            console.log(cityName, '这是打印1');
            // 去掉可能存在的"市"字
            const targetCity = cityName;

            for (let i = 0; i < this.provinces.length; i++) {
                const province = this.provinces[i];
                // 先判断 province 是否存在 children
                if (!province || !province.children || !Array.isArray(province.children)) {
                    continue;
                }
                const city = province.children && province.children.find(city =>
                    city.name === targetCity
                );

                if (city) {
                    return {
                        provinceIndex: i,
                        cityObj: city ? city : ''
                    };
                }

            }

            return null;
        },
        async getCurrentCity() {
            uni.showLoading({
                title: '定位中...',
                mask: true
            });
            // uni.request({
            //     url: 'https://apis.map.qq.com/ws/location/v1/ip', //仅为示例，并非真实接口地址。
            //     method:'GET',
            //     data: {
            //         key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
            //     },

            //     success: (res) => {
            //         console.log(res,'##')
            //         this.currentCity = res.data.result.ad_info.city

            //         uni.hideLoading();

            //         this.surrounding(res.data.result.location.lat, res.data.result.location.lng)
            //     }
            // });
            await homeApi.getCityLists({
                key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
                // ip: '*************',
            }).then(res => {

                uni.hideLoading();
                this.currentCity = res.result.ad_info.city

                this.open()
                //   this.surrounding(res.result.location.lat, res.result.location.lng)
            })
        },
        surrounding(latitude, longitude) {
            //   uni.showLoading({
            //       title: '加载中...'
            //   });
            // uni.request({
            //     url: 'https://apis.map.qq.com/ws/place/v1/search', //仅为示例，并非真实接口地址。
            //     data: {
            //         key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
            //         page_index: '1',
            //         page_size: '10',
            //         boundary: `nearby(${latitude},${longitude},1000)`
            //     },
            //     success: (res) => {
            //         console.log(res,'###44')
            //         this.cityList = res.data.data
            //         uni.hideLoading();
            //     }
            // });
            homeApi.getSurroun({
                key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
                page_index: '1',
                page_size: '10',
                boundary: `nearby(${latitude},${longitude},1000)`
            }).then(res => {
                uni.hideLoading();
                this.cityList = res.data

            })
        },
        selectCitylate(city) {
            console.log(city, '这是城市的选择');
            this.value = city.title
            this.$store.commit('setCity', city);
            // 其他选中逻辑
            //   uni.navigateBack({
            //       url: '/pages/second/second'
            //   })
        },
        radioGroupChange(name) {
            console.log(name, '!!!!!!');
            this.value = name
            this.$store.commit('setCity', this.value);
            // 其他选中逻辑
            uni.navigateBack()
        },
        change(value) {
            console.log(value, 'value');
            //   console.log(this.currentCity)
            //   this.$store.commit('setCurrentCity', this.currentCity);
            //   uni.navigateTo({
            //       url: '/pages/second/map/address'
            //   })        
            // this.currentCity=value;
            this.locateCurrentCity(value);
        },
        selectPosition() {
            this.open()
        },
        async open() {
            // uni.showLoading({
            //       title: '加载中...'
            //   });
            this.show = true;
            // 没有缓存则加载数据
            //   this.loading = true;
            await homeApi.getcitylate().then(res => {
                 uni.hideLoading();
                if (res.code === 200) {
                   
                    this.provinces = res.data;
                    this.locateCurrentCity();
                }

            }).catch(error => {
                //  this.loading = false;
                uni.showToast({
                    title: '加载失败，请重试',
                    icon: 'none'
                });
            });



            //   try {
            //     // this.loading = false;

            //       const res = await homeApi.getcitylate();
            //       if (res.code === 200) {
            //         uni.hideLoading();
            //           this.provinces = res.data;
            //           this.locateCurrentCity();
            //       }
            //   } catch (error) {
            //     //  this.loading = false;
            //       uni.showToast({
            //           title: '加载失败，请重试',
            //           icon: 'none'
            //       });
            //   } finally {
            //     //   this.loading = false;
            //   }
        },
        // 定位到当前城市
        locateCurrentCity(value) {
            console.log('定位到当前城市',value);
            if (!this.currentCity || !this.provinces.length) return;

            const currentCity = value ? value : this.currentCity;
            const result = this.findProvinceAndCity(currentCity);
            console.log('result',result);

            if (result) {
                const provinceIndex = result.provinceIndex;

                // 安全访问 provinces[provinceIndex]
                const province = this.provinces[provinceIndex];
                if (!province) {
                    console.error('省份不存在:', provinceIndex);
                    return;
                }

                const cityObj = result.cityObj;

                this.selectedProvince = provinceIndex;
                this.selectedCity = cityObj || null;

                this.$nextTick(() => {
                    // 这里也可以加 scroll-view 的存在性判断
                });

                value ? (this.currentCity = value) : '';
            } else {
                uni.showToast({ title: '请输入正确的城市名字', icon: 'none' });
            }



        },
        preloadData() {
            if (!this.provincesCache) {
                homeApi.getcitylate().then(res => {
                    if (res.code === 200) {
                        this.provincesCache = res.data;
                    }
                });
            }
        },
        close() {
            this.show = false
            this.selectedPosition = null
            this.selectedAddress = ''
            this.showAddress = false
        },
        selectProvince(index) {
            this.selectedProvince = index;
            // 如果切换省份，清空已选城市
            if (!this.provinces[index].children.includes(this.selectedCity)) {
                this.selectedCity = null;
            }
        },
        selectCity(city) {
            this.selectedCity = city;
            this.currentCity = city.name;
        },
        confirmCity() {
            if (this.selectedCity) {
                console.log(this.selectedCity, '这是打印');
                this.currentCity = this.selectedCity.name;
                this.$emit('citydatafunc', this.selectedCity);
                //   uni.setStorageSync('hopecity',this.selectedCity)
                //   uni.navigateTo({
                //         url:`./addinfo`
                //     })
                // 其他确认逻辑...
                //   this.surrounding(this.selectedCity.gcj02_lat, this.selectedCity.gcj02_lng)
                //   this.close();
            } else {
                uni.showToast({
                    title: '请选择城市',
                    icon: 'none'
                });
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    /* padding: 20rpx; */
    height: 100vh;
}

.search-input {
    // padding: 30rpx;
    width: 93%;
    height: 80rpx;
    margin: 0 auto;
    display: flex;
    border: 1px solid #ccc;
    border-radius: 20rpx;
    margin-top: 30rpx;


}

.current-city {
    width: 50%;
    display: flex;
    align-items: center;
    padding: 13px 27px 0;

    .u-icon {
        margin-left: 10rpx;
    }
}

// ::v-deep .u-input {
//   border-left: 1px solid #ccc;
//   // border-radius: 20rpx;
//   margin-left: 5rpx;
// }

.city-list-section {
    padding: 30rpx;
    //   height: calc(100vh - 230rpx);
    overflow-y: scroll;

    ::v-deep .u-radio-group--row {
        display: inline;
    }

    .city-item {
        width: 100%;
        height: 40rpx;
        display: flex;
        align-items: center;

        .content {
            flex: 1;
            margin-left: 20rpx;
        }

        // border-bottom: 1px solid #ccc;
        .city-title {
            font-size: 30rpx;
            color: #333;
        }

        .city-title.active {
            color: #50b1b2;
            /* 被选中时变绿色 */
        }

        .city-address {
            font-size: 27rpx;
            color: #999;
            margin-top: 10rpx;
        }
    }
}

.popup-title {
    width: 100%;
    padding: 30rpx;
    width: 100%;
    font-size: 37rpx;
    color: #333;
    font-weight: bold;
}

.popup-container {
    display: flex;
    height: calc(100vh - 500rpx);
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    overflow: hidden;

    //   .loading-container {
    //       width: 100%;
    //       height: 200rpx;
    //       display: flex;
    //       flex-direction: column;
    //       align-items: center;
    //       justify-content: center;

    //       .loading-text {
    //           margin-top: 20rpx;
    //           font-size: 28rpx;
    //           color: #999;
    //       }
    //   }

    .province-list {
        width: 45%;
        background-color: #fff;
        overflow-y: auto;
    }

    .city-list {
        flex: 1;
        background-color: #f5f7fa;
        overflow-y: auto;
    }

    .province-item,
    .city-item {
        padding: 24rpx;
        text-align: center;
        font-size: 12px;
    }

    .province-item.active {
        color: #00C2B3;
        font-weight: bold;
        background-color: #f5f7fa;
    }

    .city-item {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 24rpx;
    }

    .city-item text.checked {
        color: #00C2B3;
        font-weight: bold;
    }


}

.popup-footer {
    padding: 20rpx;
    border-top: 1px solid #f0f0f0;
    background-color: #fff;

    ::v-deep .u-button {
        background-color: #50b1b2;
        border: 1px solid #50b1b2;
    }
}

@keyframes skeleton-loading {
    0% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0 50%;
    }
}
</style>