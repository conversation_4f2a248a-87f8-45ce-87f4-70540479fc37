<template>
  <div class="success-page">
    <u-navbar @leftClick="handleBack" title="" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <!-- 成功提示图标 -->
    <div class="icon-success">
      <i class="fas fa-check-circle"></i>
    </div>

    <!-- 成功提示文字 -->
    <div class="success-message">
      <h2>发布成功</h2>
      <p>通过审核后积分+10</p>
    </div>

    <!-- 操作按钮 -->
    <div class="action-button">
      <button @click="browseMore">浏览更多内容</button>
    </div>
  </div>
</template>

<script>
export default {
    data(){
        return{

        }
    },
  methods: {
    browseMore() {
      console.log('浏览更多内容');
      // 这里可以添加跳转到更多内容页面的逻辑
     uni.switchTab({
        url: '/pages/community/community'
      })
    },
    handleBack() {
      console.log('fanhuui');
      uni.navigateBack({
        delta:2
      });
    }
  }
}
</script>

<style scoped>
.success-page {
  text-align: center;
  padding: 50px;
}

.icon-success {
  font-size: 48px;
  color: #1abc9c;
  margin-bottom: 20px;
}

.success-message h2 {
  font-size: 24px;
  margin: 0;
  margin-bottom: 10px;
}

.success-message p {
  font-size: 16px;
  color: #777;
}

.action-button button {
  background-color: #1abc9c;
  color: white;
  border: none;
  padding: 10px 20px;
  font-size: 16px;
  border-radius: 5px;
  cursor: pointer;
}

.action-button button:hover {
  background-color: #16a085;
}
</style>