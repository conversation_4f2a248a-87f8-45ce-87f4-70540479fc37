<template>
    <view class="simple-pdf-viewer">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
            <u-loading-icon mode="spinner" size="40" color="#14B19E"></u-loading-icon>
            <text class="loading-text">{{ loadingText }}</text>
        </view>

        <!-- 错误状态 -->
        <view v-else-if="error" class="error-container">
            <u-icon name="file-pdf" size="80" color="#ff4757"></u-icon>
            <text class="error-title">{{ fileName || 'PDF文档' }}</text>
            <text class="error-text">{{ errorMessage }}</text>
            <view class="error-actions">
                <button class="action-btn primary" @click="downloadAndOpen">
                    <u-icon name="download" size="18" color="#fff" style="margin-right: 8px;"></u-icon>
                    下载查看
                </button>
                <button class="action-btn secondary" @click="retryLoad">
                    <u-icon name="refresh" size="18" color="#14B19E" style="margin-right: 8px;"></u-icon>
                    重新加载
                </button>
            </view>
        </view>

        <!-- PDF Canvas显示区域 -->
        <view v-else class="pdf-display">
            <!-- 工具栏 -->
            <view class="toolbar" v-if="showControls">
                <view class="toolbar-left">
                    <button class="tool-btn" @click="zoomOut" :disabled="scale <= 0.5">
                        <u-icon name="minus" size="16" color="#333"></u-icon>
                    </button>
                    <text class="zoom-text">{{ Math.round(scale * 100) }}%</text>
                    <button class="tool-btn" @click="zoomIn" :disabled="scale >= 3">
                        <u-icon name="plus" size="16" color="#333"></u-icon>
                    </button>
                </view>
                <view class="toolbar-right">
                    <text class="page-info" v-if="totalPages > 1">{{ currentPage }} / {{ totalPages }}</text>
                    <button class="tool-btn" @click="downloadAndOpen">
                        <u-icon name="download" size="16" color="#333"></u-icon>
                    </button>
                </view>
            </view>

            <!-- Canvas容器 -->
            <scroll-view class="canvas-scroll" 
                         scroll-x="true" 
                         scroll-y="true"
                         enhanced="true"
                         :show-scrollbar="false">
                <view class="canvas-wrapper" 
                      :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }">
                    <canvas 
                        class="pdf-canvas"
                        canvas-id="simplePdfCanvas"
                        :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
                        @touchstart="onTouchStart"
                        @touchmove="onTouchMove"
                        @touchend="onTouchEnd">
                    </canvas>
                </view>
            </scroll-view>

            <!-- 页面导航 (多页PDF) -->
            <view v-if="totalPages > 1" class="page-navigation">
                <button class="nav-btn" @click="prevPage" :disabled="currentPage <= 1">
                    <u-icon name="arrow-left" size="20" color="#333"></u-icon>
                </button>
                <view class="page-dots">
                    <view v-for="page in Math.min(totalPages, 5)" 
                          :key="page" 
                          class="dot" 
                          :class="{ active: page === currentPage }"
                          @click="goToPage(page)">
                    </view>
                    <text v-if="totalPages > 5" class="more-pages">...</text>
                </view>
                <button class="nav-btn" @click="nextPage" :disabled="currentPage >= totalPages">
                    <u-icon name="arrow-right" size="20" color="#333"></u-icon>
                </button>
            </view>
        </view>
    </view>
</template>

<script>
import PDFCanvasRenderer from '@/utils/pdf-canvas-renderer.js'

export default {
    name: 'SimplePdfViewer',
    props: {
        // PDF文件URL
        pdfUrl: {
            type: String,
            required: true
        },
        // 文件名
        fileName: {
            type: String,
            default: ''
        },
        // 是否显示控制按钮
        showControls: {
            type: Boolean,
            default: true
        },
        // 自动加载
        autoLoad: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            loading: false,
            error: false,
            loadingText: '正在加载PDF...',
            errorMessage: '',
            
            // PDF渲染器
            renderer: null,
            
            // PDF信息
            currentPage: 1,
            totalPages: 0,
            scale: 1,
            
            // Canvas尺寸
            canvasWidth: 400,
            canvasHeight: 600,
            
            // 触摸相关
            touchStartX: 0,
            touchStartY: 0,
            touchStartDistance: 0,
            touchStartScale: 1,
            
            // 系统信息
            systemInfo: {}
        }
    },
    mounted() {
        this.init()
    },
    beforeDestroy() {
        if (this.renderer) {
            this.renderer.destroy()
        }
    },
    watch: {
        pdfUrl(newUrl) {
            if (newUrl && this.autoLoad) {
                this.loadPDF()
            }
        }
    },
    methods: {
        // 初始化
        async init() {
            try {
                // 获取系统信息
                this.systemInfo = uni.getSystemInfoSync()
                
                // 设置Canvas默认尺寸
                this.canvasWidth = this.systemInfo.windowWidth - 40
                this.canvasHeight = this.systemInfo.windowHeight - 200
                
                // 创建PDF渲染器
                this.renderer = new PDFCanvasRenderer({
                    canvasId: 'simplePdfCanvas',
                    scale: this.scale
                })
                
                // 自动加载PDF
                if (this.pdfUrl && this.autoLoad) {
                    await this.loadPDF()
                }
                
            } catch (error) {
                console.error('初始化失败:', error)
                this.showError('初始化失败')
            }
        },

        // 加载PDF
        async loadPDF() {
            if (!this.pdfUrl) {
                this.showError('PDF地址不能为空')
                return
            }

            this.loading = true
            this.error = false
            this.loadingText = '正在加载PDF文档...'

            try {
                // 使用渲染器加载PDF
                const result = await this.renderer.loadPDF(this.pdfUrl)
                
                if (result.success) {
                    this.totalPages = result.totalPages
                    this.loadingText = '正在渲染页面...'
                    
                    // 渲染第一页
                    await this.renderPage(1)
                    
                    this.loading = false
                    
                    // 触发加载完成事件
                    this.$emit('loaded', {
                        totalPages: this.totalPages,
                        currentPage: this.currentPage
                    })
                } else {
                    throw new Error('PDF加载失败')
                }
                
            } catch (error) {
                console.error('PDF加载失败:', error)
                this.showError(error.message || 'PDF加载失败')
            }
        },

        // 渲染页面
        async renderPage(pageNum) {
            try {
                if (!this.renderer) return
                
                const result = await this.renderer.renderPage(pageNum)
                
                if (result.success) {
                    this.currentPage = pageNum
                    this.canvasWidth = result.width || this.canvasWidth
                    this.canvasHeight = result.height || this.canvasHeight
                    
                    // 触发页面变化事件
                    this.$emit('pageChange', {
                        currentPage: this.currentPage,
                        totalPages: this.totalPages
                    })
                }
                
            } catch (error) {
                console.error('页面渲染失败:', error)
                this.showError('页面渲染失败')
            }
        },

        // 显示错误
        showError(message) {
            this.error = true
            this.errorMessage = message
            this.loading = false
            
            this.$emit('error', { message })
        },

        // 重新加载
        retryLoad() {
            this.loadPDF()
        },

        // 缩放控制
        zoomIn() {
            if (this.scale < 3) {
                this.scale = Math.min(this.scale + 0.25, 3)
                this.updateScale()
            }
        },

        zoomOut() {
            if (this.scale > 0.5) {
                this.scale = Math.max(this.scale - 0.25, 0.5)
                this.updateScale()
            }
        },

        // 更新缩放
        async updateScale() {
            if (this.renderer) {
                this.renderer.setScale(this.scale)
                await this.renderPage(this.currentPage)
                
                this.$emit('scaleChange', { scale: this.scale })
            }
        },

        // 页面导航
        prevPage() {
            if (this.currentPage > 1) {
                this.renderPage(this.currentPage - 1)
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.renderPage(this.currentPage + 1)
            }
        },

        goToPage(pageNum) {
            if (pageNum >= 1 && pageNum <= this.totalPages) {
                this.renderPage(pageNum)
            }
        },

        // 下载并打开
        async downloadAndOpen() {
            try {
                uni.showLoading({ title: '准备下载...' })
                
                // #ifdef APP-PLUS
                const downloadTask = uni.downloadFile({
                    url: this.pdfUrl,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            uni.hideLoading()
                            uni.openDocument({
                                filePath: res.tempFilePath,
                                showMenu: true,
                                success: () => {
                                    uni.showToast({
                                        title: '文件已打开',
                                        icon: 'success'
                                    })
                                },
                                fail: () => {
                                    uni.showToast({
                                        title: '无法打开文件',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    },
                    fail: () => {
                        uni.hideLoading()
                        uni.showToast({
                            title: '下载失败',
                            icon: 'none'
                        })
                    }
                })
                
                downloadTask.onProgressUpdate((res) => {
                    uni.showLoading({ title: `下载中...${res.progress}%` })
                })
                // #endif
                
                // #ifdef H5
                uni.hideLoading()
                const link = document.createElement('a')
                link.href = this.pdfUrl
                link.download = this.fileName || 'document.pdf'
                link.target = '_blank'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
                // #endif
                
                // #ifdef MP-WEIXIN
                uni.hideLoading()
                uni.showToast({
                    title: '请在浏览器中下载',
                    icon: 'none'
                })
                // #endif
                
            } catch (error) {
                uni.hideLoading()
                console.error('下载失败:', error)
                uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                })
            }
        },

        // 触摸事件处理
        onTouchStart(e) {
            if (e.touches.length === 2) {
                // 双指缩放
                const touch1 = e.touches[0]
                const touch2 = e.touches[1]
                this.touchStartDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                )
                this.touchStartScale = this.scale
            }
        },

        onTouchMove(e) {
            if (e.touches.length === 2) {
                // 双指缩放
                const touch1 = e.touches[0]
                const touch2 = e.touches[1]
                const currentDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                )
                
                const scaleRatio = currentDistance / this.touchStartDistance
                const newScale = this.touchStartScale * scaleRatio
                
                if (newScale >= 0.5 && newScale <= 3) {
                    this.scale = newScale
                }
            }
        },

        onTouchEnd(e) {
            if (e.touches.length === 0) {
                // 触摸结束，更新缩放
                this.updateScale()
            }
        }
    }
}
</script>

<style scoped>
.simple-pdf-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    padding: 40px 20px;
}

.loading-text {
    font-size: 14px;
    color: #666;
}

.error-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-top: 15px;
}

.error-text {
    font-size: 14px;
    color: #999;
    text-align: center;
    line-height: 1.5;
}

.error-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    border: none;
}

.action-btn.primary {
    background-color: #14B19E;
    color: #fff;
}

.action-btn.secondary {
    background-color: #fff;
    color: #14B19E;
    border: 1px solid #14B19E;
}

/* PDF显示区域 */
.pdf-display {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 工具栏 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #fff;
    border-bottom: 1px solid #e5e5e5;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.tool-btn {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    font-size: 12px;
}

.tool-btn:disabled {
    opacity: 0.5;
}

.zoom-text {
    font-size: 12px;
    color: #666;
    min-width: 40px;
    text-align: center;
}

.page-info {
    font-size: 12px;
    color: #333;
}

/* Canvas区域 */
.canvas-scroll {
    flex: 1;
    background-color: #fff;
}

.canvas-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;
    padding: 20px;
}

.pdf-canvas {
    display: block;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
}

/* 页面导航 */
.page-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    background-color: #fff;
    border-top: 1px solid #e5e5e5;
    gap: 15px;
}

.nav-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #e5e5e5;
    border-radius: 20px;
}

.nav-btn:disabled {
    opacity: 0.5;
}

.page-dots {
    display: flex;
    align-items: center;
    gap: 8px;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #e5e5e5;
    transition: all 0.3s ease;
}

.dot.active {
    background-color: #14B19E;
    transform: scale(1.2);
}

.more-pages {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
}
</style>
