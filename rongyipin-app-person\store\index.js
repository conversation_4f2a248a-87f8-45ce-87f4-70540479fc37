
import Vue from 'vue'
import Vuex from 'vuex'
import { chat } from "@/utils/api.js"
Vue.use(Vuex)

export default new Vuex.Store({
  state: {
    count: 0,
    user: null,
    city: '',
    currentCity: '',
    work: '',
    cation: '',
    cationdetail: '',
    realTimeData: '',
    yourDataList: [],
    lastReceivedMessage: null // 用于Chat页面监听的最新消息
  },
  mutations: {
    setUser(state, user) {
      state.user = user
    },
    setCity(state, city) {
      state.city = city
    },
    setCurrentCity(state, currentCity) {
      state.currentCity = currentCity
    },
    setwork(state, work) {
      state.work = work
    },
    clearWork(state) {
      state.work = ''; // 清除 work 的值
    },
    setCation(state, cation) {
      console.log(cation, 'cation')
      state.cation = cation
    },
    setCationdetail(state, cationdetail) {
      console.log(cationdetail, 'cationdetail')
      state.cationdetail = cationdetail
    },
    updateData(state, data) {
      state.realTimeData = data;
    },
    UPDATE_DATA_LIST(state, payload) {
      state.yourDataList = payload;
    },
    setLastReceivedMessage(state, message) {
      state.lastReceivedMessage = message;
      console.log('🔄 Vuex: 更新lastReceivedMessage', message);
    }
  },
  getters: {
    getCity: state => state.city,
    getUser: state => state.user
  },
  actions: {
    updateCity({ commit }, city) {
      commit('setCity', city)
    },
    async handleWebSocketMessage({ commit, state }, message) {
      console.log(message, 'message')

      // 更新最新消息到state，供Chat页面监听
      commit('setLastReceivedMessage', message)

      const res = await chat.chatList()
      if (res.code == 200 ) {
        if (res.data.Unread > 0) {
          uni.setTabBarBadge({
            index: 3,
            text: res.data.Unread.toString()
          })
        } else {
          uni.removeTabBarBadge({
            index: 3
          })
        }

        // 通知消息页面更新数据
        uni.$emit('message-list-updated', {
          messageList: res.data.data || [],
          unreadCount: res.data.Unread || 0,
          total: res.data.total || 0
        })
      }
      uni.hideLoading()
    }
  }
})