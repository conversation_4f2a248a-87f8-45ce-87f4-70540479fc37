<template>
	<view class="account-container">
         <view class="navbar">
            <u-navbar height="44px" title="账号与安全" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>
		<!-- 列表容器 -->
		<view class="list-card">
			<!-- 更换手机号 -->
			<view class="list-item" @click="navigateTo('./mobilePhone')">
				<text class="item-label">更换手机号</text>
				<view class="item-right">
					<text class="item-info">{{ maskedPhone }}</text>
					<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
				</view>
			</view>

			<!-- 注销账号 -->
			<view class="list-item" @click="navigateTo('./emitPassword')">
				<text class="item-label">注销账号</text>
				<view class="item-right">
					<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
				</view>
			</view>

			<!-- 更换密码 -->
			<view class="list-item" @click="navigateTo('./emitPassword')">
				<text class="item-label">更换密码</text>
				<view class="item-right">
					<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 假设这是从API获取的用户手机号
				phone: '15512346596'
			}
		},
		computed: {
			// 计算属性，用于生成脱敏后的手机号
			maskedPhone() {
				if (this.phone && this.phone.length === 11) {
					// 使用正则表达式进行替换，更安全高效
					return this.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
				}
				return this.phone;
			}
		},
		// onLoad生命周期，用于设置页面标题
		onLoad() {
			this.phone =uni.getStorageSync('userInfo').mobile
		},
		methods: {
			// 统一的跳转方法
			navigateTo(url) {
				uni.navigateTo({
					url: url,
					fail: (err) => {
						console.error(`跳转失败: ${url}`, err);
						uni.showToast({
							title: '页面不存在',
							icon: 'none'
						})
					}
				});
			}
		}
	}
</script>

<style scoped>
	/* 页面背景 */
	.account-container {
		background-color: #F5F6FA;
		min-height: 100vh;
		padding-top: 24rpx;
	}

	/* 列表卡片样式 */
	.list-card {
		background-color: #FFFFFF;
		/* margin: 0 30rpx; */
		border-radius: 16rpx;
		overflow: hidden; /* 防止子元素的边框溢出圆角 */
	}

	/* 列表项 */
	.list-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 32rpx;
		/* 使用伪类给除了最后一个元素的所有项添加下边框 */
		border-bottom: 1rpx solid #EBEEF5;
		/* 激活状态效果 */
		transition: background-color 0.2s;
	}

	.list-item:active {
		background-color: #f9f9f9;
	}

	/* 移除最后一个列表项的下边框 */
	.list-item:last-child {
		border-bottom: none;
	}

	/* 左侧标签 */
	.item-label {
		font-size: 30rpx;
		color: #303133;
	}

	/* 右侧内容区域 */
	.item-right {
		display: flex;
		align-items: center;
		color: #909399; /* 右侧内容统一颜色 */
	}

	/* 右侧信息文本，如手机号 */
	.item-info {
		font-size: 28rpx;
		margin-right: 12rpx;
	}
</style>