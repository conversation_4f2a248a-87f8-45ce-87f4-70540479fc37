<template>
  <view>
    <view v-if="loading" class="loading">
      <u-loading-icon></u-loading-icon>
      加载中
    </view>

    <u-empty mode="data" v-else-if="list.length === 0"></u-empty>

    <view v-else>
      <view class="company-list">
        <view v-for="company in list" :key="company.id" class="company-item">
          <!-- 公司信息 -->
          <view class="company-info">
            <u-image width="120rpx" height="120rpx" :src="company.logo" shape="square"></u-image>
            <view class="company-info-right"> 
              <view class="company-name">{{ company.name }}</view>
              <view class="company-details">
                <text>{{ company.size }}</text>
                <text>{{ company.address }}</text>
              </view>
            </view>
          </view>

          <!-- 职位列表 -->
          <view v-for="(job, index) in visibleJobs(company)" :key="job.title" class="job-item">
            <view class="job-title">{{ job.name }}</view>
            <view class="job-details">
              <text>{{ job.experience }}</text>
              <text>{{ job.education }}</text>
              <text v-for="(tag, tagIndex) in job.tags" :key="tagIndex" class="job-tag">{{ tag }}</text>
              <text class="job-salary">{{ job.salary }}元</text>
            </view>
          </view>
          <view v-show="!company.jobs.length==0">
            <!-- 查看更多职位按钮 -->
            <view v-if="company.jobs.length > 2 && !company.expanded" class="see-more" @click="expandCompany(company)">
              查看更多职位 >
            </view>
            <view v-else class="see-more" @click="shouqi(company)">
              收起 <
            </view>
          </view>
          
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    subTab: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      expandedCompanies: {}
    };
  },
  methods: {
    getLogo(name) {
      // 模拟公司logo
      return `https://ui-avatars.com/api/?name=${encodeURIComponent(name)}&background=random`;
    },
    viewDetail(id) {
      uni.navigateTo({
        url: `/pages/company/detail?id=${id}`
      });
    },
    expandCompany(company) {
      
      company["expanded"]=true;
      console.log("expandCompany", company);
      console.log("expandedCompanies", this.expandedCompanies);
      this.$set(this.expandedCompanies, company.id, true);
    },
    shouqi(company){
      company["expanded"]=false;
      console.log("expandCompany", company);
      console.log("expandedCompanies", this.expandedCompanies);
      this.$set(this.expandedCompanies, company.id, false);
    },

    visibleJobs(company) {
      if (this.expandedCompanies[company.id]) {
        return company.jobs;
      } else {
        return company.jobs.slice(0, 2);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.loading {
  text-align: center;
  padding: 30rpx 0;
  color: #c7c7c7;
}

.company-item {
  margin-bottom: 20rpx;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
}

.company-info {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.company-name {
  font-size: 32rpx;
  font-weight: bold;
}

.company-details {
  font-size: 24rpx;
  color: #666;
}

.job-item {
  margin-top: 10rpx;
}

.job-title {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 5rpx;
}

.job-details {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666;
}

.job-tag {
  background-color: #f5f5f5;
  padding: 5rpx 10rpx;
  margin-right: 10rpx;
  border-radius: 5rpx;
}

.job-salary {
  color: #07c160;
  margin-left: auto;
}

.see-more {
  text-align: center;
  margin-top: 20rpx;
  color: #07c160;
  cursor: pointer;
}
</style>