<template>
    <view class="attachment-resume-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="附件简历" :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'"
                safeAreaInsetTop placeholder fixed>
                <template #right>
                    <u-icon name="more-dot-fill" size="20" color="#333"></u-icon>
                </template>
            </u-navbar>
        </view>

        <!-- 内容区域 -->
        <view class="content-container">
            <!-- 有数据时显示列表 -->
            <view v-if="resumeList.length > 0" class="resume-list">
                <view v-for="(item, index) in resumeList" :key="item.id" class="resume-item">
                    <!-- PDF图标 -->
                    <view class="pdf-icon">
                        <u-icon name="file-text" size="40" color="#FF6B35"></u-icon>
                    </view>

                    <!-- 文件信息 -->
                    <view class="file-info">
                        <view class="file-name">
                            <text class="name-text">{{ item.attachment_name }}</text>
                            <text v-if="item.isDefault" class="default-tag">默认</text>
                        </view>
                        <text class="update-time">{{ item.update_time }}</text>
                    </view>

                    <!-- 操作按钮 -->
                    <view class="action-buttons">
                        <view class="action-btn" @click="previewResume(item)">
                            <u-icon name="eye" size="30" color="#666"></u-icon>
                        </view>
                        <view class="action-btn" @click="deleteResume(item, index)">
                            <u-icon name="trash" size="30" color="#666"></u-icon>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 无数据时显示空状态 -->
            <view v-else class="empty-state">
                <view class="empty-icon">
                    <u-icon name="file-text" size="80" color="#ccc"></u-icon>
                </view>
                <text class="empty-text">暂无附件简历</text>
                <text class="empty-desc">您还没有上传任何简历文件</text>
                <button @click="pickAndUpload">选择并上传文件</button>
                 <view style="margin-top: 10px;">
                <button @click="testPDFUpload" style="background: #007AFF; color: white; border: none; padding: 10px; border-radius: 5px; margin-bottom: 10px;">
                    测试PDF上传
                </button>
                <button @click="debugFileInfo" style="background: #FF9500; color: white; border: none; padding: 10px; border-radius: 5px;">
                    调试文件信息
                </button>
            </view>
                <!-- <uni-file-picker
                    v-model="fileList"
                    
                    fileMediatype="file"
                    file-extname="pdf"
                    limit="1"
                    @select="onSelect"
                    @success="onSuccess"
                    >上传简历</uni-file-picker> -->
                <!-- <view class="upload-btn" @click="uploadResume">
                    <text class="upload-text">上传简历</text>
                </view> -->
            </view>
        </view>

        <!-- 底部上传按钮 -->
        <view v-if="resumeList.length > 0" class="bottom-upload">
            <view>
                <!-- <u-icon name="plus" size="20" color="#fff"></u-icon> -->
                <uni-file-picker
                    v-model="fileList"
                    fileMediatype="file"
                    file-extname="pdf"
                    limit="1"
                    @select="onSelect"
                    @success="onSuccess"
                    >上传简历</uni-file-picker>
            </view>
            <!-- 测试按钮 - 仅在开发环境显示 -->
            <!-- #ifdef APP-PLUS -->
           
            <!-- #endif -->
        </view>
    </view>
</template>

<script>
import {filelate} from "@/utils/api.js"
export default {
    data() {
        return {
            resumeList: [], // 简历列表
            fileList:[]
        }
    }, 
    onLoad() {
        this.loadResumeList()
    },
    onShow(){
        console.log('onshow')
        if(window.uriValue){
           console.log(window.uriValue)
           this.handleFileUpload(window.uriValue);
        }
    },
    methods: {
        // 文件选择回调
        onSelect(files) {
  
            console.log("选择的文件:", files.tempFiles[0],this.resumeList);
            // this.resumeList=[files.tempFiles[0]];
            this.handleFileUpload(files.tempFiles[0])
//             uni.uploadFile({
//                 url: '/jobapi/common/upload',
//                 filePath: files.tempFiles[0].url,
//                 name: 'file',
//                 success: (res) => {
//                     console.log("上传成功:", res);
//                 },
//                 fail: (err) => {
// }
//             })
        },
        pickAndUpload() {
            // 跳转到新的上传页面
            uni.navigateTo({
                url: '/pages/user/resume/simple-upload'
            })
        },

        // 处理文件上传 - 支持二进制数据
        async handleFileUpload(file) {
            // #ifdef APP-PLUS
            const permissions = [
                'android.permission.READ_EXTERNAL_STORAGE',
                'android.permission.WRITE_EXTERNAL_STORAGE'
            ]

            plus.android.requestPermissions(permissions, (result) => {
                console.log('📋 权限请求结果:', result)
                let hasAllPermissions = true

                for (let i = 0; i < result.granted.length; i++) {
                    if (permissions.includes(result.granted[i])) {
                        console.log('✅ 权限已获取:', result.granted[i])
                    }
                }

                for (let i = 0; i < result.deniedAlways.length; i++) {
                    if (permissions.includes(result.deniedAlways[i])) {
                        console.log('❌ 权限被永久拒绝:', result.deniedAlways[i])
                        hasAllPermissions = false
                    }
                }

                for (let i = 0; i < result.deniedPresent.length; i++) {
                    if (permissions.includes(result.deniedPresent[i])) {
                        console.log('❌ 权限被拒绝:', result.deniedPresent[i])
                        hasAllPermissions = false
                    }
                }

                if (hasAllPermissions) {
                    callback()
                } else {
                    uni.showModal({
                        title: '权限不足',
                        content: '需要存储权限才能选择文件，请在设置中开启权限',
                        showCancel: false
                    })
                }
            }, (error) => {
                console.error('❌ 权限请求失败:', error)
                uni.showToast({
                    title: '权限请求失败',
                    icon: 'none'
                })
            })
            // #endif
        },

        // 打开文件管理器
        openFileManager() {
            console.log('📁 打开文件管理器')

            // #ifdef APP-PLUS
            const main = plus.android.runtimeMainActivity()
            const Intent = plus.android.importClass('android.content.Intent')
            const intent = new Intent(Intent.ACTION_GET_CONTENT)

            intent.addCategory(Intent.CATEGORY_OPENABLE)
            intent.setType('application/pdf') // 只允许选择PDF文件

            const REQUEST_CODE = 1001

            main.onActivityResult = (requestCode, resultCode, data) => {
                console.log('📄 文件选择结果:', { requestCode, resultCode, data })

                if (requestCode === REQUEST_CODE && resultCode === -1 && data) {
                    const uri = data.getData()
                    console.log('📎 选择的文件URI:', uri.toString())

                    // 处理选择的文件
                    this.handleSelectedFile(uri)
                }
            }

            main.startActivityForResult(intent, REQUEST_CODE)
            // #endif
        },

        // 处理选择的文件
        handleSelectedFile(uri) {
            console.log('🔄 处理选择的文件:', uri.toString())

            // #ifdef APP-PLUS
            const uriString = uri.toString()
            console.log('🚀 获取文件信息:', uriString)
            // 获取文件信息  
            this.getFileInfoFromUri(uri, (fileInfo) => {
                console.log('📋 文件信息:', fileInfo) 

                // 读取文件为二进制数据
                this.readFileAsBinary(uri, fileInfo, (binaryData) => {
                    console.log('� readFileAsBinary回调被调用')

                    if (!binaryData) {
                        console.error('❌ 二进制数据为空')
                        uni.showToast({
                            title: '文件读取失败',
                            icon: 'none'
                        })
                        return
                    }

                    console.log('�💾 文件二进制数据长度:', binaryData.length)

                    // 创建File对象传递给handleFileUpload
                    const fileObject = {
                        name: fileInfo.name,
                        size: fileInfo.size,
                        type: 'application/pdf',
                        data: binaryData, // 二进制数据
                        uri: uriString
                    }

                    console.log('📦 创建的文件对象:', {
                        name: fileObject.name,
                        size: fileObject.size,
                        type: fileObject.type,
                        dataLength: fileObject.data ? fileObject.data.length : 0
                    })

                    // 调用上传方法
                    console.log('🚀 准备调用handleFileUpload')
                    this.handleFileUpload(fileObject)
                })
            })
            // #endif
        },

        // 从URI获取文件信息
        getFileInfoFromUri(uri, callback) {
            console.log('🔍 开始获取文件信息:', uri.toString())

            // #ifdef APP-PLUS
            try {
                const ContentResolver = plus.android.importClass('android.content.ContentResolver')
                const Cursor = plus.android.importClass('android.database.Cursor')
                const OpenableColumns = plus.android.importClass('android.provider.OpenableColumns')

                console.log('📱 Android类导入完成')

                const resolver = plus.android.runtimeMainActivity().getContentResolver()
                console.log('📱 获取ContentResolver完成')

                const cursor = resolver.query(uri, null, null, null, null)
                console.log('📱 查询cursor完成:', !!cursor)

                let fileName = 'unknown.pdf'
                let fileSize = 0

                if (cursor && cursor.moveToFirst()) {
                    console.log('📱 cursor移动到第一行成功')

                    try {
                        const nameIndex = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME)
                        const sizeIndex = cursor.getColumnIndex(OpenableColumns.SIZE)

                        console.log('📱 列索引:', { nameIndex, sizeIndex })

                        if (nameIndex !== -1) {
                            fileName = cursor.getString(nameIndex)
                            console.log('📱 获取文件名:', fileName)
                        }
                        if (sizeIndex !== -1) {
                            fileSize = cursor.getLong(sizeIndex)
                            console.log('📱 获取文件大小:', fileSize)
                        }
                    } catch (columnError) {
                        console.error('❌ 获取列数据失败:', columnError)
                    }

                    cursor.close()
                    console.log('📱 cursor已关闭')
                } else {
                    console.log('⚠️ cursor为空或无法移动到第一行')
                }

                const fileInfo = { name: fileName, size: fileSize }
                console.log('✅ 文件信息获取完成:', fileInfo)

                // 确保回调在下一个事件循环中执行
                setTimeout(() => {
                    callback(fileInfo)
                }, 0)

            } catch (error) {
                console.error('❌ 获取文件信息失败:', error)
                // 即使出错也要调用回调，避免流程中断
                setTimeout(() => {
                    callback({ name: 'error.pdf', size: 0 })
                }, 0)
            }
            // #endif
        },

        // 读取文件为二进制数据
        readFileAsBinary(uri, fileInfo, callback) {
            console.log('🔍 开始读取文件二进制数据:', uri.toString())

            // #ifdef APP-PLUS
            try {
                console.log('📱 导入Android类...')
                const ContentResolver = plus.android.importClass('android.content.ContentResolver')
                const InputStream = plus.android.importClass('java.io.InputStream')
                const ByteArrayOutputStream = plus.android.importClass('java.io.ByteArrayOutputStream')
                console.log('✅ Android类导入完成')

                console.log('📱 获取ContentResolver...')
                const resolver = plus.android.runtimeMainActivity().getContentResolver()
                console.log('✅ ContentResolver获取完成')

                console.log('📱 打开输入流...')
                const inputStream = resolver.openInputStream(uri)
                if (!inputStream) {
                    throw new Error('无法打开输入流')
                }
                console.log('✅ 输入流打开成功')

                console.log('📱 创建输出流...')
                const byteArrayOutputStream = new ByteArrayOutputStream()
                console.log('✅ 输出流创建完成')

                console.log('📱 开始读取文件数据...')
                const buffer = plus.android.newObject('[B', 1024) // 1KB buffer
                let bytesRead
                let totalBytes = 0

                while ((bytesRead = inputStream.read(buffer)) !== -1) {
                    byteArrayOutputStream.write(buffer, 0, bytesRead)
                    totalBytes += bytesRead
                    if (totalBytes % 10240 === 0) { // 每10KB打印一次进度
                        console.log('📊 已读取:', totalBytes, '字节')
                    }
                }

                console.log('📱 转换为字节数组...')
                const byteArray = byteArrayOutputStream.toByteArray()

                console.log('📱 关闭流...')
                inputStream.close()
                byteArrayOutputStream.close()
                console.log('✅ 流已关闭')

                console.log('✅ 文件读取完成，总大小:', byteArray.length, '字节')

                // 确保回调在下一个事件循环中执行
                setTimeout(() => {
                    callback(byteArray)
                }, 0)

            } catch (error) {
                console.error('❌ 读取文件失败:', error)
                console.error('❌ 错误详情:', error.message)
                console.error('❌ 错误堆栈:', error.stack)

                uni.showToast({
                    title: '读取文件失败: ' + error.message,
                    icon: 'none',
                    duration: 3000
                })

                // 即使出错也要调用回调，避免流程中断
                setTimeout(() => {
                    callback(null)
                }, 0)
            }
            // #endif
        },

        // 测试方法：创建一个模拟的PDF文件对象
        testPDFUpload() {
            console.log('🧪 测试PDF上传功能')

            // 创建一个简单的PDF内容
            const pdfContent = '%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\nxref\n0 2\n0000000000 65535 f \n0000000009 00000 n \ntrailer\n<<\n/Size 2\n/Root 1 0 R\n>>\nstartxref\n74\n%%EOF'

            const encoder = new TextEncoder()
            const uint8Array = encoder.encode(pdfContent)

            const testFile = {
                name: 'test-resume.pdf',
                size: uint8Array.length,
                type: 'application/pdf',
                data: uint8Array
            }

            console.log('🧪 测试文件对象:', testFile)
            this.handleFileUpload(testFile)
        },

        // 调试方法：测试文件信息获取
        debugFileInfo() {
            console.log('🔧 调试文件信息获取')

            // #ifdef APP-PLUS
            try {
                // 测试Android类导入
                console.log('🔧 测试Android类导入...')
                const ContentResolver = plus.android.importClass('android.content.ContentResolver')
                const Cursor = plus.android.importClass('android.database.Cursor')
                const OpenableColumns = plus.android.importClass('android.provider.OpenableColumns')
                console.log('✅ Android类导入成功')

                // 测试获取ContentResolver
                console.log('🔧 测试获取ContentResolver...')
                const resolver = plus.android.runtimeMainActivity().getContentResolver()
                console.log('✅ ContentResolver获取成功:', !!resolver)

                uni.showToast({
                    title: '调试测试完成，查看控制台',
                    icon: 'none'
                })

            } catch (error) {
                console.error('❌ 调试测试失败:', error)
                uni.showToast({
                    title: '调试测试失败: ' + error.message,
                    icon: 'none'
                })
            }
            // #endif
        },

        ip(obj){
            plus.android.importClass(obj);
            return obj;
        },
            
        uploadFileInit(){
            const CODE_REQUEST = 500;  
                let context = plus.android.runtimeMainActivity();  
                
                this.ip(context);  
            let Intent = plus.android.importClass('android.content.Intent');  
                let intent = new Intent(Intent.ACTION_GET_CONTENT);  
                intent.addCategory(Intent.CATEGORY_OPENABLE);  
                intent.setType("*/*");   

                    context.onActivityResult = function(requestCode,resultCode,intentData){  
                    console.log("选择文件的:",requestCode,resultCode,intentData);
                    if(requestCode == CODE_REQUEST){  
                        console.log("intentData:",requestCode);
                        if(intentData){  
                            console.log("uriValue:",intentData);
                            var uriValue = intentData.getData();  
                            plus.android.importClass(uriValue);  
                            var scheme = uriValue.getScheme();  
                            console.log("scheme:",scheme);
                            
                            if(scheme == 'content'){//还需要进行数据库查询，一般图片数据  
                                uni.showToast({  
                                    title: '',  
                                    icon: 'none'  
                                });
                                // var cursor = this.ip(context.getContentResolver()).query(uriValue,['_data'], null, null, null);  
                                //     console.log('cursor',cursor)
                                // if(cursor){  
                                //     this.ip(cursor).moveToFirst();  
                                //     var columnIndex = cursor.getColumnIndexOrThrow('_data');  
                                //     try{
                                //     var filePath = cursor.getString(columnIndex);  
                                //     _this.filePath = filePath;
                                
                                //     cursor.close();
                                //     //  _this.调用上传接口的方法(filePath, ‘文件类型');
                                //       console.log('cursor111')
                                //     }catch(e){
                                    
                                //     }
                                // }  
                            }else if(scheme == 'file'){  
                                console.log(uriValue)
                                window.uriValue=uriValue.getPath();
                                // this.handleFileUpload(uriValue.getPath())
                                // 路径 uriValue.getPath()
                                // console.log('file',uriValue)
                            }  
                        }else{  
                            uni.showToast({
                            title: '选择文件失败',
                            icon: 'none'
                            });
                        }  
                    }  
                }  
                context.startActivityForResult(intent,CODE_REQUEST);  
        },
        // 加载简历列表
        async loadResumeList() {
            try {
                // TODO: 替换为实际的API接口
                // const response = await this.$api.getResumeList()
                // this.resumeList = response.data

                // 模拟数据 - 可以设置为空数组来测试空状态
                const res =await filelate.ReumeAttachmentlist()
                if(res.code==200){
                    uni.hideLoading()
                    this.resumeList = res.data
                }else{
                    this.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('加载简历列表失败:', error)
                this.resumeList = []
            }
        },

        // 预览简历
        previewResume(item) {
            console.log('预览简历:', item)
            let lateitem = encodeURIComponent(JSON.stringify(item))
            uni.navigateTo({
                url: `/pages/user/resume/vaResume?path=${lateitem}` ,
            })
        },

        // 删除简历
         deleteResume(item, index) {
            uni.showModal({
                title: '确认删除',
                content: `确定要删除"${item.attachment_name}"吗？`,
                success: async(res) => {
                    if (res.confirm) {
                        const params={
                            id:item.id
                        }
                        let res = await filelate.delResumeAttachment(params)
                        if(res.code==200){
                            uni.showToast({
                                title: '删除成功',
                                icon: 'success'
                            })
                            uni.hideLoading()
                            this.loadResumeList()
                        }else{
                            uni.showToast({
                                title: res.msg,
                                icon: 'none'
                            })

                        }
                    }
                }
            })
        },

        // 上传简历
        uploadResume() {
            uni.chooseFile({
                count: 1,
                type: 'file',
                extension: ['.pdf', '.doc', '.docx'],
                success: (res) => {
                    console.log('选择文件:', res)
                    this.handleFileUpload(res.tempFiles[0])
                },
                fail: (err) => {
                    console.error('选择文件失败:', err)
                    uni.showToast({
                        title: '选择文件失败',
                        icon: 'none'
                    })
                }
            })
        },

        // 处理文件上传 - 支持二进制数据
        async handleFileUpload(file) {
            console.log('🚀 开始上传文件:', file)
            console.log('📄 文件类型:', typeof file)
            console.log('📊 文件大小:', file.size || 'unknown')

            uni.showLoading({
                title: '上传中...'
            })

            try {
                let uploadResult

                // 判断是否为包含二进制数据的文件对象
                if (file.data && file.name) {
                    console.log('📱 APP端二进制文件上传')
                    uploadResult = await this.uploadBinaryFileToServer(file)
                } else {
                    console.log('🌐 标准文件上传')
                    uploadResult = await this.uploadFileToServer(file)
                }

                if (uploadResult.success) {
                    const params = {
                        attachment_name: file.name,
                        storage_path: uploadResult.data.url
                    }

                    console.log('💾 保存简历参数:', params)

                    // 第二步：调用保存简历接口
                    const saveResult = await filelate.saveResumeAttachment(params)
                    console.log('✅ 保存简历结果:', saveResult)

                    if (saveResult.code == 200 || saveResult.success) {
                        // 两个接口都成功，更新列表
                        this.loadResumeList()
                        uni.hideLoading()
                        uni.showToast({
                            title: '上传成功',
                            icon: 'success'
                        })
                        this.fileList = [];
                    } else {
                        throw new Error(saveResult.message || '保存简历信息失败')
                    }
                } else {
                    throw new Error(uploadResult.message || '文件上传失败')
                }
            } catch (error) {
                console.error('❌ 上传失败:', error)
                uni.hideLoading()
                uni.showToast({
                    title: error.message || '上传失败',
                    icon: 'none'
                })
            }
        },

        // 上传二进制文件到服务器 - APP端专用
        uploadBinaryFileToServer(fileObject) {
            return new Promise((resolve, reject) => {
                console.log('🚀 开始二进制文件上传')
                console.log('📄 文件信息:', {
                    name: fileObject.name,
                    size: fileObject.size,
                    type: fileObject.type
                })

                // #ifdef APP-PLUS
                try {
                    // 将Java字节数组转换为JavaScript ArrayBuffer
                    const byteArray = fileObject.data
                    const arrayBuffer = new ArrayBuffer(byteArray.length)
                    const uint8Array = new Uint8Array(arrayBuffer)

                    for (let i = 0; i < byteArray.length; i++) {
                        uint8Array[i] = byteArray[i] & 0xFF // 确保是无符号字节
                    }

                    console.log('📊 转换后的ArrayBuffer大小:', arrayBuffer.byteLength)

                    // 创建FormData
                    const formData = new FormData()
                    const blob = new Blob([arrayBuffer], { type: 'application/pdf' })
                    formData.append('file', blob, fileObject.name)

                    // 使用XMLHttpRequest上传
                    const xhr = new XMLHttpRequest()

                    xhr.onload = function() {
                        console.log('📤 上传响应状态:', xhr.status)
                        console.log('📤 上传响应内容:', xhr.responseText)

                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText)
                                if (response.code === 200 || response.success) {
                                    resolve({
                                        success: true,
                                        data: response.data || response
                                    })
                                } else {
                                    reject(new Error(response.message || '上传失败'))
                                }
                            } catch (e) {
                                reject(new Error('响应解析失败'))
                            }
                        } else {
                            reject(new Error(`上传失败，状态码: ${xhr.status}`))
                        }
                    }

                    xhr.onerror = function() {
                        console.error('❌ 上传请求失败')
                        reject(new Error('网络请求失败'))
                    }

                    xhr.ontimeout = function() {
                        console.error('❌ 上传超时')
                        reject(new Error('上传超时'))
                    }

                    // 设置超时时间
                    xhr.timeout = 60000 // 60秒

                    // 发送请求
                    xhr.open('POST', 'http://8.130.152.121:82/jobapi/common/upload')
                    xhr.send(formData)

                } catch (error) {
                    console.error('❌ 二进制上传异常:', error)
                    reject(error)
                }
                // #endif

                // #ifdef H5
                reject(new Error('H5端不支持此方法'))
                // #endif
            })
        },

        // 上传文件到服务器
        uploadFileToServer(file) {
            return new Promise((resolve, reject) => {
                // #ifdef APP-PLUS
                uni.uploadFile({
                    url: 'http://8.130.152.121:82/jobapi/common/upload', // 文件上传接口
                    filePath: file.path,
                    name: 'file', // 服务器接收的字段名
                    formData: {
                        type: 5, // 文件类型标识
                        // 可以添加其他需要的参数
                    },
                    success: (res) => {
                        console.log('文件上传响应:', res)
                        try {
                            const result = JSON.parse(res.data)
                            console.log('上传结果:', result)
                            if (result.code==200 || result.success) {
                                resolve({
                                    success: true,
                                    data: result.data, // 包含文件URL等信息
                                    message: result.message
                                })
                            } else {
                                reject(new Error(result.message || '文件上传失败'))
                            }
                        } catch (parseError) {
                            console.error('解析上传响应失败:', parseError)
                            reject(new Error('服务器响应格式错误'))
                        }
                    },
                    fail: (error) => {
                        console.error('文件上传请求失败:', error)
                        reject(new Error('网络请求失败'))
                    }
                })
                 // #endif

                // #ifdef H5
                uni.uploadFile({
                    url: '/jobapi/common/upload', // 文件上传接口
                    filePath: file.path,
                    name: 'file', // 服务器接收的字段名
                    formData: {
                        type: 5, // 文件类型标识
                        // 可以添加其他需要的参数
                    },
                    success: (res) => {
                        console.log('文件上传响应:', res)
                        try {
                            const result = JSON.parse(res.data)
                            console.log('上传结果:', result)
                            if (result.code==200 || result.success) {
                                resolve({
                                    success: true,
                                    data: result.data, // 包含文件URL等信息
                                    message: result.message
                                })
                            } else {
                                reject(new Error(result.message || '文件上传失败'))
                            }
                        } catch (parseError) {
                            console.error('解析上传响应失败:', parseError)
                            reject(new Error('服务器响应格式错误'))
                        }
                    },
                    fail: (error) => {
                        console.error('文件上传请求失败:', error)
                        reject(new Error('网络请求失败'))
                    }
                })
                // #endif
            })
        },


        // 处理文件上传
        

        // 获取当前时间
        getCurrentTime() {
            const now = new Date()
            const year = now.getFullYear()
            const month = String(now.getMonth() + 1).padStart(2, '0')
            const day = String(now.getDate()).padStart(2, '0')
            return `${year}.${month}.${day}更新`
        },

        // 模拟数据
        getMockData() {
            // 返回有数据的情况
            return [
                {
                    id: 1,
                    fileName: '简历.pdf',
                    updateTime: '2025.05.09更新',
                    isDefault: true,
                    fileUrl: ''
                },
                {
                    id: 2,
                    fileName: '简历.pdf',
                    updateTime: '2025.05.09更新',
                    isDefault: false,
                    fileUrl: ''
                },
                {
                    id: 3,
                    fileName: '简历.pdf',
                    updateTime: '2025.05.09更新',
                    isDefault: false,
                    fileUrl: ''
                }
            ]

            // 如果要测试空状态，请注释上面的return，启用下面的return
            // return []
        }
    }
}
</script>

<style lang="scss" scoped>
.attachment-resume-page {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background-color: #fff;
}

/* 内容容器 */
.content-container {
    flex: 1;
    margin-top: 88rpx;
    padding: 30rpx;
}

/* 简历列表 */
.resume-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.resume-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.pdf-icon {
    margin-right: 24rpx;
}

.file-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.file-name {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.name-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.default-tag {
    background-color: #14B19E;
    color: #fff;
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 8rpx;
}

.update-time {
    font-size: 24rpx;
    color: #999;
}

.action-buttons {
    display: flex;
    gap: 24rpx;
}

.action-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 12rpx;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 60rpx;
    text-align: center;
}

.empty-icon {
    margin-bottom: 40rpx;
}

.empty-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.empty-desc {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60rpx;
}

::v-deep .files-button {
    background-color: #14B19E;
    border-radius: 50rpx;
    padding: 24rpx 48rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
    justify-content: center;
    color: #fff;
}

.upload-text {
    font-size: 28rpx;
    color: #fff;
    font-weight: 500;
}

/* 底部上传按钮 */
.bottom-upload {
    position: fixed;
    bottom: 60rpx;
    left: 30rpx;
    right: 30rpx;
    z-index: 100;
}

.bottom-upload .upload-btn {
    // width: 100%;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(20, 177, 158, 0.3);
}
</style>
