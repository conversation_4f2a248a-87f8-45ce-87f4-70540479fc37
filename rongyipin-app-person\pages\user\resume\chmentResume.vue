<template>
    <view class="attachment-resume-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="附件简历" :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'"
                safeAreaInsetTop placeholder fixed>
                <template #right>
                    <u-icon name="more-dot-fill" size="20" color="#333"></u-icon>
                </template>
            </u-navbar>
        </view>

        <!-- 内容区域 -->
        <view class="content-container">
            <!-- 有数据时显示列表 -->
            <view v-if="resumeList.length > 0" class="resume-list">
                <view v-for="(item, index) in resumeList" :key="item.id" class="resume-item">
                    <!-- PDF图标 -->
                    <view class="pdf-icon">
                        <u-icon name="file-text" size="40" color="#FF6B35"></u-icon>
                    </view>

                    <!-- 文件信息 -->
                    <view class="file-info">
                        <view class="file-name">
                            <text class="name-text">{{ item.attachment_name }}</text>
                            <text v-if="item.isDefault" class="default-tag">默认</text>
                        </view>
                        <text class="update-time">{{ item.update_time }}</text>
                    </view>

                    <!-- 操作按钮 -->
                    <view class="action-buttons">
                        <view class="action-btn" @click="previewResume(item)">
                            <u-icon name="eye" size="30" color="#666"></u-icon>
                        </view>
                        <view class="action-btn" @click="deleteResume(item, index)">
                            <u-icon name="trash" size="30" color="#666"></u-icon>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 无数据时显示空状态 -->
            <view v-else class="empty-state">
                <view class="empty-icon">
                    <u-icon name="file-text" size="80" color="#ccc"></u-icon>
                </view>
                <text class="empty-text">暂无附件简历</text>
                <text class="empty-desc">您还没有上传任何简历文件</text>
                <bgyxFilepickerVue style="" @upload="upload" filename="file"
                    url="http://*************:82/jobapi/common/upload">
                     <button style="width: 90%;border-radius: 20rpx;background-color: #14B19E;color: white;">上传</button>
                </bgyxFilepickerVue>
                <!-- <uni-file-picker
                    v-model="fileList"
                    
                    fileMediatype="file"
                    file-extname="pdf"
                    limit="1"
                    @select="onSelect"
                    @success="onSuccess"
                    >上传简历</uni-file-picker> -->
                <!-- <view class="upload-btn" @click="uploadResume">
                    <text class="upload-text">上传简历</text>
                </view> -->
            </view>
        </view>

        <!-- 底部上传按钮 -->
        <view v-if="resumeList.length > 0" class="bottom-upload">
            <view>
                <bgyxFilepickerVue style="" @upload="upload" filename="file"
                    url="http://*************:82/jobapi/common/upload">
                    <button style="width: 90%;border-radius: 20rpx;background-color: #14B19E;color: white;">上传</button>
                </bgyxFilepickerVue>
            </view>
        </view>
    </view>
</template>

<script>
import { filelate } from "@/utils/api.js"
import bgyxFilepickerVue from '../../../components/bgyx-filepicker/bgyx-filepicker.vue';
export default {
    components: {
        bgyxFilepickerVue
    },

    data() {
        return {
            resumeList: [], // 简历列表
            fileList: []
        }
    },
    onLoad() {
        this.loadResumeList()
    },
    onShow() {

        if (uni.getStorageSync('uriValue')) {
            const uriValue = uni.getStorageSync('uriValue');
            console.log('uriValu', uriValue)
            this.handleFileUpload(uriValue);
        }
    },
    methods: {
        async upload(e) {
            console.log(e?.data.data.url);
            const params = {
                attachment_name: e?.data.data.name,
                storage_path: e?.data.data.url
            }
            // 第二步：调用保存简历接口
            const saveResult = await filelate.saveResumeAttachment(params)
            console.log('保存简历结果:', saveResult)

            if (saveResult.code == 200 || saveResult.success) {
                // 两个接口都成功，更新列表
                this.loadResumeList()
                uni.hideLoading()
                uni.showToast({
                    title: '上传成功',
                    icon: 'success'
                })
                this.fileList = [];
            } else {
                throw new Error(saveResult.message || '保存简历信息失败')
            }
        },
        // 文件选择回调

        // 加载简历列表
        async loadResumeList() {
            try {
                // TODO: 替换为实际的API接口
                // const response = await this.$api.getResumeList()
                // this.resumeList = response.data

                // 模拟数据 - 可以设置为空数组来测试空状态
                const res = await filelate.ReumeAttachmentlist()
                if (res.code == 200) {
                    uni.hideLoading()
                    this.resumeList = res.data
                } else {
                    this.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('加载简历列表失败:', error)
                this.resumeList = []
            }
        },

        // 预览简历
        previewResume(item) {
            uni.navigateTo({
                url: `/pages/user/resume/vaResume?id=${item.id}`,
            })
        },

        // 删除简历
        deleteResume(item, index) {
            uni.showModal({
                title: '确认删除',
                content: `确定要删除"${item.attachment_name}"吗？`,
                success: async (res) => {
                    if (res.confirm) {
                        const params = {
                            id: item.id
                        }
                        let res = await filelate.delResumeAttachment(params)
                        if (res.code == 200) {
                            uni.showToast({
                                title: '删除成功',
                                icon: 'success'
                            })
                            uni.hideLoading()
                            this.loadResumeList()
                        } else {
                            uni.showToast({
                                title: res.msg,
                                icon: 'none'
                            })

                        }
                    }
                }
            })
        },

        // 上传简历
        uploadResume() {
            uni.chooseFile({
                count: 1,
                type: 'file',
                extension: ['.pdf', '.doc', '.docx'],
                success: (res) => {
                    console.log('选择文件:', res)
                    this.handleFileUpload(res.tempFiles[0])
                },
                fail: (err) => {
                    console.error('选择文件失败:', err)
                    uni.showToast({
                        title: '选择文件失败',
                        icon: 'none'
                    })
                }
            })
        },


        // 处理文件上传


        // 获取当前时间
        getCurrentTime() {
            const now = new Date()
            const year = now.getFullYear()
            const month = String(now.getMonth() + 1).padStart(2, '0')
            const day = String(now.getDate()).padStart(2, '0')
            return `${year}.${month}.${day}更新`
        },
    }
}
</script>

<style lang="scss" scoped>
.attachment-resume-page {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.navbar {
    
}

/* 内容容器 */
.content-container {
    flex: 1;
    // margin-top: 88rpx;
    padding: 30rpx;
}

/* 简历列表 */
.resume-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.resume-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.pdf-icon {
    margin-right: 24rpx;
}

.file-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.file-name {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.name-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.default-tag {
    background-color: #14B19E;
    color: #fff;
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 8rpx;
}

.update-time {
    font-size: 24rpx;
    color: #999;
}

.action-buttons {
    display: flex;
    gap: 24rpx;
}

.action-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 12rpx;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 60rpx;
    text-align: center;
}

.empty-icon {
    margin-bottom: 40rpx;
}

.empty-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.empty-desc {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60rpx;
}

::v-deep .files-button {
    background-color: #14B19E;
    border-radius: 50rpx;
    padding: 24rpx 48rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
    justify-content: center;
    color: #fff;
}

.upload-text {
    font-size: 28rpx;
    color: #fff;
    font-weight: 500;
}

/* 底部上传按钮 */
.bottom-upload {
    position: fixed;
    bottom: 60rpx;
    left: 30rpx;
    right: 30rpx;
    z-index: 100;
}

.bottom-upload .upload-btn {
    // width: 100%;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(20, 177, 158, 0.3);
}
</style>
