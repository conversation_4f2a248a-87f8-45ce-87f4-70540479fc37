// 应用渠道表
{
	"bsonType": "object",
	"description": "提供渠道和场景值数据",
	"required": [],
	"permission": {
		"read": "'READ_UNI_STAT_APP_CHANNELS' in auth.permission",
		"create": false,
		"update": false,
		"delete": false
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"appid": {
			"bsonType": "string",
			"description": "统计应用ID，对应opendb-app-list.appid",
			"foreignKey": "opendb-app-list.appid"
		},
		"platform_id": {
			"bsonType": "string",
			"description": "应用平台，对应uni-stat-app-platforms._id",
			"foreignKey": "uni-stat-app-platforms._id"
		},
		"channel_code": {
			"bsonType": "int",
			"description": "客户端上报的渠道代码"
		},
		"channel_name": {
			"bsonType": "string",
			"description": "渠道名称，用户可编辑"
		},
		"create_time": {
			"bsonType": "timestamp",
			"description": "创建时间"
		},
		"last_modify_time": {
			"bsonType": "timestamp",
			"description": "最后修改时间"
		}
	}
}

