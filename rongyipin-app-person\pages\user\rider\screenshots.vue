<template>
    <view class="upload-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="上传骑手平台详细信息截图" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 上传区域 -->
        <view class="upload-container">
            <view class="upload-area" @click="chooseImage">
                <view class="upload-box">
                    <image v-if="imageUrl" :src="imageUrl" class="uploaded-image" mode="aspectFit"></image>
                    <view v-else class="upload-placeholder">
                        <view class="plus-icon">+</view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部完成按钮 -->
        <view class="bottom-section">
            <view class="complete-btn" :class="{ 'active': imageUrl }" @click="handleComplete">
                <text class="btn-text">完成</text>
            </view>
        </view>

        <!-- 成功弹窗 -->
        <view v-if="showSuccessModal" class="modal-overlay" @click="closeModal">
            <view class="success-modal" @click.stop>
                <view class="success-icon">
                    <view class="check-icon">✓</view>
                </view>
                <text class="success-title">信息提交成功</text>
                <text class="success-desc">将在2个工作日内审核完成</text>
            </view>
        </view>
    </view>
</template>

<script>
import { rider } from "@/utils/api"
export default {
    data() {
        return {
            imageUrl: '',
            showSuccessModal: false,
            imageConver1: '',
            formDataList: null,
        }
    },
    onLoad(options) {
        if (options.list) {
            this.formDataList = JSON.parse(decodeURIComponent(options.list))
            console.log(this.formDataList)
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack()
        },

        // 选择图片
        chooseImage() {
            uni.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'],
                sourceType: ['album', 'camera'],
                success: (res) => {
                    console.log(res)
                    this.imageUrl = res.tempFilePaths[0]
                    console.log(this.imageUrl)
                    this.uploadImage(this.imageUrl)
                },
                fail: (err) => {
                    console.log('选择图片失败:', err)
                }
            })
        },
        
        uploadImage(filePath) { 
            console.log('选择的图片路径:', filePath)
            uni.showLoading({ title: '上传中' })
            // #ifdef APP-PLUS
            uni.uploadFile({
                url: 'http://8.130.152.121:82/jobapi/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 4
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver1 = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif

            // #ifdef H5
            uni.uploadFile({
                url: '/jobapi/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 4
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver1 = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif
        },
        // 完成上传
        async handleComplete() {
            if (!this.imageUrl) {
                uni.showToast({
                    title: '请先上传截图',
                    icon: 'none'
                })
                return 
            }
            console.log('111', this.formDataList)
            const params ={
                username:this.formDataList.name,
                platform :this.formDataList.platform,
                platform_number:this.formDataList.account,
                city_id:this.formDataList.selectedAddress,
                register_time:this.formDataList.registerTime,
                pic:this.imageConver1
            }
            const res =await rider.riderApply(params)
            if(res.code  ==200){
                this.showSuccessModal = true
            }else{
                 uni.showToast({
                    title: res.msg,
                    icon: 'none'
                })
            }
            // 显示成功弹窗
            setTimeout(() => {
                this.closeModal()
            }, 2000)
        },

        // 关闭弹窗
        closeModal() {
            this.showSuccessModal = false
            // 可以在这里跳转到其他页面或返回
            setTimeout(() => {
                uni.navigateBack({
                    delta: 2
                })
            }, 500)
        }
    }
}
</script>

<style lang="scss" scoped>
.upload-page {
    min-height: 100vh;
    background-color: white;
    position: relative;
}

/* 顶部导航栏 */
.navbar {}

/* 上传容器 */
.upload-container {
    margin-top: 8rpx;
    // padding: 100rpx 60rpx;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

/* 上传区域 */
.upload-area {
    width: 100%;
    display: flex;
    justify-content: center;
}

.upload-box {
    width: 400rpx;
    height: 400rpx;
    border: 2rpx dashed #ccc;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    position: relative;
}

.upload-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.plus-icon {
    font-size: 320rpx;
    color: #ccc;
    font-weight: 300;
}

.uploaded-image {
    width: 100%;
    height: 100%;
    border-radius: 10rpx;
}

/* 底部区域 */
.bottom-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
}

/* 完成按钮 */
.complete-btn {
    background-color: #999;
    border-radius: 8rpx;
    padding: 28rpx;
    text-align: center;
    transition: background-color 0.3s ease;

    .btn-text {
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
    }

    /* 激活状态 */
    &.active {
        background-color: #14B19E;
    }
}

/* 弹窗遮罩 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

/* 成功弹窗 */
.success-modal {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 60rpx 40rpx 40rpx;
    margin: 0 60rpx;
    text-align: center;
    min-width: 500rpx;
}

.success-icon {
    margin-bottom: 30rpx;
    display: flex;
    justify-content: center;
}

.check-icon {
    width: 80rpx;
    height: 80rpx;
    background-color: #14B19E;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 40rpx;
    color: #fff;
    font-weight: bold;
}

.success-title {
    display: block;
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 20rpx;
}

.success-desc {
    display: block;
    font-size: 28rpx;
    color: #666;
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>
