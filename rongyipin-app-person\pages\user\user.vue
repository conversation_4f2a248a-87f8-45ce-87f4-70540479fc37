<template>
	<view class="profile-container">
		<!-- 1. 顶部操作栏 -->
		<view class="top-bar">
			<image class="top-icon" src="@/static/app/my/antOutline.png" mode="widthFix"></image>
			<image @click="toseting()" class="top-icon" src="@/static/app/my/setting.png" mode="widthFix"></image>
		</view>

		<!-- 2. 用户信息 -->
		<view class="user-info-card">
			<image class="avatar" :src="userInfo.avatarUrl" mode="aspectFill"></image>
			<view class="user-details">
				<text class="user-name"><text>{{ userInfo.username }} </text>
					<text v-if="userInfo.is_real == 0" @click="toRealName()"
						style="margin-left: 50rpx;font-size: 25rpx; background: red;border-radius: 30rpx;padding: 5rpx 10rpx;">去实名</text>
					<text v-else
						style="margin-left: 50rpx;font-size: 25rpx; background: greenyellow;border-radius: 30rpx;padding: 5rpx 10rpx;">已实名</text>
				</text>
				<text class="user-meta">{{ age }}</text>
			</view>
			<uni-icons type="right" size="20" @click="touserinfo()"></uni-icons>
		</view>

		<!-- 3. 统计数据 -->
		<view class="stats-section">
			<view class="stat-item" @click="anAuditionCount()">
				<text class="stat-number">{{DataCount.anAuditionCount}}</text>
				<text class="stat-label">已投简历</text>
			</view>
			<view class="stat-item" @click="collectCount()">
				<text class="stat-number">{{DataCount.collectCount}}</text>
				<text class="stat-label">收藏</text>
			</view>
			<view class="stat-item" @click="jobApplyCount()">
				<text class="stat-number">{{DataCount.jobApplyCount}}</text>
				<text class="stat-label">沟通过</text>
			</view>
			<view class="stat-item" @click="toTraceList()">
				<text class="stat-number">{{DataCount.traceCount}}</text>
				<text class="stat-label">我看过</text>
			</view>
		</view>

		<!-- 4. 简历管理 (黑色卡片) -->
		<view class="management-card">
			<view class="manage-item" @click="handresume('onlineResume')">
				<image class="manage-icon" src="@/static/app/my/resume.png" mode="aspectFit"></image>
				<text class="manage-label">在线简历</text>
			</view>
			<view class="manage-item" @click="handresume('resume')">
				<image class="manage-icon" src="@/static/app/my/chment.png" mode="aspectFit"></image>
				<text class="manage-label">附件简历</text>
			</view>
			<view class="manage-item" @click="jobIntention">
				<image class="manage-icon" src="@/static/app/my/jobSearch.png" mode="aspectFit"></image>
				<text class="manage-label">求职意向</text>
			</view>
			<view class="manage-item">
				<image class="manage-icon" src="@/static/app/my/privacy.png" mode="aspectFit"></image>
				<text class="manage-label">求职隐私</text>
			</view>
		</view>

		<!-- 5. 简历刷新横幅 -->
		<view class="refresh-banner">
			<text class="banner-text">简历刷新 | 手动刷新, 增加简历曝光量, 提升简历排名</text>
			<!-- <view class="banner-button">更多套餐</view> -->
		</view>

		<!-- 6. 积分与刷新操作 (绿色卡片) -->
		<view class="actions-row">
			<view class="action-card" @click="refreshJL">
				<image class="manage-icon"  :class="{ rotate: isRotating }" src="@/static/app/my/refresh.png" mode="aspectFit"></image>
				<text class="action-text">立即刷新</text>
				<image class="action-count-icon" src="@/static/app/my/integral.png" mode="widthFix"></image>
				<text class="action-count">1</text>
			</view>
			<view class="action-card" @click="handotherClick('integral')">
				<text class="action-text">剩余积分</text>
				<text class="action-text-sub">(去充值)</text>
				<image class="action-count-icon" src="@/static/app/my/integral.png" mode="widthFix"></image>
				<text class="action-count">100</text>
			</view>
		</view>

		<!-- 7. 主要功能 (白色卡片 1) -->
		<view class="card-wrapper">
			<view class="main-actions-card">
				<view class="func-item" @click="handleItemClick('position')">
					<image class="func-icon" src="@/static/app/my/rider.png" mode="widthFix"></image>
					<text class="func-label">骑手认证</text>
				</view>
				<view class="func-item" @click="handleItemClick('recommend')">
					<image class="func-icon" src="@/static/app/my/recommend.png" mode="widthFix"></image>
					<text class="func-label">我要推荐</text>
				</view>
				<view class="func-item" @click="handleItemClick('recommendList')">
					<image class="func-icon" src="@/static/app/my/repoenList.png" mode="widthFix"></image>
					<text class="func-label">推荐列表</text>
				</view>
			</view>
		</view>

		<!-- 8. 其他功能 (白色卡片 2) -->
		<view class="card-wrapper">
			<view class="other-functions-card">
				<view class="card-title">其他功能</view>
				<view class="functions-grid">
					<view class="func-grid-item" @click="handotherClick('invoicing')">
						<image class="func-icon-grid" src="@/static/app/my/invoicing.png" mode="widthFix"></image>
						<text class="func-label-grid">我要开票</text>
					</view>
					<view class="func-grid-item" @click="handotherClick('feedback')">
						<image class="func-icon-grid" src="@/static/app/my/feedback.png" mode="widthFix"></image>
						<text class="func-label-grid">建议反馈</text>
					</view>
					<view class="func-grid-item" @click="handotherClick('collection')">
						<image class="func-icon-grid" src="@/static/app/my/collection.png" mode="widthFix"></image>
						<text class="func-label-grid">我的收藏</text>
					</view>
					<view class="func-grid-item">
						<image class="func-icon-grid" src="@/static/app/my/customer.png" mode="widthFix"></image>
						<text class="func-label-grid">我的客服</text>
					</view>
					<view class="func-grid-item" @click="handotherClick('integral')">
						<image class="func-icon-grid" src="@/static/app/my/integration.png" mode="widthFix"></image>
						<text class="func-label-grid">积分中心</text>
					</view>
					<view class="func-grid-item" @click="handotherClick('realName')">
						<image class="func-icon-grid" src="@/static/app/my/real_name.png" mode="widthFix"></image>
						<text class="func-label-grid">实名认证</text>
					</view>
					<view class="func-grid-item" @click="handotherClick('privacy')">
						<image class="func-icon-grid" src="@/static/app/my/PrivacyPolicy.png" mode="widthFix"></image>
						<text class="func-label-grid">隐私政策</text>
					</view>
					<view class="func-grid-item" @click="InterviewList">
						<image class="func-icon-grid" src="@/static/app/my/PrivacyPolicy.png" mode="widthFix"></image>
						<text class="func-label-grid">我的面试</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 9. 页面页脚 -->
		<view class="footer">
			<text class="footer-text">客服电话 0310-5566110 工作时间 09:00-18:00</text>
			<text class="footer-text">人力资源许可证 营业执照</text>
			<text class="footer-text">冀ICP备案14013428号-9A</text>
		</view>

	</view>
</template>

<script>
import { rider,userApi } from '@/utils/api.js'
import { data } from 'uview-ui/libs/mixin/mixin'
export default {
	data() {
		return {
			// 页面数据可在此定义
			userInfo: {},
			DataCount:{},
			isRotating:false,
		}
	},
	computed: {
		age(){
			return new Date().getFullYear() - this.userInfo.birthday.substring(0, 4)
		}
	},
	onShow() {
		// 页面加载时执行
		this.userInfo = uni.getStorageSync('userInfo')
		console.log(this.userInfo)
		this.UserDataCount();
	},

	methods: {
		//刷新简历
		refreshJL(){
			this.isRotating = true; // 触发旋转动画
			setTimeout(() => { // 动画完成后重置状态，避免重复点击时多次触发动画
				this.isRotating = false;
				uni.showToast({ title: '刷新成功', icon: '' });
			}, 500); // 持续时间与CSS动画匹配
		},
		//我看过list
		toTraceList(){
			uni.navigateTo({
				url: '/pages/user/usercontont/TraceList'
			})
		},
		//沟通过list
		jobApplyCount(){
			uni.navigateTo({
				url: '/pages/user/usercontont/jobApplyCount'
			})
		},
		//收藏list
		collectCount(){
			uni.navigateTo({
				url: '/pages/user/usercontont/collectCount'
			})
		},
		//已投简历list
		anAuditionCount(){
			uni.navigateTo({
				url: '/pages/user/usercontont/anAuditionCount'
			})
		},
		//统计数据
		UserDataCount(){
			userApi.UserDataCount().then(res => {
				uni.hideLoading();
				this.DataCount = res.data
				console.log(res,'统计数据')
			}).catch(err => {
				console.log(err)
			})
		},

		InterviewList(){
			uni.navigateTo({
				url: '/pages/user/usercontont/InterviewList'
			})
		},
		touserinfo() {
			uni.navigateTo({
				url: '/pages/user/userinfo/index'
			})
		},
		handresume(type) {
			switch (type) {
				case 'resume':
					uni.navigateTo({
						url: '/pages/user/resume/chmentResume'
					})
					break;
				case 'onlineResume':
					uni.navigateTo({
						url: '/pages/user/resume/onlineResume'
					})
					break;
				default:
					eventType = 'text';
					messageContent = content;
			}
		},
		//求职意向
		jobIntention(){
			uni.navigateTo({
				url: '/pages/index/addzhiwei?last=user'
			})
		},
		async handleItemClick(type) {
			switch (type) {
				case 'position':
					const res = await rider.riderApplyList()
					console.log(res)
					if (res.code == 200) {
						if (res.data == '') {
							uni.navigateTo({
								url: '/pages/user/rider/authentication'
							})
						} else {
							if (res.data.audit_status == 0) {
								uni.navigateTo({
									url: '/pages/user/rider/cationNull'
								})
							} else if (res.data.audit_status == 1) {
								uni.navigateTo({
									url: '/pages/user/rider/cationsucces'
								})
							} else if (res.data.audit_status == 2) {
								uni.navigateTo({
									url: `/pages/user/rider/cationreso?reason=${res.data.audit_reason}`
								})
							}
						}
						uni.hideLoading();
					} else {

						uni.showToast({
							title: res.msg,
							icon: 'none'
						});
						uni.hideLoading();
					}
					break;
				case 'recommend':
					uni.navigateTo({
						url: '/pages/user/rider/recommend'
					})
					break;
				case 'recommendList':
					uni.navigateTo({
						url: '/pages/user/rider/recommendList'
					})
					break;
				default:
					eventType = 'text';
					messageContent = content;
			}
		},
		handotherClick(type) {
			switch (type) {
				case 'invoicing':
					uni.navigateTo({
						url: '/pages/user/other/invoicing'
					})
					break;
				case 'feedback':
					uni.navigateTo({
						url: '/pages/user/other/feedback'
					})
					break;
				case 'collection':
					uni.navigateTo({
						url: '/pages/user/other/collection'
					})
					break;
				case 'realName':
					this.realName();
					break;
				case 'privacy':
					uni.navigateTo({
						url: '/pages/user/privacy'
					})
					break;
				case 'integral':
					uni.navigateTo({
						url: '/pages/user/other/integral'
					})
					break;
				default:
					eventType = 'text';
					messageContent = content;
			}
		},
		realName(){
			if(this.userInfo.is_real == 0){
				this.toRealName();
			}else{
				uni.showToast({
					title: '已实名认证',
					icon: 'none'
				})
			}
		},
		toRealName() {
			console.log('111')
			uni.navigateTo({
				url: './realName'
			})
		},
		toMyPoints() {
			console.log('111')
			uni.navigateTo({
				url: './usercontont/myPoints'
			})
		},
		toseting() {
			console.log('111')
			uni.navigateTo({
				url: './setting/index'
			})
		}
	}
}
</script>

<style lang="scss">
// 全局页面背景色
page {
	background-color: #e9f8f4;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
}

.profile-container {
	padding: 0 30rpx 40rpx;
	box-sizing: border-box;
}

// 1. 顶部操作栏
.top-bar {
	display: flex;
	// justify-content: space-between;
	justify-content: flex-end;
	align-items: center;
	padding: 60rpx 0 5rpx;
	height: 100rpx; // 模拟导航栏高度
	box-sizing: border-box;

	.top-icon {
		width: 44rpx;
		height: 44rpx;
		margin-left: 30rpx;
	}
}

// 2. 用户信息
.user-info-card {
	display: flex;
	align-items: center;
	padding: 20rpx 0;

	.avatar {
		width: 110rpx;
		height: 110rpx;
		border-radius: 50%;
		margin-right: 25rpx;
		background-color: #eee;
	}

	.user-details {
		display: flex;
		flex-direction: column;
		flex: 1;

		.user-name {
			font-size: 38rpx;
			font-weight: bold;
			color: #333;
		}

		.user-meta {
			font-size: 26rpx;
			color: #555;
			margin-top: 8rpx;
		}
	}

	.arrow-icon {
		width: 24rpx;
		height: 24rpx;
	}
}

// 3. 统计数据
.stats-section {
	display: flex;
	justify-content: space-around;
	padding: 30rpx 0;

	.stat-item {
		display: flex;
		flex-direction: column;
		align-items: center;

		.stat-number {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}

		.stat-label {
			font-size: 24rpx;
			color: #666;
			margin-top: 8rpx;
		}
	}
}

// 4. 简历管理 (黑色卡片)
.management-card {
	display: flex;
	justify-content: space-around;
	align-items: center;
	background-color: #1a1a1a;
	border-radius: 20rpx;
	padding: 30rpx 10rpx;
	margin-top: 10rpx;

	.manage-item {
		display: flex;
		flex-direction: column;
		align-items: center;

		.manage-icon {
			width: 56rpx;
			height: 56rpx;
			display: block;
		}

		.manage-label {
			color: #fff;
			font-size: 24rpx;
			margin-top: 15rpx;
		}
	}
}

// 5. 简历刷新横幅
.refresh-banner {
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #343434;
	border-radius: 40rpx;
	margin-top: 30rpx;
	padding: 12rpx 15rpx 12rpx 30rpx;

	.banner-text {
		color: #f0e1a5;
		font-size: 26rpx;
		white-space: nowrap;
	}

	.banner-button {
		background: #3c5dff;
		color: #fff;
		font-size: 24rpx;
		padding: 12rpx 25rpx;
		border-radius: 30rpx;
		font-weight: 500;
		white-space: nowrap;
	}
}

@keyframes rotate360 {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
 
.rotate {
  animation: rotate360 0.5s linear;
}

// 6. 积分与刷新操作 (绿色卡片)
.actions-row {
	display: flex;
	justify-content: space-between;
	margin-top: 30rpx;
	gap: 20rpx;

	// #refresh-icon {
	// 	transition: transform 0.5s; /* 平滑过渡效果 */
	// }	
	.action-card {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #e6f7b1;
		border-radius: 20rpx;
		padding: 25rpx 15rpx;
		font-weight: 500;

		.manage-icon {
			width: 32rpx !important;
			height: 32rpx !important;
			margin-right: 10rpx;
		}

		.action-text {
			font-size: 28rpx;
			color: #333;
		}

		.action-text-sub {
			font-size: 28rpx;
			color: #6b7a2d;
			margin-left: 4rpx;
		}

		.action-count-icon {
			width: 32rpx;
			height: 32rpx;
			margin-left: 10rpx;
		}

		.action-count {
			font-size: 28rpx;
			color: #333;
			margin-left: 5rpx;
		}
	}
}

// 7 & 8. 白色卡片通用样式
.card-wrapper {
	background-color: #fff;
	border-radius: 20rpx;
	padding: 30rpx;
	margin-top: 30rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.04);
}

// 7. 主要功能
.main-actions-card {
	display: flex;
	justify-content: space-around;

	.func-item {
		display: flex;
		flex-direction: column;
		align-items: center;

		.func-icon {
			width: 56rpx;
			height: 56rpx;
		}

		.func-label {
			font-size: 26rpx;
			color: #333;
			margin-top: 15rpx;
		}
	}
}

// 8. 其他功能
.other-functions-card {
	.card-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 30rpx;
	}

	.functions-grid {
		display: flex;
		flex-wrap: wrap;

		.func-grid-item {
			width: 25%;
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-bottom: 30rpx;

			.func-icon-grid {
				width: 52rpx;
				height: 52rpx;
			}

			.func-label-grid {
				font-size: 24rpx;
				color: #333;
				margin-top: 15rpx;
			}
		}
	}
}

// 9. 页面页脚
.footer {
	padding: 50rpx 0 20rpx;
	text-align: center;

	.footer-text {
		display: block;
		font-size: 22rpx;
		color: #aaa;
		line-height: 1.6;
	}
}
</style>