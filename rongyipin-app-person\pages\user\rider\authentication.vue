<template>
    <view class="rider-auth-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="骑手认证" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 表单内容 -->
        <view class="form-container">
            <!-- 您的姓名 -->
            <view class="form-item">
                <view class="label-row">
                    <text class="required-star">*</text>
                    <text class="label-text">您的姓名</text>
                </view>
                <input class="input-field" type="text" placeholder="请输入" v-model="formData.name" />
            </view>

            <!-- 平台 -->
            <view class="form-item">
                <view class="label-row">
                    <text class="required-star">*</text>
                    <text class="label-text">平台</text>
                </view>
                <input class="input-field" type="text" placeholder="请输入" v-model="formData.platform" />
            </view>

            <!-- 平台登录账号 -->
            <view class="form-item">
                <view class="label-row">
                    <text class="required-star">*</text>
                    <text class="label-text">平台登录账号</text>
                </view>
                <input class="input-field" type="text" placeholder="请输入" v-model="formData.account" />
            </view>

            <!-- 注册时间 -->
            <view class="form-item">
                <view class="label-row">
                    <text class="required-star">*</text>
                    <text class="label-text">注册时间</text>
                </view>
                <!-- <input class="input-field" type="text" placeholder="请输入" v-model="formData.registerTime" /> -->
                <uni-datetime-picker type="date" :clear-icon="false" v-model="formData.registerTime"
                    @maskClick="maskClick" />
            </view>

            <!-- 工作城市 -->
            <view class="form-item">
                <view class="label-row">
                    <text class="required-star">*</text>
                    <text class="label-text">工作城市</text>
                </view>
                <view class="address-picker" @click="showAddressPicker">
                    <text>{{ formData.workCity || '请选择工作地址' }}</text>
                    <uni-icons type="right" size="16" color="#666"></uni-icons>
                </view>
            </view>
        </view>

        <!-- 底部提示和按钮 -->
        <view class="bottom-section">
            <view class="help-link">
                <text class="help-text">遇到了问题？联系客服</text>
            </view>

            <view class="submit-btn" :class="{ 'active': isFormComplete }" @click="handleSubmit">
                <text class="btn-text">下一步</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            formData: {
                name: '',
                platform: '',
                account: '',
                registerTime: '',
                workCity: '',
                selectedAddress: ''
            }
        }
    },

    computed: {
        // 判断表单是否完整
        isFormComplete() {
            return this.formData.name.trim() !== '' &&
                this.formData.platform.trim() !== '' &&
                this.formData.account.trim() !== '' &&
                this.formData.registerTime.trim() !== '' &&
                this.formData.workCity.trim() !== ''
        }
    },
    onShow() {
        // 监听城市选择事件
        uni.$on('citySelected', this.handleCitySelected)
    },
    methods: {
        maskClick(e) {
            console.log('maskClick事件:', e);
        },
        handleCitySelected(selectedData) {
            this.formData.workCity = selectedData.city.name
            this.formData.selectedAddress = selectedData.city.id
        },
        // 返回上一页
        goBack() {
            uni.navigateBack()
        },

        // 提交表单
        handleSubmit() {
            // 验证必填字段
            if (!this.formData.name) {
                uni.showToast({
                    title: '请输入您的姓名',
                    icon: 'none'
                })
                return
            }

            if (!this.formData.platform) {
                uni.showToast({
                    title: '请输入平台',
                    icon: 'none'
                })
                return
            }

            if (!this.formData.account) {
                uni.showToast({
                    title: '请输入平台登录账号',
                    icon: 'none'
                })
                return
            }

            if (!this.formData.registerTime) {
                uni.showToast({
                    title: '请输入注册时间',
                    icon: 'none'
                })
                return
            }

            if (!this.formData.workCity) {
                uni.showToast({
                    title: '请输入工作城市',
                    icon: 'none'
                })
                return
            }

            // 提交数据
            console.log('提交表单数据:', this.formData)
            const userInfo = uni.getStorageSync('userInfo')
            if (userInfo.is_real == 1) {
                const dataStr = encodeURIComponent(JSON.stringify(this.formData))
                setTimeout(() => {
                    uni.navigateTo({
                        url: `./screenshots?list=${dataStr}`
                    })
                }, 500)
            } else {
                uni.showModal({
                    title: '',
                    content: '您还没有完成实名认证\n请先完成实名认证',
                    showCancel: false,
                    confirmText: '去认证',
                    confirmColor: '#3C9BFF',
                    success: (res) => {
                        if (res.confirm) {
                            // 跳转到实名认证页面
                            uni.navigateTo({
                                url: '/pages/user/realName'
                            })
                        }
                    }
                })
            }

        },
        //选择地址
        showAddressPicker() {
            uni.navigateTo({
                url: './workcity'

            })
        }
    }
}
</script>

<style lang="scss" scoped>
.rider-auth-page {
    min-height: 100vh;
    background-color: #f8f8f8;
}

/* 顶部导航栏 */
.navbar {
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // height: 88rpx;
    // background-color: #fff;
    // padding: 0 30rpx;
    // position: fixed;
    // top: 0;
    // left: 0;
    // right: 0;
    // z-index: 999;
    // border-bottom: 1rpx solid #f0f0f0;

    // .nav-left,
    // .nav-right {
    //     width: 60rpx;
    //     height: 60rpx;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    // }

    // .nav-title {
    //     flex: 1;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     gap: 8rpx;

    //     .warning-icon {
    //         font-size: 32rpx;
    //     }

    //     .title-text {
    //         font-size: 32rpx;
    //         font-weight: 500;
    //         color: #333;
    //     }
    // }
}

/* 表单容器 */
.form-container {
    margin-top: 38rpx;
    padding: 0 15rpx;
}

/* 表单项 */
.form-item {
    background-color: #fff;
    margin-bottom: 2rpx;
    padding: 0 30rpx;

    .label-row {
        display: flex;
        align-items: center;
        padding: 30rpx 0 20rpx 0;

        .required-star {
            color: #ff4757;
            font-size: 28rpx;
            margin-right: 8rpx;
        }

        .label-text {
            font-size: 28rpx;
            color: #333;
            font-weight: 400;
        }
    }

    .input-field {
        width: 100%;
        padding: 0 0 30rpx 0;
        font-size: 28rpx;
        color: #333;
        border: none;
        outline: none;
        background: transparent;

        &::placeholder {
            color: #999;
            font-size: 28rpx;
        }
    }

    .address-picker {
        // width: 100%;
        height: 88rpx;
        // background-color: #f8f8f8;
        border-radius: 12rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        // padding: 0 30rpx;

        text {
            font-size: 28rpx;
            color: #333;
        }
    }
}

/* 底部区域 */
.bottom-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
}

/* 帮助链接 */
.help-link {
    text-align: center;
    margin-bottom: 30rpx;

    .help-text {
        font-size: 26rpx;
        color: #007AFF;
    }
}

/* 提交按钮 */
.submit-btn {
    background-color: #999;
    border-radius: 8rpx;
    padding: 28rpx;
    text-align: center;
    transition: background-color 0.3s ease;

    .btn-text {
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
    }

    /* 激活状态 - 所有数据填写完成 */
    &.active {
        background-color: #14B19E;
    }
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>