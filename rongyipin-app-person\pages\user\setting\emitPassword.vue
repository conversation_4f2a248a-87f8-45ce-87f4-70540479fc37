<template>
    <view class="page-container">
        <view class="navbar">
            <u-navbar height="44px" title="" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>
        <!-- 页面主标题 -->
        <view class="title">修改密码</view>

        <!-- 说明文字区域 -->
        <view class="info-text">
            <view>修改密码后，可以使用密码登录容易聘</view>
            <view class="current-phone">当前手机号: {{ maskedPhone }}</view>
        </view>

        <!-- 表单区域 -->
        <view class="form-section">
            <!-- 新手机号输入行 -->
            <view class="input-row">

                <input class="input-field" type="text" v-model="password1" placeholder="请输入新密码"
                    placeholder-class="placeholder-style" maxlength="20" @blur="validatePassword1" />
            </view>
            <view v-if="password1Error" class="error-text">{{ password1Error }}</view>
            <view class="input-row">

                <input class="input-field" type="text" v-model="password2" placeholder="再次输入新密码"
                    placeholder-class="placeholder-style" maxlength="20" @blur="validatePassword2" />
            </view>
            <view v-if="password2Error" class="error-text">{{ password2Error }}</view>
            <!-- 验证码输入行 -->
            <view class="input-row">
                <input class="input-field" type="number" v-model="code" placeholder="请输入短信验证码"
                    placeholder-class="placeholder-style" maxlength="6" />
                <view class="code-button" :class="{ disabled: isCountingDown }" @click="getVerificationCode">
                    {{ countdownText }}
                </view>
            </view>

            <!-- 语音验证码提示 -->
            <view class="voice-help" @click="getVoiceCode">
                长时间收不到验证码，可尝试语音接听验证码
            </view>
        </view>

        <!-- 保存按钮 -->
        <button class="submit-button" @click="handleSubmit">保存</button>

    </view>
</template>

<script>
import { userApi } from '@/utils/api.js';
export default {
    data() {
        return {
            newPhone: '',
            code: '',
            countdownText: '获取验证码',
            isCountingDown: false,
            countdownTimer: null,
            userphone: '',
            password1: '',
            password2: '',
            password1Error: '',
            password2Error: ''
        };
    },
    computed: {
        maskedPhone() {
            return this.userphone && this.userphone.length === 11
                ? this.userphone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
                : this.userphone
        }
    },
    onLoad() {
        this.userphone = uni.getStorageSync('userInfo').mobile
        console.log(this.userphone)
    },
    methods: {
        // 获取短信验证码
        async getVerificationCode() {

            if (this.isCountingDown) return;

            try {
                uni.showLoading({ title: '发送中...' });

                const params = {
                    phone: this.userphone,
                    event: 'jobchangemobile' // 修改手机号验证类型
                };

                const result = await userApi.Captcha(params);
                if (result.code === 200) {
                    uni.showToast({
                        title: '验证码已发送',
                        icon: 'success'
                    });
                    this.isCountingDown = true;
                    let seconds = 60;
                    this.countdownText = `${seconds}s后重试`;

                    this.countdownTimer = setInterval(() => {
                        seconds--;
                        if (seconds > 0) {
                            this.countdownText = `${seconds}s后重试`;
                        } else {
                            clearInterval(this.countdownTimer);
                            this.countdownText = '获取验证码';
                            this.isCountingDown = false;
                        }
                    }, 1000);


                } else {
                    uni.showToast({
                        title: result.msg,
                        icon: 'none'
                    });
                }
            } catch (err) {

            }
            // --- 调用API发送验证码 ---

            // -------------------------


        },

        // 获取语音验证码
        getVoiceCode() {
            uni.showToast({
                title: '正在为您拨打语音电话...',
                icon: 'none'
            });
            // 在这里可以调用语音验证码的API
        },

        // 密码校验
        validatePassword1() {
            // 至少8位，包含字母和数字
            const reg = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,20}$/;
            if (!this.password1) {
                this.password1Error = '请输入新密码';
            } else if (!reg.test(this.password1)) {
                this.password1Error = '密码需8-20位且包含字母和数字';
            } else {
                this.password1Error = '';
            }
        },
        validatePassword2() {
            if (!this.password2) {
                this.password2Error = '请再次输入新密码';
            } else if (this.password2 !== this.password1) {
                this.password2Error = '两次输入的密码不一致';
            } else {
                this.password2Error = '';
            }
        },

        // 提交保存
        async handleSubmit() {
            this.validatePassword1();
            this.validatePassword2();
            if (this.password1Error || this.password2Error) {
                return;
            }

            // --- 调用API提交数据 ---
            const params = {
                phone: this.userphone,
                code: this.code,
                event: 'jobchangemobile'
            };
            const res = await userApi.phoneCheck(params)
            if (res.code == 200) {
                const paras = {
                    mobile: this.userphone,
                    newpassword:this.password2,
                    newpassword2:this.password1
                }
                const result = await userApi.resetPwd(paras)
                if (result.code == 200) {
                    uni.showToast({
                        title: '修改成功',
                        icon: 'success'
                    });
                    setTimeout(() => {
                        uni.navigateBack();
                    }, 1500);
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                }
            } else {
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                });
            }

            console.log(this.userphone)
        }
    },
    beforeDestroy() {
        // 页面销毁前清除定时器，防止内存泄漏
        if (this.countdownTimer) {
            clearInterval(this.countdownTimer);
            this.countdownTimer = null;
        }
    }
};
</script>

<style lang="scss" scoped>
.page-container {
    background-color: #ffffff;
    padding: 40rpx;
    min-height: 100vh;
    box-sizing: border-box;
}

.title {
    font-size: 56rpx;
    font-weight: bold;
    color: #222;
    margin-top: 20rpx;
    margin-bottom: 40rpx;
}

.info-text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 80rpx;

    .current-phone {
        margin-top: 20rpx;
    }
}

.form-section {
    width: 100%;
}

.input-row {
    display: flex;
    align-items: center;
    width: 100%;
    border-bottom: 1px solid #f0f0f0;
    padding: 20rpx 0;
    margin-bottom: 40rpx;
}

.country-code {
    display: flex;
    align-items: center;
    font-size: 32rpx;
    color: #333;
    padding-right: 20rpx;

    .down-arrow {
        width: 0;
        height: 0;
        border-left: 10rpx solid transparent;
        border-right: 10rpx solid transparent;
        border-top: 12rpx solid #999;
        margin-left: 10rpx;
    }
}

.input-field {
    flex: 1;
    font-size: 32rpx;
    height: 60rpx;
    line-height: 60rpx;
}

.placeholder-style {
    color: #cccccc;
}

.code-button {
    font-size: 30rpx;
    color: #27c2a3; // 主题色
    white-space: nowrap; // 防止文字换行
    padding-left: 20rpx;

    &.disabled {
        color: #999999;
    }
}

.voice-help {
    font-size: 26rpx;
    color: #27c2a3; // 主题色
    margin-top: 20rpx;
}

.submit-button {
    width: 100%;
    background-color: #27c2a3; // 主题色
    color: #ffffff;
    font-size: 32rpx;
    border-radius: 16rpx;
    height: 96rpx;
    line-height: 96rpx;
    margin-top: 80rpx;

    // 按钮按下的效果
    &:active {
        background-color: #22a88e;
    }
}

.error-text {
    color: #ff4d4f;
    font-size: 26rpx;
    margin-bottom: 20rpx;
    margin-left: 10rpx;
}
</style>