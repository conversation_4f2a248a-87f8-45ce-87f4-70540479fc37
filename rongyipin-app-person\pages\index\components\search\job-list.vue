<template>
  <view>
    <view v-if="loading"  class="loading">
        <u-loading-icon></u-loading-icon>
        加载中
    </view>
    
    <u-empty mode="data" v-else-if="list.length === 0">暂无数据</u-empty>
    
    <view v-else>
      <view class="job-item" v-for="(job, index) in list" :key="index">
            <!-- 职位名称和薪资 -->
                <view class="job-header">
                    <view class="job-title">{{ job.name }}</view>
                    <view class="job-salary">{{ job.min_salary }}-{{ job.max_salary }}</view>
                </view>

                <!-- 公司信息 -->
                <view class="company-info">
                    <text>{{ job.company_name }}</text>
                    <text>{{ job.companySize }}</text>
                    <text>{{ job.companyAddress }}</text>
                </view>

                <!-- 工作要求 -->
                <view class="job-requirements">
                    <text>{{ job.experience }}</text>
                    <text>{{ job.education }}</text>
                    <text>{{ job.workContent }}</text>
                </view>

                <!-- HR 信息 -->
                <view class="hr-info">
                    <u-avatar :src="job.hrAvatar" size="40"></u-avatar>
                    <text>{{ job.username }}</text>
                    <text>{{ job.hrPosition }}</text>
                    
                </view>

                <!-- 打招呼按钮 -->
                <view class="action-button">
                        <u-button color="#059f9f" size="mini" @click="handleHello(job)" shape="circle" :hairline="false">打招呼</u-button>
                </view>
            
            </view>
      <!-- <view class="job-item" v-for="item in list" :key="item.id" @click="viewDetail(item.id)">
        <view class="title">{{ item.title }}</view>
        <view class="company">{{ item.company }}</view>
        <view class="info">
          <text class="salary">{{ item.salary }}</text>
          <text class="location">{{ item.location }}</text>
          <text class="experience">{{ item.experience }}</text>
        </view>
        <view class="time">{{ item.time }}</view>
      </view> -->
    </view>
  </view>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    },
    subTab: {
      type: Number,
      default: 0
    }
  },
  methods: {
    viewDetail(id) {
      uni.navigateTo({
        url: `/pages/job/detail?id=${id}`
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.loading {
  text-align: center;
  padding: 30rpx 0;
  color: #c7c7c7;
}
.job-item {
      position: relative;
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;

  .job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10rpx;

    .job-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .job-salary {
      font-size: 28rpx;
      color: #e74c3c;
    }
  }

  .company-info,
  .job-requirements,
  .hr-info {
    display: flex;
    align-items: center;
    margin-bottom: 10rpx;

    text {
      font-size: 24rpx;
      color: #666;
      margin-right: 10rpx;
    }
  }

  .hr-info {
    margin-bottom: 20rpx;

    .u-avatar {
      margin-right: 10rpx;
    }
  }

  .action-button {
    text-align: right;
    position: absolute;
    right: 20px;
    bottom: 37px;
  }
}
// .job-item {
//   padding: 30rpx 0;
//   border-bottom: 1rpx solid #eee;
  
//   .title {
//     font-size: 32rpx;
//     font-weight: bold;
//     margin-bottom: 10rpx;
//     color: #333;
//   }
  
//   .company {
//     font-size: 28rpx;
//     color: #666;
//     margin-bottom: 15rpx;
//   }
  
//   .info {
//     display: flex;
//     font-size: 24rpx;
//     color: #999;
//     margin-bottom: 10rpx;
    
//     text {
//       margin-right: 20rpx;
//     }
    
//     .salary {
//       color: #ff5a5f;
//     }
//   }
  
//   .time {
//     font-size: 24rpx;
//     color: #bbb;
//   }
// }
</style>