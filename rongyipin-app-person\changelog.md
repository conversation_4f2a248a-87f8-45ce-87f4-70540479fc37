## 2.2.3（2024-12-12）
更新 依赖的uni-id-pages的版本为1.1.23
## 2.2.2（2024-10-27）
修复在鸿蒙上注册登录后跳转页面白屏的问题
## 2.2.1（2024-10-26）
兼容鸿蒙
## 2.2.0（2024-04-28）
更新 依赖的uni-id-pages的版本为1.1.20
## 2.1.9（2024-01-15）
更新 db_init.json 按collection拆分，每个collection由schema.json、index.json、init_data.json三个文件描述[详情](https://doc.dcloud.net.cn/uniCloud/hellodb.html#init-db-2)
## 2.1.8（2023-12-15）
更新 升级依赖的uni-icons版本为2.0.8
## 2.1.7（2023-12-15）
修复 因网络错误引起的 manifest.json 文件错误引起的 web 端字体大小等样式问题
## 2.1.6（2023-12-14）
- 更新 依赖的uni-id-pages的版本为1.1.17
## 2.1.5（2023-10-20）
- 更新 依赖的uni-id-pages的版本为1.1.16
## 2.1.4（2023-07-11）
- 纠正`opendb-news-articles.schema.json`错误的权限表达式`doc.uid`为`doc.user_id`
## 2.1.3（2023-05-10）
- 更新 依赖的uni-id-pages的版本为1.1.13 修复 启用摇树优化后切换登陆方式报错的问题
## 2.1.2（2023-02-10）
- 新增 微信小程序端 首页需强制登录时，隐藏返回首页按钮
## 2.1.1（2023-02-02）
- 重要 分包加载uni-id-pages 优化后 运行时主包大小为：637KB 分包为：78KB，发布后主包大小为：585KB  分包为：75KB
- 新增 微信小程序端 支持选择使用微信资料的“头像”和“昵称” 设置用户资料 [详情参考](https://wdoc-76491.picgzc.qpic.cn/MTY4ODg1MDUyNzQyMDUxNw_21263_rTNhg68FTngQGdvQ_1647431233?w=1280&h=695.7176470588236)
## 2.1.0（2023-01-17）
- 重要 新增uni-admin需要的相关依赖和初始化数据（方便uni-admin关联uni-starter后可直接运行）  
- 升级依赖的 [uni-id-pages](https://ext.dcloud.net.cn/plugin?name=uni-id-pages) 修复如下问题：
	1. 优化 压缩依赖的文件资源大小 
	2. 更新依赖的 验证码插件`uni-captcha`版本的版本为 0.6.4 修复 部分情况下APP端无法获取验证码的问题 [详情参考](https://ext.dcloud.net.cn/plugin?id=4048)
	3. 修复 客户端token过期后，点击退出登录按钮报错的问题
	4. uni-id-co 修复 updateUser 接口`手机号`和`邮箱`参数值为空字符串时，修改无效的问题
## 2.0.6（2022-10-19）
- 更新 文件路径：`/uni_modules/uni-id-pages/init.js`内uni-push客户端推送标识获取失败的提示
## 2.0.5（2022-10-19）
- 更新依赖的`uni-id-pages`的版本为1.0.26
## 2.0.4（2022-09-21）
- 新增 使用uni-id-pages的账号信息的状态管理功能
## 2.0.3（2022-09-20）
- 更新 依赖的`uni-ui`组件为最新版本（注意：该版本的`uni-form`相关组件将自定义节点设置成[虚拟节点](https://uniapp.dcloud.net.cn/tutorial/vue-api.html#%E5%85%B6%E4%BB%96%E9%85%8D%E7%BD%AE))）
- 更新 依赖的`uni_module`-> `uni-id-pages`为 v1.0.19版 修复 小程序端，使用将自定义节点设置成虚拟节点的uni-ui组件，导致的样式错乱问题
## 2.0.2（2022-09-19）
- 更新表结构，解决部分clientDB操作没有权限的问题
## 2.0.1（2022-09-16）
- 更改默认值
## 2.0.0（2022-09-16）
- 【重要】 v2版正式发布 应用`uni-id-pages`、`uniIdRouter`；（注意：此版本更新内容较多，升级请注意备份）
- 考虑到部分旧项目不想升级，保留`uni-starter v1`版源码，托管在仓库的[v1分支](https://gitcode.net/dcloud/uni-starter/-/blob/v1/README.md)；继续使用v1版，遇到问题可以提交[Issue](https://gitcode.net/dcloud/uni-starter/-/issues/new?issue%5Bassignee_id%5D=&issue%5Bmilestone_id%5D=)有bug仍然会修复，但v1版本不再新增功能。
## 1.2.7（2022-08-10）
- 修复微信小程序绑定手机号失败的问题
## 1.2.6（2022-06-29）
- 支持 ios 安全区
## 1.2.5（2022-05-29）
升级预置的`uni_modules`->`uni-captcha`版本为：0.6.0。[详情](https://ext.dcloud.net.cn/plugin?name=uni-captcha)
## 1.2.4（2022-05-20）
- 修改`uni-starter.config.js`->`debug`的默认值为`false`
## 1.2.3（2022-05-20）
- 默认关闭`manifest.json`中的扩展配置
- `uni-starter.config.js` 新增debug,用于配置是否开启调试模式
## 1.2.2（2022-05-19）
- 优化登陆体验，账号密码登陆错误超过2次，再显示图形验证码进行人机校验。
## 1.2.1（2022-05-18）
- 修复在某些情况下，微信小程序端验证码显示错误的问题
## 1.2.0（2022-05-16）
- 短信验证码登陆、绑定手机号码新增防刷逻辑。当短信验证码输入错误2次以上，弹出图形验证码进行人机校验。
- uni-id-cf，新增防刷机制。更改loginLog为uniIdLog 记录各类uni-id操作，并新增action字段记录操作的行为名称
- 注册账号新增需要输入图形验证码
## 1.1.34（2022-05-12）
修复绑定手机号码，未验证空验证码的问题。注意：请确保项目依赖的uni-id版本为3.3.18+
## 1.1.33（2022-02-24）
修复微信小程序端，个人资料-绑定手机号码，一键获取微信资料中手机号码绑定授权，点击“拒绝”时toast：encryptedData 不可为空的问题
## 1.1.32（2022-02-24）
- 删除多余文件：`uniCloud/database/opendb-news-articles-detail.schema.json`
- 修复当用户选择验证码登陆方式，在输入验证码页面，点击微信登陆时报“你未同意隐私政策协议”的问题
## 1.1.31（2022-02-16）
修复微信小程序端，修改绑定的手机号码时表单验证不正常的问题
## 1.1.30（2022-01-26）
- 新增逻辑:调用uni-id-cf的logout接口后刷新设备信息中token的有效期
- 修复某些情况下前端执行logout没调用uniID.logout的问题
- 修复某些情况下报push_clientid未定义的问题
## 1.1.29（2022-01-25）
- 保存`uni_id_token`到`storage`改用异步方法，方便通过拦截器执行`token`更新后的操作
- 新增通过拦截器监听`uni_id_token`更新，调用云函数刷新刷新设备信息token有效期的API `renewDeviceTokenExpired`
- 删除留言板示例
- 修复图片验证码样式问题
## 1.1.28（2022-01-12）
删除list页第37行多了个`？`引起的报错
## 1.1.27（2022-01-11）
更新uni-id-cf为:1.0.10版，修复 限制只有 admin 用户可管理用户标签（不支持非 admin 用户操作managerMultiTag 接口）
## 1.1.26（2021-12-29）
- 性能优化，list页面使用`getTemp`，[详情](https://uniapp.dcloud.io/uniCloud/unicloud-db?id=collection)
- 拉齐uni-starter和uni-admin的schema新增：uni-id-tag.schema.json，更新：opendb-verify-codes.schema.json
- 修复首次登陆,用户id没存储到storage的问题
- 新增：执行退出登陆后，通过云函数调用`uniID.logout`
## 1.1.25（2021-12-09）
修复H5端在about页面，返回触发`uniShare.hide()`引发报错
## 1.1.24（2021-11-29）
- 新增注销用户账号的功能
- 修复在某些情况下，签到不连续7天，也获得60积分的问题
## 1.1.23（2021-11-20）
- 使用`uni.getUniverifyManager`优化一键登陆中，点击第三方登陆的逻辑：未勾选隐私政策时，toast提醒并阻止了一键登陆界面的close
- 新增支持看激励视频广告签到
## 1.1.22（2021-11-10）
删除`common/openApp.js`中可选链操作符，解决vue3版本在hbuilderX内置浏览器不兼容的问题
## 1.1.21（2021-11-10）
新增app端列表页面使用原生list下拉刷新
## 1.1.20（2021-11-08）
修复vue3版某些情况下i18n报错的问题
## 1.1.19（2021-11-08）
配置文件`uni-starter.config.js`默认关闭i18n多语言国际化
## 1.1.18（2021-10-14）
使用2.0版`uni-share`。当显示分享窗口时，监听返回操作(如：物理返回，全面屏手机侧滑)关闭分享窗口，而不是关闭当前页面。
## 1.1.17（2021-10-12）
- 更新文档
- 修复list页面where条件中缺少&符，导致的错误
## 1.1.16（2021-10-05）
在控制台提示：开启多语言国际化，将获取i18n中配置的tabbar的值覆盖pages.json中配置的tabbar的值
## 1.1.15（2021-10-02）
新增，支持配置是否开启i18n多语言国际化。
配置文件：`uni-starter.config.js`
`
"i18n":{
	"enable":true //默认启用，国际化。如果你不想使用国际化相关功能，请改为false
}
`
## 1.1.14（2021-09-30）
1. 通过微信小程序登录自动保存`sessionKey`到`uni-id-users`表
2. 我的-设置-个人资料 点击绑定手机号码，完善账号信息支持以下三种策略：
	- APP端，（如果支持）使用"通过运营商一键获取手机号码"
	- 微信小程序端，支持"一键获取微信绑定的手机号"
	- 其他端，通过手机验证码
## 1.1.13（2021-09-29）
修复search页面因多语言国际化导致的白屏问题
## 1.1.12（2021-09-28）
1. 改造微信登录逻辑，直接使用`uni.login`参数`"onlyAuthorize":true`实现
2. 修复，一键登录弹出层，已勾选“同意隐私政策协议”点击自定义登录按钮，报“你未同意隐私政策协议”的bug
## 1.1.11（2021-09-24）
优化邀请下载app页（`pages/ucenter/invite`）下载按钮闪烁的问题
## 1.1.10（2021-09-23）
修复获取验证码按钮的文字，在中文模式下显示为英文的问题
## 1.1.9（2021-09-16）
修复由多语言切换功能引起的隐私政策协议标题链接被重写的问题
## 1.1.8（2021-09-15）
更新数据表guestbook的schema中更新权限的配置
## 1.1.7（2021-09-14）
更新数据表opendb-news-articles的schema中的权限配置
## 1.1.6（2021-09-13）
纠正错误schema权限表达式`doc.uid`为`doc.user_id`
## 1.1.5（2021-09-01）
为了更直观理解路由拦截。移除路由拦截器中，默认过滤登录相关页面拦截的逻辑。确保所有白名单页面均在配置文件router.visitor中体现
## 1.1.4（2021-08-31）
修改错误的文章表`SChema`的读权限表达式
## 1.1.3（2021-08-31）
修复在微信小程序端默认语言为英文的问题
## 1.1.2（2021-08-30）
修复在微信小程序下切换语言报`locale`不存在的问题
## 1.1.1（2021-08-30）
- 解决3.2.6以下版本hbuilderx，编译的项目报`uni.setLocale`不存在的问题
## 1.1.0（2021-08-27）
- APP端支持vue3 （hbuilderx 3.2.5+）
- 支持国际化 中英文切换
- 新增留言板示例
- 修复签到的时区问题
## 1.0.48（2021-08-10）
- 修复登录成功后响应体包含`userInfo.password`的问题
- 修改了`uni-id-users`表的schema中字段username的编辑权限，防止用户通过clientDB绕过用户名不能重复的规则更新用户名的问题
## 1.0.47（2021-08-09）
- 更新文档快速体验部署流程
- 修复一键登录优先时报变量找不到的问题
## 1.0.46（2021-08-05）
清理多余文件
## 1.0.45（2021-08-05）
默认首页为nvue页面+fast
## 1.0.44（2021-08-05）
解决首页为非nvue页面时白屏的问题。
- 注意：本次在`common/appInit.js`中修改了路由拦截的逻辑，是个兼容方案；当首页为非nvue页面，路由拦截器逻辑会在加载首页时执行。接下来新版本的hx编译的uni-app项目无论首页是否为nvue都不走拦截器，保持各端逻辑一致。
## 1.0.43（2021-08-02）
1. 微信小程序端，新增：微信登录成功后，弹出是否"获取微信头像和昵称，完善个人资料"的弹框
2. APP端，新增逻辑：微信登录成功后，自动获取用户的微信昵称和头像完善用户个人资料
- 提示：因为微信的头像一旦更换，微信返回的头像url会失效。所以，以上两示例功能将url（客户端：下载到临时目录/服务端：转为Buffer）再上传到uniCloud云存储中再使用。
## 1.0.42（2021-07-29）
新增绑定手机号码页面前端校验
## 1.0.41（2021-07-27）
1. 支持vue3.0
2. 去掉App.vue全局样式，避免与非flex布局的页面样式冲突
## 1.0.40（2021-07-22）
1. 调整使用正则表达式配置强制登录功能的写法，解决在小程序端的兼容问题。
2. 新增签到功能（培养用户习惯，提升用户粘性）。支持：每日签到奖励、周期性连续7日签到，奖励翻倍。
## 1.0.39（2021-07-19）
1. 强制登录配置,新增白名单模式
2. 强制登录配置,支持正则表达式
## 1.0.38（2021-07-17）
删除多余文件
## 1.0.37（2021-07-14）
去掉配置文件：`uni-starter.config.js`，`h5` —> `url`结尾的`/`
## 1.0.36（2021-07-14）
剪切板中的邀请码，添加标识性前缀 `uniInvitationCode:`
## 1.0.35（2021-07-12）
1. H5端默认不开启，隐私权限协议签署页面。因为网页端没有什么隐私权限能被获取，目前全球仅欧盟有要求；如有需要请手动开启
2. 在列表页演示，如何在onShow生命周期获取设备位置，并在设备或者应用没有权限时自动引导。设置完毕自动重新获取。[更多点此查看插件介绍](https://ext.dcloud.net.cn/plugin?name=json-gps)
## 1.0.34（2021-07-08）
修复，打开登录页时携带参数，导致的快捷登录方式重复的问题
## 1.0.33（2021-07-06）
修复，点击短信验证码登录打开的页面不正确的问题
## 1.0.32（2021-07-06）
修复，仅配置一种快捷登录时的错误
## 1.0.31（2021-07-02）
优化项目文档
## 1.0.30（2021-07-01）
1. 简化宫格页面写法，方便理解如何控制不同状态角色的用户是否可见哪些元素。
2. uni-id-cf发送短信验证码api，默认注释掉：虚拟发送短信验证码的代码块。
3. uni-id-cf统一action名称为驼峰法
## 1.0.29（2021-06-29）
1. 修复在安卓10以下设备,操作登录获取不到oaid会直接导致登录失败的bug
2. 修复uniCloud版本为阿里云版时删除头像设置失败，腾讯云版删除头像后二次上传失败的问题
## 1.0.28（2021-06-28）
修复云函数uni-id-cf的resetPwdBySmsCode接口，未注册过的用户也能调用的问题
## 1.0.27（2021-06-25）
修改文档，新增h5版演示示例
## 1.0.26（2021-06-24）
升级用户头像上传的裁切功能，app端为原生裁剪其他端保持原来方式。数据表字段改用avatar_file存储file对象方便做图片的回显
## 1.0.25（2021-06-23）
预置uniCloud admin依赖的uniCloud文件，方便uniCloud admin与uni-starter配套使用时免做文件迁移
## 1.0.24（2021-06-23）
删除callFunction拦截器中多余的代码
## 1.0.23（2021-06-22）
更正调试遗留的uni-config-center/uni-id/config.json的tokenExpiresIn=1配置问题，改为默认值7200
## 1.0.22（2021-06-22）
1. 新增一键登录授权界面的其他快捷登录按钮
2. 优化uni-quick-login组件代码
3. 调整隐私政策协议框勾选逻辑：在登录页面已勾选，同步勾选。如果没勾选需要手动勾选（为符合应用市场上架要求）
4. 调整登录页隐私政策协议框位置。
5. 增强路由拦截，新增判断token是否过期。
## 1.0.21（2021-06-21）
优化：uni_modules模式使用uni-id-cf，方便uni-starter与uniCloud-admin的uni-id-cf同步更新。
## 1.0.20（2021-06-18）
1.H5端新增，强制要求用户同意隐私协议 2.兼容ios端自动设置打开下载页用户的剪切板为邀请者的inviteCode 3.成功注册用户，且请求体含邀请码inviteCode自动关联裂变关系
## 1.0.19（2021-06-17）
1.新增获取邀请码接口getUserInviteCode 2.在邀请用户下载应用页面，自动设置被邀请用户的剪切板为邀请者的code(仅支持安卓端) 3.在注册或登录并注册请求时自动添加剪切板中的请求参数 4.统一接口名称为驼峰法
## 1.0.18（2021-06-15）
修复，APP端有安装微信客户端但未显示微信登录快捷键的问题
## 1.0.17（2021-06-09）
修复，非APP端deviceInfo为空引起的登录失败问题
## 1.0.16（2021-06-08）
新增，操作注册/登录操作自动获取客户端设备：push_clientid、imei、oaid、idfa新增/更新到数据表uni-id-device新增，操作注册/登录操作自动获取客户端设备：push_clientid、imei、oaid、idfa新增/更新到数据表uni-id-device
## 1.0.15（2021-06-07）
为迎合苹果App Store的规则，登录与分享功能项显示之前自动检测是否安装了对应客户端。比如：设备未安装微信则不显示微信快捷登录和微信分享选项。为迎合苹果App Store的规则，登录与分享功能项显示之前自动检测是否安装了对应客户端。比如：设备未安装微信则不显示微信快捷登录和微信分享选项。
## 1.0.14（2021-06-07）
修改错误的表名称uni-verify为opendb-verify-codes
## 1.0.13（2021-06-04）
新增一键登录界面的第三方快捷登录按钮
## 1.0.12（2021-05-28）
修复拦截器在ios app端会报错：Unhandled promise...的问题
## 1.0.10（2021-05-27）
新增callfunction的拦截器废除this.request的写法。为callFunction添加：请求失败是否断网判断并提示、恢复网络自动重新执行、自动处理响应体：token过期自动跳转到登录页面、token自动续期
## 1.0.9（2021-05-23）
修复变量被重复定义的问题
## 1.0.8（2021-05-22）
宫格页(/pages/grid/grid)，新增根据当前用户是否登录、是否为管理员的角色来决定是否显示的示范
## 1.0.7（2021-05-22）
删除多余数据
## 1.0.6（2021-05-22）
修复当username（用户名&密码）为第一优先级的登录方式时。无法切换到smsCode(短信验证码)登录方式
## 1.0.5（2021-05-20）
改用uni_modules方式处理图片选择api时无权限，引导用户快捷打开系统设置
## 1.0.4（2021-05-19）
为方便部署，添加空的manifest.json uni-config-center下的uni-id配置
## 1.0.3（2021-05-18）
重大调整，原云函数名称：user-center改名叫uni-id-cf
修复，绑定手机号码场景。因手机未插SIM导致的一键登录失败后未直接跳到获取短信验证码方式绑定
## 1.0.2（2021-05-17）
添加 uni-config-center/uniCloud/cloudfunctions/common/uni-config-center/uni-id/config.json 文件
## 1.0.1（2021-05-17）
manifest.json 在小程序平台增加了一个配置项 betterScopedSlots，启用新的作用域插槽编译，用于支持作用域插槽内使用复杂表达式。
## 1.0.0（2021-05-17）
第一版