<template>
  <view class="container">
    <!-- 头部导航栏 -->
     <u-navbar :autoBack="true" title="添加银行卡" :is-fixed="true" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <view class="form-container">
      <view class="form-title">添加银行卡</view>
      
      <view class="form-item">
        <text class="label">持卡人</text>
        <u-input 
          v-model="form.cardholder" 
          placeholder="请输入您的姓名"
          placeholder-class="placeholder"
          :border="true"
          clearable
        />
      </view>
      
      <view class="form-item">
        <text class="label">卡号</text>
        <u-input 
          v-model="form.cardNumber" 
          placeholder="请输入您的银行卡号"
          placeholder-class="placeholder"
          :border="true"
          type="number"
          clearable
          @input="formatCardNumber"
        />
      </view>
      
      <!-- <view class="form-item">
        <text class="label">卡类型</text>
        <u-input 
          v-model="form.bankName" 
          placeholder="银行卡名称"
          placeholder-class="placeholder"
          :border="true"
         
        />
        <u-icon 
          name="arrow-right" 
          size="28" 
          color="#999" 
          class="bank-select-icon"
          @click="showBankPicker = true"
        />
      </view> -->
      
      <view class="hint-text">
        提示:为保证资金安全，只能绑定实名认证本人持有的银行卡
      </view>
      
      <u-button 
        type="primary" 
        shape="circle" 
        @click="handleSubmit"
        :disabled="!canSubmit"
        class="submit-btn"
      >
        绑定银行卡
      </u-button>
    </view>
    
    <!-- 银行选择器 -->
    <u-picker 
      v-model="showBankPicker" 
      mode="selector" 
      :range="bankList" 
      range-key="name"
      @confirm="selectBank"
    ></u-picker>
  </view>
</template>

<script>
import {rider} from '@/utils/api.js'
export default {
  data() {
    return {
      form: {
        cardholder: '',
        cardNumber: '',
        bankName: ''
      },
      showBankPicker: false,
      bankList: [
        { name: '中国工商银行', value: 'icbc' },
        { name: '中国农业银行', value: 'abc' },
        { name: '中国银行', value: 'boc' },
        { name: '中国建设银行', value: 'ccb' },
        { name: '交通银行', value: 'bcm' },
        { name: '招商银行', value: 'cmb' },
        { name: '中信银行', value: 'citic' },
        { name: '中国邮政储蓄银行', value: 'psbc' }
        // 可以添加更多银行...
      ]
    }
  },
  computed: {
    canSubmit() {
      return this.form.cardholder && 
             this.form.cardNumber && 
             this.form.cardNumber.replace(/\s/g, '').length >= 16;
    }
  },
  methods: {
    // 格式化银行卡号 (每4位加空格)
    formatCardNumber() {
    //   this.form.cardNumber = this.form.cardNumber.replace(/\s/g, '')
    //     .replace(/(\d{4})(?=\d)/g, '$1 ');
    },
    
    // 选择银行
    selectBank(e) {
      this.form.bankName = this.bankList[e[0]].name;
    },
    
    // 提交表单
    handleSubmit() {
      if (!this.canSubmit) return;
      
      uni.showLoading({ title: '绑定中...' });
      
      // 模拟API请求
    //   setTimeout(() => {
        // uni.hideLoading();
        // uni.showToast({ title: '绑定成功' });
        const params = {
          payment_account_name: this.form.cardholder,
          payment_account: this.form.cardNumber,
        //   payment_addr: this.form.bankName
        };
        console.log(params);
         rider.withdrawalAccountAdd({...params}).then(res => {
            uni.hideLoading();
            // this.detailList=res.data.data;
            // this.totalBalance=res.data.total;
        
            console.log(res);
        });
        
        // 绑定成功后返回上一页
        // setTimeout(() => {
        //   uni.navigateBack();
        // }, 1500);
    //   }, 1500);
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.form-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  color: #333;
}

.form-item {
  margin-bottom: 40rpx;
  position: relative;
  display: flex;
  align-items: center;
  .label {
    display: block;
    font-size: 30rpx;
    color: #666;
    // margin-bottom: 20rpx;
  }
  
  ::v-deep .u-input {
    padding-right: 60rpx !important;
  }
}

.bank-select-icon {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
}

.hint-text {
  font-size: 26rpx;
  color: #999;
  margin: 40rpx 0;
  line-height: 1.6;
}

.submit-btn {
  margin-top: 30rpx;
  height: 90rpx;
  font-size: 34rpx;
}

.placeholder {
  color: #ccc;
  font-size: 30rpx;
}
</style>