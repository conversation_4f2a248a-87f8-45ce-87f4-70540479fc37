<template>
  <view class="balance-detail-page">
    <!-- 头部导航栏 -->
     <u-navbar :autoBack="true" title="余额明细" :is-fixed="true" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <!-- 余额明细列表 -->
    <view class="detail-list">
      <view class="detail-item" v-for="(item, index) in detailList" :key="index">
        <view class="item-left">
          <text class="item-title">{{ item.payment_method_text }}</text>
          <text class="item-date"> {{ item.create_time }}</text>
        </view>
        <view class="item-right">
          <text :class="['item-amount', item.amount > 0 ? 'positive' : 'negative']">{{ item.amount }}元</text>
        </view>
      </view>
    </view>

    <!-- 累计收益 -->
    <!-- <view class="total-balance">
      <text class="total-label">累计收益</text>
      <text class="total-amount">{{ totalBalance }}元</text>
    </view> -->
  </view>
</template>

<script>
import {rider} from '@/utils/api.js'
import { methods, onLoad } from 'uview-ui/libs/mixin/mixin';
export default {
    
  data() {
    return {
      detailList: [],
      totalBalance: 159.6
    };
  },
  onLoad() {
    this.getBalance();
  },
  methods: {
    getBalance() {
      rider.withdrawalList().then(res => {
         uni.hideLoading();

        this.detailList=res.data.data;
        // this.totalBalance=res.data.total_amount;
       
        console.log(res);
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.balance-detail-page {
  padding: 20rpx;
  background-color: #fff;
}

.detail-list {
  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }

    .item-left {
      .item-title {
        font-size: 28rpx;
        color: #333;
      }

      .item-date {
        font-size: 24rpx;
        color: #999;
        margin-top: 10rpx;
      }
    }

    .item-right {
      .item-amount {
        font-size: 28rpx;
        &.positive {
          color: #FF5722;
        }

        &.negative {
          color: #333;
        }
      }
    }
  }
}

.total-balance {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  font-size: 28rpx;

  .total-label {
    color: #666;
  }

  .total-amount {
    color: #FF5722;
    margin-left: 10rpx;
  }
}
</style>


