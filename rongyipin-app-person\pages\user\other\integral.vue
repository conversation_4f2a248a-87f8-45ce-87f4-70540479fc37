<template>
    <view class="integral-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="积分中心" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
                <template #right>
                    <u-icon name="more-dot-fill" size="20" color="#333" @click="showMore"></u-icon>
                </template>
            </u-navbar>
        </view>

        <!-- 我的积分卡片 -->
        <view class="my-points-card">
            <text class="card-title">我的积分</text>
            <view class="points-display">
                <view class="points-circle">
                    <text class="points-number">{{userInfo.score}}</text>
                </view>
            </view>
        </view>

        <!-- 做任务赚积分 -->
        <view class="task-section">
            <text class="section-title">做任务赚积分</text>
            <view class="task-list">
                <view
                    v-for="(task, index) in taskList"
                    :key="task.id"
                    class="task-item"
                    @click="handleTask(task)"
                >
                    <view class="task-left">
                        <view class="task-icon">
                            <!-- <u-icon :name="task.icon" size="20" :color="task.iconColor"></u-icon> -->
                        </view>
                        <view class="task-info">
                            <text class="task-name">{{ task.name }}</text>
                            <view class="task-reward">
                                <view class="reward-icon">
                                    <u-icon name="rmb-circle-fill" color="#FF9500" size="28"></u-icon>
                                    <text class="reward-text">+{{ task.points }}</text>
                                    
                                </view>
                                
                            </view>
                            <view class="task-propres" v-if="task.id==3">
                                <u-line-progress :percentage="task.check_num/task.check_total*100" height="20" activeColor="#FF9500"></u-line-progress>
                            </view>
                            
                        </view>
                    </view>
                    <view class="task-right">
                        <view class="task-btn" :class="{ 'completed': task.status==1 }">
                            <text class="btn-text">{{ task.status_text }}</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 积分兑换 -->
        <view class="exchange-section">
            <text class="section-title">积分兑换</text>
            <view class="exchange-grid">
                <view
                    v-for="(item, index) in exchangeList"
                    :key="item.id"
                    class="exchange-item"
                    :class="{'exchange-item-active':selectedPoints.id== item.id}"
                    @click="exchangePoints(item)"
                >
                    <view class="exchange-points" >
                        <u-icon name="rmb-circle-fill" color="#FF9500" size="28"></u-icon>
                        <text class="points-text">{{ item.score }}</text>
                        <text class="points-unit">积分</text>
                    </view>
                    <text class="exchange-value">{{ item.price }}元</text>
                </view>
            </view>
        </view>

        <!-- 充值方式 -->
        <view class="recharge-section">
            <text class="section-title">充值方式</text>
            <view class="recharge-options">
                <view class="recharge-item" :class="{'recharge-item-active':selectedRechargeMethod=='alipay'}" @click="rechargeWithAlipay">
                    <u-icon name="zhifubao-circle-fill" size="44" color="#1677FF"></u-icon>
                    <text class="recharge-text">支付宝支付</text>
                </view>
                <view class="recharge-item" :class="{'recharge-item-active':selectedRechargeMethod=='wechat'}" @click="rechargeWithWechat">
                    <u-icon name="weixin-circle-fill" size="44" color="#07C160"></u-icon>
                    <text class="recharge-text">微信支付</text>
                </view>
            </view>
            <text class="recharge-tip">充值即代表同意 <text class="link-text" @click="viewAgreement">充值服务协议</text></text>
        </view>

        <!-- 确认充值按钮 -->
        <view class="confirm-section">
            <view class="confirm-btn" @click="confirmRecharge">
                <text class="confirm-text">确认充值</text>
            </view>
            <text class="order-info">有问题请联系客服：1111111111</text>
        </view>
    </view>
</template>

<script>
import { userApi,userinfoApi } from '@/utils/api'
export default {
    data() {
        return {
            currentPoints: 20, // 当前积分
            taskList: [], // 任务列表
            exchangeList: [], // 兑换列表
            selectedRechargeMethod: '', // 选中的充值方式
            selectedPoints: {}, // 选中的积分
            userInfo: {},
        }
    },
    onLoad() {
        this.userInfo = uni.getStorageSync('userInfo')
        this.loadData()
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack()
        },

        // 显示更多选项
        showMore() {
            uni.showActionSheet({
                itemList: ['积分规则', '使用说明', '联系客服'],
                success: (res) => {
                    switch(res.tapIndex) {
                        case 0:
                            this.showPointsRule()
                            break
                        case 1:
                            this.showUsageGuide()
                            break
                        case 2:
                            this.contactService()
                            break
                    }
                }
            })
        },

        // 处理任务
        handleTask(task) {

            if (task.status==1) {
                uni.showToast({
                    title: '任务已完成',
                    icon: 'none'
                })
                return
            }
            
            switch (task.id) {
				case 1:
					this.register(task)//一般登录进来就是注册完成的,
					break
				case 2:
					this.uploadInfo(task);//上传头像
					break
				case 3:
					this.uploadInfo(task);//填写个人信息
					break
				case 4:
					this.publicNumber(task);//推广公众号
					break
				case 5:
					this.inform(task)
					break
				
			}

            // console.log('执行任务:', task)

            // // 模拟任务完成
            // task.completed = true
            // task.btnText = '已完成'
            // this.currentPoints += task.points

            // uni.showToast({
            //     title: `获得${task.points}积分`,
            //     icon: 'success'
            // })
        },
        //未注册
        register() {
            uni.navigateTo({
                url: '/pages/login/login'
            })
        },
        //上传头像
        uploadInfo() { 
            uni.navigateTo({
                url: '/pages/user/userinfo/index'
            })
        },
        //公众号
        publicNumber(){
            uni.showToast({
                title: `公众号暂未开通`,
                icon: 'none'
            })
        },
        //消息
        inform(){
            uni.showToast({
                title: `暂未开发,敬请期待`,
                icon: 'none'
            })
        },

        // 积分兑换
        exchangePoints(item) {
            if (this.currentPoints < item.points) {
                uni.showToast({
                    title: '积分不足',
                    icon: 'none'
                })
                return
            }
            this.selectedPoints = item;
            

            // uni.showModal({
            //     title: '确认兑换',
            //     content: `确定用${item.points}积分兑换${item.value}吗？`,
            //     success: (res) => {
            //         if (res.confirm) {
            //             this.currentPoints -= item.points
            //             uni.showToast({
            //                 title: '兑换成功',
            //                 icon: 'success'
            //             })
            //         }
            //     }
            // })
        },

        // 支付宝充值
        rechargeWithAlipay() {
            this.selectedRechargeMethod = 'alipay'
            console.log('选择支付宝充值')
        },

        // 微信充值
        rechargeWithWechat() {
            this.selectedRechargeMethod = 'wechat'
            console.log('选择微信充值')
        },

        // 查看协议
        viewAgreement() {
            console.log('查看充值服务协议')
            uni.showToast({
                title: '跳转到协议页面',
                icon: 'none'
            })
        },

        // 确认充值
        async confirmRecharge() {
            if (!this.selectedRechargeMethod) {
                uni.showToast({
                    title: '请选择充值方式',
                    icon: 'none'
                })
                return
            }
            const params = { 
                method: this.selectedRechargeMethod,
                 pay_id:this.selectedPoints.id
            }
            console.log("params",params);
            if(this.selectedRechargeMethod=='alipay'){
              await userApi.balanceRecharge(params).then(res => { 
                    uni.hideLoading();
                    console.log(res);
                    if(res.code==200){
                        this.handleAlipayPayment(res.data.formData) 
                    }else{
                        uni.showToast({
                            title: res.message,
                            icon: 'none'
                        })
                    }
                })  
            }else if(this.selectedRechargeMethod=='wechat'){ 
                uni.showToast({
					title: '微信支付暂未接通',
					icon: 'none'
				});
            }     
        },

        // 微信支付处理
		async handleWechatPayment(paymentParams) {
			
				// uni.showLoading({
				// 	title: '正在调起支付...'
				// });

				// 1. 先调用后端接口获取支付参数paymentParams

				// 2. 调起微信支付
				uni.requestPayment({
					provider: 'wxpay',
					orderInfo: {
						appid: paymentParams.appid,           // 微信开放平台审核通过的应用APPID
						partnerid: paymentParams.partnerid,   // 微信支付分配的商户号
						prepayid: paymentParams.prepayid,     // 预支付交易会话ID
						package: 'Sign=WXPay',                // 固定值
						noncestr: paymentParams.noncestr,     // 随机字符串
						timestamp: paymentParams.timestamp,   // 时间戳
						sign: paymentParams.sign              // 签名
					},
                    success(res) {
                        console.log('success:' ,res);
                        this.handlePaymentSuccess();
                    },
                    fail(e) {

                    }
				});
                

				uni.hideLoading();

				uni.showToast({
					title: '支付成功',
					icon: 'success'
				});

				

			
		},


        // 支付宝支付处理
		handleAlipayPayment(orderinfo) {
			
				if (!orderinfo) {
					throw new Error('支付宝订单信息为空');
				}
				// 设置支付宝沙箱环境

				// 2. 调起支付宝支付
				uni.requestPayment({
					provider: 'alipay',
					orderInfo: orderinfo, // 支付宝的支付参数
                    success: (res) => { 
                        uni.hideLoading();
                        console.log('支付宝支付成功:', res);
					},
                    fail: (err) => { 
                        uni.hideLoading();
                        console.log('支付宝支付失败:', res);
					},
					// complete: (res) => {
					// 	console.log('支付宝支付结果:', res);
					// 	if (res.errMsg === 'requestPayment:ok') {
					// 		// 支付成功
					// 		console.log(res);
					// 	} else {
					// 		// 支付失败
					// 		console.log(res);
					// 	}
					// }
				});

				uni.hideLoading();
				

				uni.showToast({
					title: '支付成功',
					icon: 'success'
				});

				this.handlePaymentSuccess();

			
		},
        // 支付成功后的处理
		handlePaymentSuccess() {
			// 这里可以添加支付成功后的逻辑
			console.log('支付成功，执行后续逻辑');
            userinfoApi.userinfo().then(ress => {
                if (ress.code == 200) {
                console.log(ress, 'qqqqq');
                uni.setStorageSync("userInfo", ress.data);
                }
            })

			// 例如：跳转到成功页面
			// uni.navigateTo({
			//     url: '/pages/payment/success'
			// });

			// 或者刷新当前页面数据
			// this.refreshData();
		},

        // 显示积分规则
        showPointsRule() {
            console.log('显示积分规则')
        },

        // 显示使用说明
        showUsageGuide() {
            console.log('显示使用说明')
        },

        // 联系客服
        contactService() {
            console.log('联系客服')
        },

        // 加载数据
        async loadData() {
            await userApi.pointTask().then(res=>{
                console.log(res);
                uni.hideLoading();
                 this.taskList = res.data;
            })
            await userApi.getRechargeList().then(res=>{
                console.log(res);
                uni.hideLoading();
                this.exchangeList = res.data;
            })
           
        },

        

        // 兑换模拟数据
        getExchangeMockData() {
            return [
                { id: 1, points: 60, value: 'xx元' },
                { id: 2, points: 60, value: 'xx元' },
                { id: 3, points: 60, value: 'xx元' },
                { id: 4, points: 60, value: 'xx元' },
                { id: 5, points: 60, value: 'xx元' },
                { id: 6, points: 60, value: 'xx元' },
                { id: 7, points: 60, value: 'xx元' },
                { id: 8, points: 60, value: 'xx元' }
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
.integral-page {
    min-height: 100vh;
    background: linear-gradient(180deg, #E8F8F5 0%, #F8F8F8 100%);
    padding-bottom: 40rpx;
}

/* 我的积分卡片 */
.my-points-card {
    margin: 88rpx 30rpx 30rpx;
    // background: linear-gradient(135deg, #E8F8F5 0%, #B8E6D3 100%);
    background: #fff;
    border-radius: 20rpx;
    padding: 40rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.points-display {
    display: flex;
    align-items: center;
}

.points-circle {
    width: 80rpx;
    height: 80rpx;
    background-color: #FF9500;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.points-number {
    font-size: 28rpx;
    color: #fff;
    font-weight: 600;
}

/* 做任务赚积分 */
.task-section {
    margin: 0 30rpx 30rpx;
}

.section-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 600;
    margin-bottom: 20rpx;
    display: block;
}

.task-list {
    background-color: #fff;
    border-radius: 16rpx;
    overflow: hidden;
}

.task-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx 10rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }
}

.task-left {
    display: flex;
    align-items: center;
    gap: 20rpx;
    flex: 1;
}

.task-icon {
    width: 60rpx;
    height: 60rpx;
    background-color: #f0f8ff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.task-info {
    flex: 1;
    display: flex;
    align-items: center;
    .task-propres{
        width: 30%;
    }

}
::v-deep .u-line-progress__text{
    font-size: 20rpx;
}

.task-name {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 8rpx;
    display: block;
}

.task-reward {
    display: flex;
    align-items: center;
    margin-left: 4rpx;
}

.reward-icon {
    // background-color: #FF9500;
    border-radius: 20rpx;
    padding: 4rpx 12rpx;
    display: flex;
}

.reward-text {
    font-size: 22rpx;
    // color: #FF9500;
    font-weight: 500;
}

.task-right {
    flex-shrink: 0;
}

.task-btn {
    background-color: #14B19E;
    border-radius: 30rpx;
    padding: 10rpx 24rpx;
    height: 40rpx;
    line-height: 30rpx;

    &.completed {
        background-color: #f0f0f0;

        .btn-text {
            color: #999;
        }
    }
}

.btn-text {
    font-size: 24rpx;
    color: #fff;
    font-weight: 500;
}

/* 积分兑换 */
.exchange-section {
    margin: 0 30rpx 30rpx;
}

.exchange-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20rpx;
}

.exchange-item {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx 20rpx;
    text-align: center;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    // border: 1px solid #14b19f00;
}
.exchange-item-active {
    // border: 1px solid #14b19f;
    background: #14b17d1e;
}

.exchange-points {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: 8rpx;
    margin-bottom: 16rpx;
}

.points-text {
    font-size: 32rpx;
    color: #FF9500;
    font-weight: 600;
}

.points-unit {
    font-size: 22rpx;
    color: #FF9500;
}

.exchange-value {
    font-size: 24rpx;
    color: #666;
}

/* 充值方式 */
.recharge-section {
    margin: 0 30rpx 30rpx;
}

.recharge-options {
    display: flex;
    gap: 20rpx;
    margin-bottom: 20rpx;
}

.recharge-item {
    flex: 1;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx 20rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    //  border: 1rpx solid #14b19f00;
}
.recharge-item-active {
    // border: 1rpx solid #14b19e;
     background-color: #14b17d1e;
}

.recharge-text {
    font-size: 24rpx;
    color: #333;
    font-weight: 500;
}

.recharge-tip {
    text-align: center;
    font-size: 22rpx;
    color: #999;
    line-height: 1.5;
}

.link-text {
    color: #14B19E;
    text-decoration: underline;
}

/* 确认充值 */
.confirm-section {
    margin: 0 30rpx;
}

.confirm-btn {
    background-color: #14B19E;
    border-radius: 50rpx;
    padding: 24rpx;
    text-align: center;
    margin-bottom: 20rpx;
}

.confirm-text {
    font-size: 32rpx;
    color: #fff;
    font-weight: 600;
}

.order-info {
    text-align: center;
    font-size: 22rpx;
    color: #999;
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>
