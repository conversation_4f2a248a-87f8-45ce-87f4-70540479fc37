<template>
  <view class="container">
    <!-- 导航栏 -->
    <!-- <u-navbar :title="title" :autoBack="true"></u-navbar> -->
    
    <!-- 页面内容 -->
    <view class="content">
      <!-- 标题和提示 -->
      <view class="header">
        <text class="main-title">创作账号名称</text>
        <text class="sub-title">请勿使用含有特殊符号或明显营销推广意图的名称</text>
      </view>
      
      <!-- 当前名称展示 -->
      <view class="current-name">
        <u-input 
          v-model="currentName" 
          placeholder="请输入新的创作账号名称" 
          maxlength="20"
          @input="handleInput"
          border="true"
        ></u-input>
        <!-- <text class="name">{{ currentName }}</text> -->
        <text class="count">{{ nameLength }}/20</text>
      </view>
      
      <!-- 输入框 -->
      <view class="input-box">
        
      </view>
      
      <!-- 修改次数提示 -->
      <!-- <view class="modify-tips">
        <u-icon name="info-circle" size="16" color="#FF9900"></u-icon>
        <text>每月可修改2次（还剩余{{ remainingTimes }}次）</text>
      </view> -->
    </view>
    
    <!-- 保存按钮 -->
    <view class="footer">
      <u-button 
        type="primary" 
        @click="saveName"
        :disabled="!canSave"
      >保存</u-button>
    </view>
  </view>
</template>

<script>

import { onLoad } from 'uview-ui/libs/mixin/mixin';

export default {
    

  data() {
    return {
      title: '修改名称',
      currentName: '', // 当前名称
    //   newName: '', // 新名称
    //   remainingTimes: 2, // 剩余修改次数
      maxLength: 10 // 最大长度
    }
  },
  
  computed: {
   
    // 当前输入的长度
    nameLength() {
      return this.currentName.length;
    },
    // 是否可以保存
    canSave() {
         return this.currentName  && 
             this.currentName.length <= this.maxLength
    //   return this.newName && 
    //          this.newName !== this.currentName && 
    //          this.newName.length <= this.maxLength &&
    //          this.remainingTimes > 0;
    }
  },
  methods: {
     initbio(val){
        console.log(val,'val')
        this.currentName=val;
    },
    // 输入处理
    handleInput() {
      // 可以在这里添加名称格式校验
    },
    // 保存名称
    saveName() {
        this.$emit('newintroduce', this.currentName);
   
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f7f7f7;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

.content {
  padding: 30rpx;
}

.header {
  margin-bottom: 40rpx;
  
  .main-title {
    font-size: 36rpx;
    font-weight: bold;
    display: block;
    margin-bottom: 10rpx;
  }
  
  .sub-title {
    font-size: 26rpx;
    color: #999;
  }
}

.current-name {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 30rpx;
  
  .name {
    font-size: 32rpx;
    color: #333;
  }
  
  .count {
    font-size: 26rpx;
    color: #999;
  }
}

.input-box {
  margin-bottom: 40rpx;
  
  ::v-deep  .u-input {
    background-color: #fff;
    padding: 20rpx;
    border-radius: 8rpx;
  }
}

.modify-tips {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #FF9900;
  
  text {
    margin-left: 10rpx;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  
  ::v-deep  .u-button {
    width: 100%;
    
    &[disabled] {
      opacity: 0.6;
    }
  }
}
</style>