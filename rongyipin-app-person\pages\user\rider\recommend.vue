<template>
    <view class="recommend-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="我要推荐" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 推荐列表 -->
        <view class="recommend-list">
            <view
                v-for="(item, index) in recommendList"
                :key="item.id"
                class="recommend-item"
                :class="{ 'highlighted': item.isHighlighted }"
            >
                <!-- 职位信息 -->
                <view class="job-info">
                    <view class="job-header">
                        <text class="job-title">{{ item.name }}</text>
                        <text class="salary-range">{{ item.min_salary }}-{{ item.max_salary }} {{ item.salary_type_name }}</text>
                    </view>

                    <view class="job-details">
                        <text class="work-time">{{ item.company_name }}</text>
                        <text class="location">{{ item.size }}</text>
                        <text class="location">{{ item.company_address }}</text>
                    </view>

                    <view class="job-tags">
                        <text
                            v-for="tag in item.tags"
                            :key="tag"
                            class="tag"
                        >
                            {{ tag }}
                        </text>
                    </view>
                </view>

                <!-- 推荐人信息 -->
                <view class="recommender-info">
                    <view class="recommender-profile">
                        <image class="avatar" :src="item.avatarUrl" mode="aspectFill"></image>
                        <text class="recommender-name">{{ item.username }}</text>
                        <text>{{ item.job_position_name }}</text>
                    </view>

                    <view class="recommend" @click="handleRecommend(item)">
                        
                         <view v-show="item.recommendation_money">
                            <image class="recommend-money" src="@/static/jinbi.png" mode="aspectFill"></image>
                            <text>{{item.recommendation_money}}</text>
                        </view>
                        <text class="btn-text">推荐</text>
                        <view class="btn-text-box" v-show="item.recommendation_money">
                            推荐成功可获得现金{{item.recommendation_money}}元
                        </view>
                    </view>
                </view>

                <!-- 推荐描述 -->
                <!-- <view class="recommend-desc">
                    <text class="desc-text">{{ item.description }}</text>
                    <text class="contact-info">{{ item.contactInfo }}</text>
                </view> -->
            </view>
        </view>
    </view>
</template>

<script>
import { jobApi } from '@/utils/api.js'
export default {
    data() {
        return {
            recommendList: []
        }
    },
    onLoad() {
        this.loadRecommendData()
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack()
        },

        // 加载推荐数据
        async loadRecommendData() {
            await jobApi.recommendJobList().then(res => {
                uni.hideLoading()
                console.log(res);
                this.recommendList=res.data.data;
            })
            
        },

        // 处理推荐操作
        handleRecommend(item) {
            uni.navigateTo({
				url: '/pages/user/rider/recommendPerson?id='+item.id
			})
            console.log('推荐职位:', item)

            // TODO: 调用推荐API
            // await this.$api.recommendJob(item.id)

            // uni.showToast({
            //     title: '推荐成功',
            //     icon: 'success'
            // })
        },

        
       
    }
}
</script>

<style lang="scss" scoped>
.recommend-page {
    min-height: 100vh;
    background-color: #f8f8f8;
}

/* 顶部导航栏 */
.navbar {
   
}

/* 推荐列表 */
.recommend-list {
    // margin-top: 88rpx;
    padding: 20rpx;
}

/* 推荐卡片 */
.recommend-item {
    background-color: #fff;
    border-radius: 16rpx;
    margin-bottom: 20rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);

    &.highlighted {
        border: 2rpx solid #007AFF;
        box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.15);
    }
}

/* 职位信息 */
.job-info {
    margin-bottom: 24rpx;
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16rpx;

    .job-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
    }

    .salary-range {
        font-size: 28rpx;
        font-weight: 500;
        color: #007AFF;
    }
}

.job-details {
    display: flex;
    gap: 20rpx;
    margin-bottom: 16rpx;

    .work-time,
    .location {
        font-size: 26rpx;
        color: #666;
    }
}

.job-tags {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .tag {
        background-color: #f0f8ff;
        color: #007AFF;
        font-size: 22rpx;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        border: 1rpx solid #e6f3ff;
    }

    .star-icon {
        font-size: 24rpx;
        margin-left: auto;
    }
}

/* 推荐人信息 */
.recommender-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
}

.recommender-profile {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background-color: #f0f0f0;
    }

    .recommender-name {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
    }
}

.recommend {
    text-align: center;
   
   .recommend-money{
      width: 26rpx;
      height: 26rpx;
   }
   .btn-text-box{
    color: #999;
   }
    .btn-text {
         background-color: #14B19E;
        font-size: 26rpx;
        color: #fff;
        font-weight: 500;
         border-radius: 30rpx;
    padding: 16rpx 32rpx;
    }
}

/* 推荐描述 */
.recommend-desc {
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;

    .desc-text {
        font-size: 24rpx;
        color: #666;
        margin-bottom: 8rpx;
        display: block;
    }

    .contact-info {
        font-size: 24rpx;
        color: #999;
        display: block;
    }
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>
