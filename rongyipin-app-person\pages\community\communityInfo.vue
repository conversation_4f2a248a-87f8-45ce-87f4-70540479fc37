<template>
  <view class="article-container">
    <u-navbar :autoBack="true" title="" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <!-- 文章内容 -->
    <scroll-view scroll-y >
      <!-- <view class="article-title">{{ article.title }}</view> -->
      <view class="article-meta">
        <view  class="cell-title">
            <u-avatar class="avatar" :src="article.avatarUrl"></u-avatar>
                   
            <text class="username"> {{ article.username }} </text>
        </view>
        <!-- <text>{{ article.username }}</text> -->
        <!-- <text style="margin-left: 20rpx;">{{ article.updated_at }}</text> -->
      </view>
      <view class="article-content">
        <rich-text :nodes="article.content"></rich-text>
        <!-- <image :src="article.avatarUrl" mode="aspectFill" class="preview-image"></image> -->
      </view>

      <!-- 分割线 -->
      <u-line margin="40rpx 0"></u-line>

      <!-- 评论列表 -->
      <view class="comments-section">
        <!-- <text class="section-title">评论</text> -->
        <scroll-view class="list-container" scroll-y="true" >
            <view v-for="(comment, index) in comments" :key="index">
                <view class="cell-title">
                    <u-avatar class="avatar" :src="comment.avatarUrl"></u-avatar>
                    <text class="username"> {{ comment.username }} </text>
                </view>

                <view class="comment_text" @click="reply(comment)">{{comment.comment_text}}</view>
                <view class="created_at">{{comment.created_at}}</view>
                <!-- <u-input v-if="isReplyComment" v-model="newComment" :placeholder="replyPlaceholder"  @confirm="submitComment"/> -->
                <view v-for="(children, index) in comment.children" class="comment_children">
                    <view class="cell-title">
                        <u-avatar class="avatar" :src="children.avatarUrl"></u-avatar>
                        <text class="username"> {{ children.username }} </text>
                        <u-icon name="play-right-fill" size="18" class="arrow-right"></u-icon>
                        <text class="username"> {{ children.huifu_username }} </text>
                    </view>
                    <view class="children_text" @click="reply(children)">{{children.comment_text}}</view>
                    <view class="children_at">{{children.created_at}}</view>
                    <!-- <u-input v-if="isReplylist" v-model="newComment" :placeholder="replyPlaceholder"  @confirm="submitComment"/> -->
                </view>
            </view>
        </scroll-view>
        <!-- <u-list>
          <u-list-item v-for="(comment, index) in comments" :key="index">
            <u-cell :title="comment.username" :label="comment.content">
              <image slot="icon" :src="comment.avatar" mode="aspectFill" style="width: 60rpx; height: 60rpx; border-radius: 50%; margin-right: 20rpx;"></image>
              <text slot="label" style="color: #999;">{{ comment.time }}</text>
            </u-cell>
          </u-list-item>
        </u-list> -->
      </view>
    </scroll-view>

    <!-- 底部操作栏 -->
    <view class="footer-bar">
      <u-input v-model="newComment" :placeholder="replyPlaceholder"  @confirm="submitComment"/>
      <u-icon name="chat" size="32"  style="margin: 0 20rpx;"></u-icon>
      <text class="interaction-num">{{ article.comment_count }}</text>
      <u-icon :name="isLiked ? 'heart-fill' : 'heart'" :color="isLiked ? '#ff4949' : '#666'" size="32" @click="toggleLike" style="margin: 0 20rpx;"></u-icon>
      <text class="interaction-num">{{ article.like_count }}</text>
      <u-icon :name="isCollected ? 'star-fill' : 'star'" :color="isCollected ? '#f7ba2a' : '#666'" size="32" @click="toggleCollect" style="margin: 0 20rpx;"></u-icon>
      <text class="interaction-num">{{ article.collect_count }}</text>
    </view>
    
   
  </view>
</template>

<script>
import {communityApi } from '@/utils/api'
export default {
  data() {
    return {
      
      id: '',
      // 文章数据
      article: {
        // title: "如何高效学习编程？",
        // author: "知乎用户·张三",
        // time: "2025-04-05",
        // content: '<div style="word-break: break-all;"><p>在当今快速发展的科技时代，掌握一门编程语言是必不可少的技能。</p><p>无论你是初学者还是资深开发者，持续学习和实践都是提高的关键。</p></div>'
      },
      // 评论列表
      comments: [],
      newComment: '', // 新评论输入框内容
      isLiked: false, // 是否已点赞
      isCollected: false, // 是否已收藏
      replyPlaceholder:'写评论...',
       isReply: false, // 控制弹窗显示
       replyItem: {},
      // replyContent: '', // 回复内容
      // selectedComment: null, // 当前选中的评论对象
    };
  },
  onLoad(option) { 
    this.id = option.id;
    this.infoData(option.id)
  },
  methods: {
     // 打开回复弹窗
    reply(comment) {
      this.isReply = true;
      this.replyItem = comment;
      this.replyPlaceholder='回复 @' + comment.username + ':';

      // this.$nextTick(() => {
      //   this.$refs.inputRef.focus();
      // });
      // this.selectedComment = comment;
      // this.showReplyModal = true;
    },



    
    // 获取信息
    async infoData(id){
       await communityApi.getCommunityListInfo({id:id}).then(res => {
            uni.hideLoading();
            this.article={...res.data.data[0]};
            console.log(res.data.data[0],'获取信息成功')
            this.comments=res.data.data[0].comments;
        //   this.info = res.data
        })
        
    },

    // 提交评论
    async submitComment() {
      console.log(this.newComment,'提交评论');
      let params={}
      if(this.isReply){
        params={
          "content_id":this.article.id,
          "content":this.newComment.trim(),
          "comment_id":this.replyItem.id,
          "action_user_id":this.article.article_user_id,
          "huifu_user_id":this.replyItem.user_id,
          'pid':this.replyItem.pid==0?this.replyItem.id:this.replyItem.pid,
        }
      }else{
        params={
          "content_id":this.article.id,
          "content":this.newComment.trim(),
          "comment_id":0,
          "action_user_id":this.article.article_user_id,
          "huifu_user_id":0
        }
      }
      //  this.newComment.trim();
      // const params={
      //   "content_id":this.article.id,
      //   "content":this.newComment.trim(),
      //   "comment_id":0,
      //   "action_user_id":this.article.article_user_id,
      //   "huifu_user_id":0
      // }
      await communityApi.addComment({...params}).then(res => {
            uni.hideLoading();
            // this.article={...res.data.data[0]};
            console.log(res,'成功');
            this.infoData(this.id);
            this.newComment = '';
            this.isReply=false;
            this.replyPlaceholder='写评论...';
            
        })

    },
    // 切换点赞状态
    toggleLike() {
      this.isLiked = !this.isLiked;
      uni.showToast({ title: this.isLiked ? '已点赞' : '取消点赞' });
    },
    // 切换收藏状态
    toggleCollect() {
      this.isCollected = !this.isCollected;
      uni.showToast({ title: this.isCollected ? '已收藏' : '取消收藏' });
    },
    // 格式化时间
    formatTime(date) {
      return date.getFullYear() + '-' + 
        ('0' + (date.getMonth() + 1)).slice(-2) + '-' + 
        ('0' + date.getDate()).slice(-2);
    }
  }
};
</script>

<style lang="scss" scoped>
.article-container {
  display: flex;
  flex-direction: column;
  padding: 10rpx 20rpx;
  height: 100vh;
//   background-color: #f8f8f8;
}
.preview-image{
  width: 200rpx;
  height: 200rpx;
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.article-meta {
  color: #999;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.article-content {
    padding: 0 10rpx;
  font-size: 30rpx;
  line-height: 1.6;
  color: #333;
}
.cell-title {
    display: flex;
    align-items: center;
	margin-bottom: 20rpx;
	.avatar{
		margin-right: 16rpx;
	}
    .username {
      font-size: 32rpx;
      font-weight: bold;
      margin-right: 10rpx;
    }
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
  display: block;
}
.list-container{
    height: 630rpx;

}
.comment_text{
    padding-left: 50rpx;
    font-size: 24rpx;
}
.created_at{
    font-size: 24rpx;
    padding-left: 50rpx;
    color: #999;
}
.comment_children{
    .cell-title{
        margin-bottom: 2rpx;
        align-items: inherit;
    }
    .username{
        font-size: 26rpx;
    }
    padding-left: 50rpx;
    .children_text{
        padding-left: 50rpx;
        font-size: 24rpx;
    }
    .children_at{
        padding-left: 50rpx;
        font-size: 24rpx;
        color: #999;
    }
    
}
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  z-index: 999;
}




.footer-bar {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
}
</style>

