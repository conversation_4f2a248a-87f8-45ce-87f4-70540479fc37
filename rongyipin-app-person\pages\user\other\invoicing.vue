<template>
    <view class="invoice-page">
        <!-- 顶部导航栏 -->
        <!-- <view class="navbar"> -->
           <u-navbar title="我的发票" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        <!-- </view> -->

        <!-- Tab切换 -->
        <view class="tab-container">
            <view class="tab-wrapper">
                <view
                    class="tab-item"
                    :class="{ 'active': currentTab === 'invoice' }"
                    @click="switchTab('invoice')"
                >
                    <text class="tab-text">去开票</text>
                </view>
                <view
                    class="tab-item"
                    :class="{ 'active': currentTab === 'record' }"
                    @click="switchTab('record')"
                >
                    <text class="tab-text">开票记录</text>
                </view>
            </view>
        </view>

        <!-- 去开票页面 -->
        <view v-if="currentTab === 'invoice'" class="invoice-content">
            <!-- 发票列表 -->
            <scroll-view class="list-container" scroll-y="true" v-if="invoiceList.length > 0">
                <view class="invoice-list">
                    <view
                        v-for="(item, index) in invoiceList"
                        :key="item.id"
                        class="invoice-item"
                        :class="{ 'selected': item.selected, 'highlighted': item.highlighted }"
                    >
                        <view class="item-left">
                            <view class="radio-btn" @click="selectInvoice(item)">
                                <view v-if="item.selected" class="radio-checked"></view>
                            </view>
                            <view class="invoice-info">
                                <text class="date">{{ item.created_at }}</text>
                                <text class="type">{{ item.product_name }}</text>
                                <text class="order-no">订单号：{{ item.order_id }}</text>
                            </view>
                        </view>
                        <view class="item-right">
                            <text class="amount">{{ item.amount }}</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
            <view v-else-if="invoiceList.length == 0" class="no-data">
                    <u-empty mode="list" icon="msg_bg.png"></u-empty>
            </view>
            <!-- 底部操作区 -->
            <view class="bottom-section">
                <view class="total-section">
                    <view class="total-left">
                        <view class="select-all" @click="toggleSelectAll">
                            <view class="radio-btn">
                                <view v-if="isAllSelected" class="radio-checked"></view>
                            </view>
                            <text class="select-text">全选</text>
                        </view>
                    </view>
                    <view class="total-right">
                        <text class="total-amount">¥ {{ totalAmount }}</text>
                    </view>
                </view>

                <view class="action-buttons">
                    <view class="btn-group">
                        <!-- <view class="action-btn delete-btn">
                            <u-icon name="trash" size="36" color="#666"></u-icon>
                            <text class="btn-text">删除</text>
                        </view>
                        <view class="action-btn share-btn">
                            <u-icon name="share" size="36" color="#666"></u-icon>
                            <text class="btn-text">分享</text>
                        </view> -->
                    </view>
                    <view class="apply-btn" @click="applyInvoice">
                        <text class="apply-text">去申请</text>
                    </view>
                </view>
            </view>
        </view>

        <!-- 开票记录页面 -->
        <view v-if="currentTab === 'record'" class="record-content">
            <scroll-view class="record-list-container" scroll-y="true" v-if="recordList.length > 0">
                <!-- 记录列表 -->
                <view class="record-list">
                    <view
                        v-for="(item, index) in recordList"
                        :key="item.id"
                        class="record-item"
                        :class="{ 'highlighted': item.highlighted }"
                    >
                        <view class="record-info">
                            <text class="date">{{ item.create_time }}</text>
                            <text class="order-no">开票主体：{{ item.type==1?'个人':'企业' }}</text>
                            <text class="project-info">发票抬头：{{ item.company_name }}</text>
                            <text class="amount">开票金额：￥{{ item.price }}</text>
                        </view>
                        <view class="record-action">
                            <!-- <view class="download-btn" @click="downloadInvoice(item)">
                                <text class="download-text">下载发票</text>
                            </view> -->
                            <text class="status-text">{{item.status==0?'待开具':'已开具'}}</text>
                        </view>
                    </view>
                </view>
            </scroll-view>
             <view v-else-if="recordList.length == 0" class="no-data">
                    <u-empty mode="list" icon="msg_bg.png"></u-empty>
            </view>
        </view>

        <!-- 发票抬头 - 固定在底部 -->
        <!-- <view v-if="currentTab === 'record'" class="invoice-header-bottom">
            <view class="header-title">
                <text class="title">发票抬头</text>
                <text class="view-all" @click="viewAllHeaders">查看全部 ></text>
            </view>
            <view class="company-info">
                <text class="company-name">xxxx公司</text>
                <text class="company-type">默认</text>
            </view>
        </view> -->
    </view>
</template>

<script>
import { invoice } from '@/utils/api.js'
export default {
    data() {
        return {
            currentTab: 'invoice', // 当前选中的tab
            invoiceList: [], // 发票列表
            recordList: [], // 开票记录列表
            isAllSelected: false, // 是否全选
            selectList: [],
        }
    },
    computed: {
        // 计算总金额
        totalAmount() {
            return this.invoiceList
                .filter(item => item.selected)
                .reduce((total, item) => total + parseFloat(item.amount.replace('¥', '').replace('元', '')), 0)
                .toFixed(2)
        }
    },
    onShow() {
        this.loadInvoiceData()
        this.loadRecordData()
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack()
        },

        // 切换tab
        switchTab(tab) {
            this.currentTab = tab
        },

        // 选择发票
        selectInvoice(item) {
            item.selected = !item.selected
            this.checkAllSelected()
        },

        // 切换全选
        toggleSelectAll() {
            this.isAllSelected = !this.isAllSelected
            
            this.invoiceList.forEach(item => {
                item.selected = this.isAllSelected; 
            })
        },

        // 检查是否全选
        checkAllSelected() {
            this.isAllSelected = this.invoiceList.every(item => item.selected)
        },

        // 申请开票
        applyInvoice() {
            const selectedItems = this.invoiceList.filter(item => item.selected)
            if (selectedItems.length === 0) {
                uni.showToast({
                    title: '请选择要开票的项目',
                    icon: 'none'
                })
                return
            }
            console.log('selectedItems', selectedItems);
            const str=[]
            selectedItems.map(item => {
                str.push(item.order_id)
            })
            // console.log('str', );
            uni.navigateTo({
                 url: './application?invoiceList=' + str.join(',')+'&totalAmount='+this.totalAmount
            });
        },

        // 下载发票
        downloadInvoice(item) {
            console.log('下载发票:', item)
            uni.showToast({
                title: '下载中...',
                icon: 'loading'
            })
        },

        // 查看全部发票抬头
        viewAllHeaders() {
            console.log('查看全部发票抬头')
        },

        // 加载发票数据
        async loadInvoiceData() {
             await invoice.getOrderList().then(res=>{
                console.log(res);
                uni.hideLoading();
                this.invoiceList=[]
                res.data.list.map(item=>{
                    // if(item.is_invoice==0){
                        item.selected=false;
                        // this.invoiceList.push(item)
                    // }
                })
                this.invoiceList = res.data.list;
            })
            // try {
            //     // TODO: 替换为实际的API接口
            //     // const response = await this.$api.getInvoiceList()
            //     // this.invoiceList = response.data

            //     // 模拟数据
            //     this.invoiceList = this.getInvoiceMockData()
            // } catch (error) {
            //     console.error('加载发票数据失败:', error)
            // }
        },

        // 加载开票记录数据
        async loadRecordData() {
           await invoice.getInvoiceList().then(res=>{
                console.log(res);
                uni.hideLoading();
                
                this.recordList = res.data;
            })
            // try {
            //     // TODO: 替换为实际的API接口
            //     // const response = await this.$api.getRecordList()
                // this.recordList = response.data


            //     // 模拟数据
            //     this.recordList = this.getRecordMockData()
            // } catch (error) {
            //     console.error('加载记录数据失败:', error)
            // }
        },

        // 发票模拟数据
        getInvoiceMockData() {
           
            // return [
            //     {
            //         id: 1,
            //         date: '2020.05.05',
            //         type: '充值积分：1000',
            //         orderNo: '111111111111111',
            //         amount: '¥100元',
            //         selected: false,
            //         highlighted: false
            //     },
            //     {
            //         id: 2,
            //         date: '2020.05.05',
            //         type: '充值积分',
            //         orderNo: '111111111111111',
            //         amount: '¥100元',
            //         selected: false,
            //         highlighted: false
            //     },
            //     {
            //         id: 3,
            //         date: '2020.05.05',
            //         type: '充值会员',
            //         orderNo: '111111111111111',
            //         amount: '¥100元',
            //         selected: false,
            //         highlighted: false
            //     },
            //     {
            //         id: 4,
            //         date: '2020.05.05',
            //         type: '充值会员',
            //         orderNo: '111111111111111',
            //         amount: '¥100元',
            //         selected: false,
            //         highlighted: false
            //     }
            // ]
        },

        // 开票记录模拟数据
        getRecordMockData() {
            return [
                {
                    id: 1,
                    date: '2020.05.05',
                    orderNo: '111111111111111',
                    projectInfo: 'xx服务专业设计服务',
                    amount: '¥368',
                    highlighted: false
                },
                {
                    id: 2,
                    date: '2020.05.05',
                    orderNo: '111111111111111',
                    projectInfo: 'xx服务专业设计服务',
                    amount: '¥368',
                    highlighted: false
                },
                {
                    id: 3,
                    date: '2020.05.05',
                    orderNo: '111111111111111',
                    projectInfo: 'xx服务专业设计服务',
                    amount: '¥368',
                    highlighted: true
                }
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
.invoice-page {
    height: 100vh;
    background: linear-gradient(180deg, #E8F8F5 0%, #F8F8F8 100%);
    display: flex;
    flex-direction: column;
    .list-container {
        height: calc(100vh - 450rpx);
    }
    .record-list-container{
         height: calc(100vh - 300rpx);
    }
    ::v-deep .u-empty{
         height: calc(100vh - 450rpx);
    }
}

/* 顶部导航栏 */
.navbar {
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // height: 88rpx;
    // background-color: transparent;
    // padding: 0 30rpx;
    // position: fixed;
    // top: 0;
    // left: 0;
    // right: 0;
    // z-index: 999;

    .nav-left,
    .nav-right {
        width: 120rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
    }

    .nav-right {
        justify-content: flex-end;

        .right-text {
            font-size: 24rpx;
            color: #666;
        }
    }

    .nav-title {
        flex: 1;
        text-align: center;

        .title-text {
            font-size: 32rpx;
            font-weight: 500;
            color: #333;
        }
    }
}

/* Tab切换 */
.tab-container {
    // margin-top: 88rpx;
    padding: 30rpx;
}

.tab-wrapper {
    display: flex;
    background-color: #f0f0f0;
    border-radius: 50rpx;
    padding: 6rpx;
}

.tab-item {
    flex: 1;
    height: 70rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 44rpx;
    transition: all 0.3s ease;

    &.active {
        background-color: #333;

        .tab-text {
            color: #fff;
        }
    }

    .tab-text {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
    }
}

/* 去开票内容 */
.invoice-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 0 30rpx;
}

/* 发票列表 */
.invoice-list {
    flex: 1;
    margin-bottom: 20rpx;
}

.invoice-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    border: 2rpx solid #14b19f00;
    &.selected {
        background-color: #f0f9f6;
        border: 2rpx solid #14B19E;
    }

    &.highlighted {
        border: 2rpx solid #007AFF;
        background-color: #f0f8ff;
    }
}

.item-left {
    display: flex;
    align-items: center;
    gap: 20rpx;
    flex: 1;
}

.radio-btn {
    width: 40rpx;
    height: 40rpx;
    border: 2rpx solid #ddd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .radio-checked {
        width: 24rpx;
        height: 24rpx;
        background-color: #14B19E;
        border-radius: 50%;
    }
}

.invoice-info {
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    .date {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
    }

    .type {
        font-size: 26rpx;
        color: #666;
    }

    .order-no {
        font-size: 24rpx;
        color: #999;
    }
}

.item-right {
    .amount {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
    }
}

/* 底部操作区 */
.bottom-section {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    padding: 30rpx;
    margin: 0 -30rpx;
}

.total-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30rpx;
}

.total-left {
    .select-all {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .select-text {
            font-size: 28rpx;
            color: #333;
        }
    }
}

.total-right {
    .total-amount {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
    }
}

.action-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20rpx;
}

.btn-group {
    display: flex;
    gap: 30rpx;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;

    .btn-text {
        font-size: 24rpx;
        color: #666;
    }
}

.apply-btn {
    background-color: #333;
    border-radius: 50rpx;
    padding: 20rpx 40rpx;

    .apply-text {
        font-size: 28rpx;
        color: #fff;
        font-weight: 500;
    }
}

/* 开票记录内容 */
.record-content {
    flex: 1;
    padding: 0 30rpx;
    // padding-bottom: 200rpx; /* 为底部发票抬头留出空间 */
}

.record-list {
    margin-bottom: 40rpx;
}

.record-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;

    &.highlighted {
        border: 2rpx solid #007AFF;
        background-color: #f0f8ff;
    }
}

.record-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    .date {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
    }

    .order-no {
        font-size: 24rpx;
        color: #999;
    }

    .project-info {
        font-size: 26rpx;
        color: #666;
    }

    .amount {
        font-size: 28rpx;
        color: #ff4444;
        font-weight: 500;
    }
}

.record-action {
    .status-text{
            font-size: 24rpx;
            color: #bbbbbb;
        }
    .download-btn {
        background-color: #333;
        border-radius: 30rpx;
        padding: 16rpx 24rpx;

        .download-text {
            font-size: 24rpx;
            color: #fff;
        }
        
    }
}

/* 发票抬头 - 底部固定 */
.invoice-header-bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    // width: 100%;
    background-color: #fff;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    z-index: 100;
}

.header-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;

    .title {
        font-size: 28rpx;
        color: #333;
        font-weight: 500;
    }

    .view-all {
        font-size: 24rpx;
        color: #666;
    }
}

.company-info {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .company-name {
        font-size: 26rpx;
        color: #333;
    }

    .company-type {
        font-size: 24rpx;
        color: #999;
    }
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>
