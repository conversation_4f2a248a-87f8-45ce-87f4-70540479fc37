<template>
  <view class="container">
    <!-- 搜索框 -->
    <view class="search-box">
        <view><u-icon name="arrow-left" color="#606266" size="28" @click="goBack"></u-icon></view>
      
      <u-search
        v-model="keyword"
        placeholder="请输入职位/公司名称"
        :show-action="true"
        action-text="搜索"
        @search="doSearch"
        @custom="doSearch"
      ></u-search>
    </view>

    <!-- 主Tab栏 - 职位/公司 -->
    <view class="main-tabs">
      <u-tabs
        :list="mainTabs"
        :current="currentMainTab"
        @change="changeMainTab"
        bar-width="100"
        bar-height="4"
        active-color="#2979ff"
        inactive-color="#606266"
      ></u-tabs>
    </view>

    <!-- 子Tab栏 - 邯郸/最新/附近 -->
    <view class="sub-tabs">
      <u-tabs
        :list="subTabs"
        :current="currentSubTab"
        @change="changeSubTab"
        bar-width="80"
        bar-height="3"
        active-color="#2979ff"
        inactive-color="#909399"
        font-size="26"
      ></u-tabs>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <!-- 职位列表 -->
      <view v-if="currentMainTab === 0">
        <job-list 
          :list="jobData" 
          :loading="loading"
          :sub-tab="currentSubTab"
        ></job-list>
      </view>

      <!-- 公司列表 -->
      <view v-else>
        <company-list 
          :list="companyData" 
          :loading="loading"
          :sub-tab="currentSubTab"
        ></company-list>
      </view>
    </view>
  </view>
</template>

<script>
// 引入组件
import JobList from './components/search/job-list.vue'
import CompanyList from './components/search/company-list.vue'
import {jobApi} from '@/utils/api.js'
export default {
  components: {
    JobList,
    CompanyList
  },
  data() {
    return {
      keyword: '', // 搜索关键词
      currentMainTab: 0, // 当前主Tab索引
      currentSubTab: 0, // 当前子Tab索引
      loading: false, // 加载状态
      mainTabs: [
        { name: '职位' },
        { name: '公司' }
      ],
      subTabs: [
        { name: '邯郸',
        // badge: {
        //     isDot: true
        //     } 
        },
        { name: '最新' },
        { name: '附近' }
      ],
      jobData: [], // 职位数据
      companyData: [], // 公司数据
      city: {},
    }
  },
  onLoad(options) {
    this.city ={id: options.cityId,name: options.cityName};
    this.subTabs[0]=this.city;
    console.log(this.subTabs);
    this.loadData()
  },
  methods: {
    //返回
    goBack() {
      uni.navigateBack({
        delta: 1 // 返回的页面数，这里设置为1表示返回上一页
      });
    },
    // 切换主Tab
    changeMainTab(value) {
        // console.log(value, index)
      this.currentMainTab = value.index
      this.currentSubTab = 0 // 重置子Tab
      this.loadData()
    },
    
    // 切换子Tab
    changeSubTab(value) {
        // console.log(value, index)
      this.currentSubTab = value.index
      this.loadData()
    },
    
    // 执行搜索
    doSearch() {
      this.loadData()
    },
    
    // 加载数据
    async loadData() {
      this.loading = true
      
      try {
        if (this.currentMainTab === 0) {
          // 获取职位数据
        //   this.jobData = await this.fetchJobData()
        this.fetchJobData()
        } else {
          // 获取公司数据
          this.fetchCompanyData()
        //   this.companyData = await this.fetchCompanyData()
        }
      } catch (error) {
        console.error('数据加载失败:', error)
        uni.showToast({
          title: '数据加载失败',
          icon: 'none'
        })
      } finally {
        this.loading = false
      }
    },
    
    // 模拟获取职位数据
    async fetchJobData() {
   
      const params={
        name:this.keyword,
      }
      const subTab = this.subTabs[this.currentSubTab].name
      if(subTab===this.city.name){
        params['city_id']=this.city.id;
      }else if(subTab==='最新'){
        params['none']=3;
      }else if(subTab==='附近'){
        params['none']=2;
      }

      await jobApi.getJobListByCompany(params).then(res => { 
        console.log("获取公司数据", res);
        uni.hideLoading();
        if(res.code == 200){
            this.jobData = res.data.data
        }else{
            uni.showToast({
                title: res.msg,
                icon: 'none'
            })
        }
      })
    },
    
    // 模拟获取公司数据
    async fetchCompanyData() {
        const params={
            name:this.keyword,
        }
      const subTab = this.subTabs[this.currentSubTab].name
      if(subTab===this.city.name){
        params['city_id']=this.city.id;
      }else if(subTab==='最新'){
        params['none']=3;
      }else if(subTab==='附近'){
        params['none']=2;
      }

      await jobApi.searchCompany(params).then(res => { 
        console.log("获取公司数据", res);
        uni.hideLoading();
         if(res.code == 200){
            this.companyData = res.data
         }else{
           uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
      })
    
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-box {
    display: flex;
    align-items: center;
  background-color: #fff;
  padding: 20rpx 6rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.main-tabs {
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.sub-tabs {
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.content {
  background-color: #fff;
  border-radius: 10rpx;
  padding: 20rpx;
}
// ::v-deep .u-badge{
//     border-top-right-radius: 0; 
//     border-top-left-radius: 0;
//      border-bottom-left-radius: 0;
//      border-bottom-right-radius: 0;
//      clip-path: polygon(100% 0%, 0% 100%, 100% 100%);
// }
// ::v-deep .u-badge-dot{
//    clip-path: polygon(100% 0%, 0% 100%, 100% 100%);

// }
// ::v-deep .u-badge--error{
//     background-color: #707070;
// }
</style>