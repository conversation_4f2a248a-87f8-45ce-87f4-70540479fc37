<template>
    <view>
        <view class="container">
            <!-- 左侧当前城市 -->
            <view class="left-panel">
            <!-- <view class="city-title">当前城市</view> -->
            <view class="city-name">{{ currentCity }}</view>
            </view>

            <!-- 右侧区县列表 -->
            <view class="right-panel">
            <!-- <view class="district-title">区域选择</view> -->
            <view class="district-list">
                <!-- 全选行 -->
                <view class="district-item" @click="toggleSelect('all')">
                <text>全{{ currentCity.slice(0,-1) }}</text>
                <u-icon name="checkmark" color="#4CAF50" size="20" v-if="isAllSelected"></u-icon>
                </view>

                <!-- 区县行 -->
                <view 
                class="district-item" 
                v-for="district in districts" 
                :key="district.id"
                @click="toggleSelect(district.id)"
                >
                <text>{{ district.name }}</text>
                <u-icon name="checkmark" color="#4CAF50" size="20" v-if="selectedDistricts.includes(district.id)"></u-icon>
                </view>
            </view>
            </view>
            
        </view>
        <view class="footer">
                <u-button type="primary" @click="confirmSelect">确认</u-button>
        </view>
    </view>
</template>

<script>
import {jobApi } from '@/utils/api'
export default {
  data() {
    return {
      // 当前城市名称
      currentCity: '北京市',

      // 区县数据源
      districts: [],

      // 已选择的区县，默认全选
      selectedDistricts: [130402,130403],
    };
  },
  computed: {
    // 判断是否选中“全部”
    isAllSelected() {
      return this.selectedDistricts.includes('all');
    }
  },
  onLoad() {
      // 回显数据
    //   this.selectedDistricts = [...res.districts];
  },

  methods: {
    // 获取ip数据
    //传过来城市和选中的区县
    async getIpdata(val,chosequ){
        console.log("chosequ",chosequ);
        this.selectedDistricts=[]
        chosequ.map(item=>{
            this.selectedDistricts.push(item.id)
        })
        
        this.currentCity = val.city_classname;
        // uni.showloading({title:'加载中'})

        await jobApi.getAreaByCityId({city_id:val.city_classid}).then((res) => { 
             uni.hideloading();
            console.log('根据市code获取区列表',res)
            if(res.code == 200){
              this.districts = res.data;
              this.districts.filter(d => this.selectedDistricts.includes(d.id));
            }else{
              uni.showToast({
                title: res.msg,
                icon: 'none'
              });
            }
            
        })
    },
    // 处理点击
    toggleSelect(val) {
        console.log('点击了',val)
      const allVal = 'all';

      if (val === allVal) {
        // 点击“全部”：只保留“全部”，清空所有区县
        this.selectedDistricts = [allVal];
      } else {
        // 处理点击某个区县
        const index = this.selectedDistricts.indexOf(val);

        if (index > -1) {
          this.selectedDistricts.splice(index, 1);
        } else {
          this.selectedDistricts.push(val);
        }

        // 如果有区县被取消，自动取消“全部”状态
        if (!this.selectedDistricts.every(v => v === allVal)) {
          const allIndex = this.selectedDistricts.indexOf(allVal);
          if (allIndex > -1) {
            this.selectedDistricts.splice(allIndex, 1);
          }
        }
      }
    },
    // 确认选择
    confirmSelect() {
        // 获取已选中的区县数据（排除 'all'）
        const selectedItems = this.districts.filter(d => this.selectedDistricts.includes(d.id));

        // 打印调试信息
        console.log('选中的区县:', selectedItems);

        // 如果你需要传递给父组件或其他页面：
        // 方式一：通过事件总线
        this.$emit('searchdata', selectedItems);

        // 方式二：通过 uni.$emit 触发全局事件
        // uni.$emit('districtSelected', {
        // districts: selectedItems
        // });

        // 关闭弹窗或跳转逻辑可自行添加
        uni.showToast({ title: '已确认选择', icon: 'none' });
    }
  }
};
</script>
<style scoped>
.container {
  display: flex;
  padding: 20rpx;
  height: 100%;
}

.left-panel {
  flex: 1;
  padding: 20rpx;
  border-right: 1px solid #eee;
}

.city-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.city-name {
  font-size: 28rpx;
  color: #333;
}

.right-panel {
  flex: 2;
  padding: 0 20rpx;
}

.district-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.district-list {
  display: flex;
  flex-direction: column;
}

.district-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  border-bottom: 1px solid #eee;
}
.footer {
  padding: 20rpx;
  background-color: #fff;
  border-top: 1px solid #eee;
  position: sticky;
  bottom: 0;
}
</style>