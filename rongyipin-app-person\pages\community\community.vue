<template>
  <view class="home-container">
	<view class="home-header">
		<!-- <u--image src="https://uviewui.com/album/1.jpg" width="20px" height="20px" @click="homefunc" shape="circle"></u--image> -->
     <u-avatar class="avatar" :src="myavatar" @click="homefunc"></u-avatar>
		<u-tabs 
			:list="tabList" 
			:current="currentTab" 
			@change="handleTabChange"
			:activeStyle="{ fontWeight: 'bold'}"
		></u-tabs>
		<u-icon name="search" @click="search"></u-icon>
	</view>
    <!-- 顶部导航 -->
    

    <!-- 推荐列表 -->
    <view v-if="currentTab === 0" class="recommend-list">
      <scroll-view class="list-container" scroll-y="true" refresher-enabled="true"
				:refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
				:lower-threshold="50" >
		<!-- <u-cell 
          v-for="(item, index) in recommendList" 
          :key="index"
          :label="item.content"
          :border-bottom="false"
        > -->
		    <view  v-for="(item, index) in recommendList" 
          :key="index" class="item-list">
          <view  class="cell-title">
            <view style="display: flex;" @click="gohomepage(item)">
              <u-avatar class="avatar" :src="item.avatarUrl"></u-avatar>
              <text class="username"> {{ item.username }} </text>
            </view>
            <view>
              <u-button color="#059f9f" size="mini" @click="follow(item)" shape="circle" :hairline="true" v-if="item.is_gz==0" >+ 关注</u-button>
              <u-button color="#ddd" size="mini" shape="circle" :hairline="false" v-else >已关注</u-button>
            </view>
			      

            <!-- <text class="company">{{ item.company }}</text> -->
          </view>
          <!-- <view slot="label" class="cell-content"> -->
            <view @click="seeAll(item)">
                <!-- 内容部分 -->
                <view 
                  class="content"
                  :class="{'content-collapsed': !item.expanded && item.showExpand}"
                  
                >
                  <text>{{ item.content }}</text>
				          <text v-if="item.showExpand">...</text>
                </view>
                <view 
                  class="expand-qw" 
				          
                  v-if="item.showExpand"
                >
                  <text>全文</text> 
                </view>
                <!-- 展开/收起按钮 -->
                <!-- <view 
                  class="expand-btn" 
                  v-if="item.showExpand"
                  @click="toggleExpand(index)"
                >
                  <text>{{ item.expanded ? '收起' : '全文' }}</text>
                  <u-icon 
                    :name="item.expanded ? 'arrow-up' : 'arrow-down'" 
                    size="14" 
                    color="#14b19e"
                  ></u-icon>
                </view> -->
            </view>
          <!-- </view> -->
          <!-- <view slot="footer" class="cell-footer" >
            <u-button type="primary" size="mini">全文</u-button>
            <u-icon name="question-circle-fill" color="#007aff"></u-icon>
            <text>面试官的哪些问题让求职者很头疼</text>
          </view> -->
		</view>
        <!-- </u-cell> -->
	  </scroll-view>
        
    
    </view>

    <!-- 关注列表 -->
    <view v-if="currentTab === 1">
      <scroll-view class="list-container" scroll-y="true" refresher-enabled="true"
				:refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
				:lower-threshold="50" >

		    <view  v-for="(item, index) in followList" 
          :key="index" class="item-list">
          <view  class="cell-title" @click="gohomepage(item)">
            <view style="display: flex;">
              <u-avatar class="avatar" :src="item.avatarUrl"></u-avatar>
              <text class="username"> {{ item.username }} </text>
            </view>
            <view>
              <u-button color="#059f9f" size="mini" @click="follow(item)" shape="circle" :hairline="true" v-if="item.is_gz==0" >+ 关注</u-button>
              <u-button color="#ddd" size="mini" shape="circle" :hairline="false" v-else >已关注</u-button>
            </view>
          </view>
          
            <view @click="seeAll(item)">
                <!-- 内容部分 -->
                <view 
                  class="content"
                  :class="{'content-collapsed': !item.expanded && item.showExpand}"
                  
                >
                  <text>{{ item.content }}</text>
				          <text v-if="item.showExpand">...</text>
                </view>
                <view 
                  class="expand-qw" 
                  v-if="item.showExpand"
                >
                  <text>全文</text> 
                </view>
            </view>
		</view>
	  </scroll-view>
    </view>
    <!-- <u-popup :show="infoShow" mode="right" @close="closesinfo">
        <view>
            <div class="pages-title">
                <u-icon name="arrow-left" size="30" @click="closesinfo"></u-icon>
            </div>
            <communityInfo ref="searchsinfo" ></communityInfo>
        </view>
    </u-popup> -->
    <!-- <u-popup :show="homepageShow" mode="right" @close="closeshomepage">
        <view>
            <div class="pages-title">
                <u-icon name="arrow-left" size="30" @click="closeshomepage"></u-icon>
            </div>
            <homePage ref="homepage" @seeAll="seeAll"></homePage>
        
        </view>
    </u-popup> -->
    <u-icon name="plus-circle-fill" class="publish" @click="publish" size="80" color="#059f9f"></u-icon>
  </view>
</template>

<script>
import {communityApi } from '@/utils/api'
// import communityInfo from './communityInfo.vue'
// import homePage from './homepage.vue'
export default {
  components: {  },
  data() {
    return {
      myavatar: '',//我的头像
      homepageShow:false,//主页显示
		refreshing: false,//下拉刷新
    infoShow:false,//显示详情选择
      currentTab: 0, // 默认选中推荐标签页
      tabList: [
        { name: '推荐' },
        { name: '关注' }
      ],
      recommendList: [],
      recommendpage: 1,
      recommendtotle: 0,
      followList: [],
      followpage: 1,
      followtotle: 0,
    };
  },
  onLoad() { 
	//推荐列表
    this.getData();
    this.getUserInfo();
  },
  methods: {
    //发布
    publish(){
      uni.navigateTo({
        url: '/pages/community/publish'
      })
    },
    async getUserInfo(){
       await communityApi.user().then(res => {
        uni.hideLoading();
			this.form = res.data
		})
    },
    //搜索数据
    search(){
      uni.navigateTo({
        url: '/pages/community/searchCommunity'
      });
    },
    // 跳转个人主页
    gohomepage(item){
      uni.navigateTo({
        url: '/pages/community/homepage?id=' + item.user_id
      })
    },
    //  跳转我的主页
    homefunc(){
      uni.navigateTo({
      url: '/pages/community/mypage'
    });
      // this.homepageShow=true;
      // console.log('dhajdhu')
      //  this.$nextTick(() => {
      //   this.$refs.homepage.getProfile();
      // })
      
    },
    closeshomepage(){
      this.homepageShow=false;
    },
    //关注
    async follow(item){
      await communityApi.mark({source_id:item.user_id,type:2}).then(res => {
            uni.hideLoading();
            console.log(res);
      })
    },

		//推荐列表
	async getData(){
		await communityApi.getCommunityList({page:this.recommendpage}).then(res => {
			uni.hideLoading();
			res.data.data.map(item => {
				item['expanded']=false;
				item['showExpand']= this.checkNeedExpand(item.content) // 是否需要显示展开按钮
				if(item.content.length > 75){
					item['content'] = item.content.substring(0, 67)
				}
				
			})
      this.recommendtotle=res.data.pageTotal;
			this.recommendList = [...this.recommendList,...res.data.data];
			console.log('和技术',this.recommendList)
			// this.$nextTick(() => {
			// 		this.$refs.uReadMore.init();
			// 	})
		})

	},
	// 检查是否需要显示展开按钮
    checkNeedExpand(content) {
      // 这里可以根据实际需求实现更精确的判断
      // 例如根据内容长度或行数

	  console.log('content',content.length);
      return content.length > 50; // 简单根据长度判断
    },
	 // 切换展开/收起状态
    toggleExpand(index) {
      this.recommendList[index].expanded = !this.recommendList[index].expanded;
      // 需要手动更新视图
      this.recommendList = [...this.recommendList];
    },
    handleTabChange(value) {
		console.log('shequ ');
      this.currentTab = value.index;
	  if(this.currentTab == 1){ 
		this.followListData()
	  }else{
		this.getData()
	  }
    },
	//下拉刷新
	onRefresh(){
		if(this.currentTab == 1){ 
      this.followpage=1;
			this.followListData()
		}else{
      this.recommendpage=1;
			this.getData()
		}
	},
	//关注列表
	async followListData(){
		 const user = uni.getStorageSync('userInfo');
		await communityApi.myFollowArticle({page:this.followpage}).then(res => {
			uni.hideLoading();
      res.data.data.map(item => {
				item['expanded']=false;
				item['showExpand']= this.checkNeedExpand(item.content) // 是否需要显示展开按钮
				if(item.content.length > 75){
					item['content'] = item.content.substring(0, 67)
				}
				
			})
      this.followtotle=res.data.pageTotal;
			this.followList = [...this.followList,...res.data.data];
			// this.followList = res.data.data
		})
	},
	//上拉加载
	onLoadMore(){
		if(this.currentTab == 1){ 
      if(this.followtotle>this.followpage){
        this.followpage++;
			  this.followListData()
      }
		}else{
      if(this.recommendtotle>this.recommendpage){
        this.recommendpage++;
			  this.getData()
      }
      
		}
	},
	//查看全部
	seeAll(item){
    uni.navigateTo({
      url: '/pages/community/communityInfo?id=' + item.id
    });
		// this.infoShow=true;
    // this.$nextTick(() => {
    //   this.$refs.searchsinfo.infoData(item);
    // })
	},
  closesinfo(){
    this.infoShow=false;
  },
	
  }
};
</script>
<style lang="scss" scoped>
.publish{
  position: fixed;
  bottom: 135rpx;
  right: 20rpx;
}
::v-deep .u-tabs__wrapper__nav__line{
	display: none;
}
::v-deep .u-tabs__wrapper__nav{
	justify-content: center;
}
.content {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
    word-break: break-word;
    
    &-collapsed {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3; /* 显示的行数 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .expand-qw{
	color: #14b19e;
    font-size: 26rpx;
	position: absolute;
	right: 94rpx;
	bottom: 26rpx;
  }
.expand-btn {
    display: flex;
    align-items: center;
	  justify-content: end;
    margin-top: 10rpx;
    
    
    text {
      margin-right: 8rpx;
    }
  }
  
  .action-item {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 26rpx;
    
    text {
      margin-left: 8rpx;
    }
  }

.home-container {
  height: calc(100vh - 150rpx);
  padding: 20rpx;
  .home-header{
	display: flex;
	// width: 100%;
	justify-content: space-between;
	align-items: center;
  padding:20rpx 0 0;
	// position: absolute;
  }
  .list-container{
    height: calc(100vh - 220rpx);
  }
//   .recommend-list{
// 	height: calc(100vh - 220rpx);
// 	overflow-y: auto;
//   }
	.item-list{
		border: 1px solid #eee;
		margin-bottom: 20rpx;
		padding: 20rpx;
		border-radius: 20rpx;
		position: relative;
	}

  .cell-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 20rpx;
    margin-bottom: 20rpx;
	.avatar{
		margin-right: 16rpx;
	}
    .username {
      font-size: 32rpx;
      font-weight: bold;
      margin-right: 10rpx;
    }

    .company {
      font-size: 28rpx;
      color: #666;
    }
  }

  .cell-content {
    font-size: 28rpx;
    color: #333;
    margin-top: 10rpx;
  }

  .cell-footer {
    display: flex;
    align-items: center;
    margin-top: 10rpx;

    u-button {
      margin-right: 10rpx;
    }

    u-icon {
      margin-right: 10rpx;
    }
  }
  .pages-title{
    height: 50rpx;
    display: flex;
    padding-left: 8px;
    align-items: center;
  }
}
</style>