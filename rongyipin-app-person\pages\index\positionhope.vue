<template>
	<view class="job-select-page">
		 <!-- <u-navbar title="" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar> -->
		<!-- 顶部搜索栏 -->
		<view class="search-header">
			<u-search 
				v-model="searchKeyword" 
				placeholder="搜索职位" 
				:show-action="false"
				bg-color="#f8f9fa"
				@search="handleSearch"
				@custom="handleSearch"
			></u-search>
			<view class="search-list">
				<div v-for="item in searchList" :key="item" @click="selectSaerchJob(item)">
					<div class="job-name">{{ item.name }}</div>
					<div class="job-fu">{{ item.job1_name }} - {{ item.job2_name }} - {{ item.job3_name }}</div>
				</div>
			</view>
		</view>

		<!-- 主体内容区域 -->
		<view v-if="jobCategories.length>0">
			<view class="main-content" >
				<!-- 左侧一级分类导航 -->
				<view class="left-sidebar">
					<scroll-view 
						scroll-y 
						class="sidebar-scroll"
						:scroll-top="leftScrollTop"
					>
						<view 
							v-for="(category, index) in jobCategories" 
							:key="index"
							class="sidebar-item"
							:class="{ 'active': currentCategoryIndex === index }"
							@click="selectCategory(index)"
						>
							<text class="sidebar-text">{{ category.name }}</text>
							<!-- <view v-if="currentCategoryIndex === index" class="active-indicator"></view> -->
						</view>
					</scroll-view>
				</view>

				<!-- 右侧职位内容区域 -->
				<view class="right-content">
					<scroll-view 
						scroll-y 
						class="content-scroll"
						:scroll-top="rightScrollTop"
						@scroll="onRightScroll"
						:scroll-with-animation="scrollWithAnimation"
						:scroll-into-view="scrollIntoView"
					>
						<view 
							v-for="(category, categoryIndex) in jobCategories" 
							:key="categoryIndex"
							:id="'category-' + categoryIndex"
							class="category-section"
						>
							<!-- 一级分类标题 -->
							<!-- <view class="category-title">
								<text class="category-title-text">{{ category.name }}</text>
							</view> -->

							<!-- 二级分类列表 -->
							<view 
								v-for="(subCategory, subIndex) in category.children" 
								:key="subIndex"
								class="sub-category-section"
							>
								<!-- 二级分类标题 -->
								<view class="sub-category-title">
									<text class="sub-category-title-text">{{ subCategory.name }}</text>
								</view>

								<!-- 三级职位列表 -->
								<view class="jobs-grid">
									<!-- 多选 -->
									<!-- <view 
										v-for="(job, jobIndex) in subCategory.children" 
										:key="jobIndex"
										class="job-item"
										:class="{ 'selected': isJobSelected(job) }"
										@click="selectJob(job)"
									>
										<text class="job-text">{{ job.name }}</text>
									</view> -->
									<!-- 单选 -->
									<view 
										v-for="(job, jobIndex) in subCategory.children" 
										:key="jobIndex"
										class="job-item"
										:class="{ 'selected': selectedJobs==job }"
										@click="selectJob(job)"
									>
										<text class="job-text">{{ job.name }}</text>
									</view>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>
		
		<!-- 已选择的职位 -->
		<!-- <view v-if="selectedJobs.length > 0" class="selected-jobs">
			<view class="selected-header">
				<text class="selected-title">已选择 ({{ selectedJobs.length }})</text>
				<text class="clear-btn" @click="clearAllJobs">清空</text>
			</view>
			<scroll-view scroll-x class="selected-scroll">
				<view class="selected-jobs-list">
					<view 
						v-for="(job, index) in selectedJobs" 
						:key="index"
						class="selected-job-item"
					>
						<text class="selected-job-name">{{ job.name }}</text>
						<u-icon 
							name="close" 
							size="16" 
							color="#909399"
							@click="removeJob(job)"
						></u-icon>
					</view>
				</view>
			</scroll-view>
		</view> -->

			<!-- 底部确认按钮 -->
			<view class="bottom-actions">
				<u-button 
				class="confirm-button"
					type="primary" 
					:disabled="selectedJobs.length === 0"
					@click="confirmSelection"
					custom-style="width: 100%; height: 88rpx; font-size: 32rpx;"
				>
					确定
				</u-button>
			</view>
		</view>
		<view class="loading" v-else>
            <u-loading-icon></u-loading-icon>
            加载中
        </view>
	</view>
</template>

<script>
import { jobApi } from "@/utils/api"
export default {
	data() {
		return {
			searchKeyword: '',
			selectedJobs: '',
			currentCategoryIndex: 0, // 当前选中的一级分类索引
			leftScrollTop: 0, // 左侧滚动位置
			rightScrollTop: 0, // 右侧滚动位置
			scrollWithAnimation: false, // 是否使用动画滚动
			scrollIntoView: '', // 滚动到指定元素
			categoryOffsets: [], // 各分类在右侧的偏移量
			isScrolling: false, // 是否正在滚动中
			scrollTimer: null, // 滚动定时器
			
			// 职位分类数据 - 三级结构
			jobCategories: [],
			searchList:[]
		}
	},

    async mounted() {
       
	},
	methods: {
		async getJobCategories() {
			 uni.showLoading({
				title: '加载中...',
				mask: true
			});
			let res = await jobApi.getJobList();
			uni.hideLoading();
			if (res.code != 200) {
				uni.showToast({
					title: res.msg,
					icon: 'none'
				})
				return
			}
			this.jobCategories = res.data
			// 页面加载完成后计算各分类的偏移量
			this.$nextTick(() => {
				this.calculateCategoryOffsets();
			});
		},
		// 搜索职位
		async handleSearch() {
			if (!this.searchKeyword.trim()) return;

			console.log('搜索职位:', this.searchKeyword);
			await jobApi.getSearchJobNameList({name:this.searchKeyword}).then((res)=>{
				uni.hideLoading();
				if(res.code == 200){
					console.log('搜索职位:', res.data);
					this.searchList = res.data
				}else{
					uni.showToast({
						title: res.msg,
						icon: 'none',
					});
				}
				
			})
		},

		// 选择左侧分类
		selectCategory(index) {
			if (this.currentCategoryIndex === index) return;

			this.currentCategoryIndex = index;
			this.scrollToCategory(index);
		},

		// 滚动到指定分类
		scrollToCategory(index) {
			this.scrollWithAnimation = true;
			this.scrollIntoView = `category-${index}`;

			// 延迟关闭动画
			setTimeout(() => {
				this.scrollWithAnimation = false;
				this.scrollIntoView = '';
			}, 300);
		},

		// 右侧滚动事件
		onRightScroll(e) {
			if (this.isScrolling) return;

			const scrollTop = e.detail.scrollTop;

			// 使用节流，避免频繁计算
			clearTimeout(this.scrollTimer);
			this.scrollTimer = setTimeout(() => {
				this.updateCurrentCategory(scrollTop);
			}, 100);
		},

		// 更新当前分类索引
		updateCurrentCategory(scrollTop) {
			// 获取所有分类元素的位置
			const query = uni.createSelectorQuery().in(this);

			this.jobCategories.forEach((category, index) => {
				query.select(`#category-${index}`).boundingClientRect();
			});

			query.exec((rects) => {
				if (!rects || rects.length === 0) return;

				let newIndex = 0;

				for (let i = 0; i < rects.length; i++) {
					if (rects[i] && rects[i].top <= 150) {
						newIndex = i;
					}
				}

				if (newIndex !== this.currentCategoryIndex) {
					this.currentCategoryIndex = newIndex;
				}
			});
		},

		// 计算分类偏移量
		calculateCategoryOffsets() {
			const query = uni.createSelectorQuery().in(this);

			this.jobCategories.forEach((category, index) => {
				query.select(`#category-${index}`).boundingClientRect();
			});

			query.exec((rects) => {
				this.categoryOffsets = rects.map(rect => rect ? rect.top : 0);
			});
		},

		// 选择职位
		selectJob(job) {
            //多选的时候
			// const isSelected = this.isJobSelected(job);

			// if (isSelected) {
			// 	this.removeJob(job);
			// } else {
			// 	this.selectedJobs.push(job);
			// }

            //单选
            const isSelected = this.isJobSelected(job);
            if (isSelected) {
				this.selectedJobs='';
			} else {
				 this.selectedJobs=job;
			}
           
		},

		// 检查职位是否已选择
		isJobSelected(job) {
           return this.selectedJobs&&this.selectedJobs.id==job.id
			// return this.selectedJobs.some(selected => selected.id === job.id);//多选的时候
		},

		// 移除已选择的职位
		removeJob(job) {
			const index = this.selectedJobs.findIndex(selected => selected.id === job.id);
			if (index > -1) {
				this.selectedJobs.splice(index, 1);
			}
		},

		// 清空所有已选择的职位
		clearAllJobs() {
			this.selectedJobs = [];
		},
		//选中搜索职位
		selectSaerchJob(item){
			console.log("选中搜索职位",item);
			this.selectedJobs={id:item.job3,name:item.name};
			this.$emit('positiondatafunc', this.selectedJobs);
			// let data={...item};
			// data['id']=item.job3;
			// uni.setStorageSync('hopejob',data)
			// uni.navigateTo({
			// 	url:'./addinfo'
			// })
		},

		// 确认选择
		confirmSelection() {
			if (this.selectedJobs.length === 0) {
				uni.showToast({
					title: '请选择至少一个职位',
					icon: 'none'
				});
				return;
			}
			this.$emit('positiondatafunc', this.selectedJobs);
			console.log('已选择的职位:', this.selectedJobs);
			// uni.setStorageSync('hopejob',this.selectedJobs)
			// uni.navigateTo({
			// 	url:'./addinfo'
			// })

			// uni.showModal({
			// 	title: '确认选择',
			// 	content: `您选择了 ${this.selectedJobs.length} 个职位`,
			// 	success: (res) => {
			// 		if (res.confirm) {
			// 			// 可以通过事件或者页面参数传递选择的职位数据
			// 			uni.navigateBack();
			// 		}
			// 	}
			// });
		}
	}
    }
</script>

<style lang="scss" scoped>
.job-select-page {
	background-color: #f8f9fa;
	height: calc(100vh - 100rpx);
	display: flex;
	flex-direction: column;
}
.loading{
	text-align: center;

}

/* 搜索头部 */
.search-header {
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #ebeef5;
	flex-shrink: 0;
}
.search-list{
	position: absolute;
    top: 160rpx;
    left: 0;
    right: 0;
    max-height: calc(100vh - 100rpx);
	overflow-y: auto;
    background-color: #fff;
	z-index: 2;
	.job-name{
		font-size: 28rpx;
		color: #303133;
		padding: 20rpx 30rpx;
		
	}
	.job-fu{
		font-size: 24rpx;
		color: #909399;
		padding: 0 30rpx 20rpx;
		border-bottom: 1rpx solid #f0f0f0;		
	}
}


/* 主体内容区域 */
.main-content {
	height: calc(100vh - 340rpx);
	flex: 1;
	display: flex;
	overflow: hidden;
}

/* 左侧分类导航 */
.left-sidebar {
	width: 200rpx;
	background-color: #f5f6fa;
	border-right: 1rpx solid #ebeef5;
	flex-shrink: 0;
}

.sidebar-scroll {
	height: 100%;
}

.sidebar-item {
	position: relative;
	padding: 30rpx 20rpx;
	text-align: center;
	border-bottom: 1rpx solid #ebeef5;
	transition: all 0.3s ease;

	&.active {
		background-color: #fff;

		.sidebar-text {
			color: #02bdc4;
			font-weight: 600;
		}
	}

	&:active {
		background-color: #e3f2fd;
	}
}

.sidebar-text {
	font-size: 26rpx;
	color: #606266;
	line-height: 1.4;
}

.active-indicator {
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 6rpx;
	height: 40rpx;
	background-color: #02bdc4;
	border-radius: 3rpx 0 0 3rpx;
}

/* 右侧内容区域 */
.right-content {
	flex: 1;
	background-color: #fff;
}

.content-scroll {
	height: 100%;
}

/* 分类区块 */
.category-section {
	padding: 0 30rpx;
}

.category-title {
	position: sticky;
	top: 0;
	background-color: #fff;
	padding: 30rpx 0 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
	z-index: 10;
}

.category-title-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #303133;
}

/* 二级分类 */
.sub-category-section {
	margin-bottom: 40rpx;
}

.sub-category-title {
	padding: 30rpx 0 20rpx;
}

.sub-category-title-text {
	font-size: 28rpx;
	font-weight: 500;
	color: #606266;
}

/* 职位网格 */
.jobs-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
	margin-bottom: 30rpx;
}

.job-item {
	padding: 20rpx 30rpx 24rpx;
	background-color: #f8f9fa;
	border-radius: 40rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;

	&.selected {
		background-color: #e3f2fd;
		border-color: #02bdc4;

		.job-text {
			color: #02bdc4;
		}
	}

	&:active {
		transform: scale(0.95);
	}
}

.job-text {
	font-size: 22rpx;
	color: #606266;
}

/* 已选择职位区域 */
.selected-jobs {
	background-color: #fff;
	border-top: 1rpx solid #ebeef5;
	flex-shrink: 0;
	max-height: 200rpx;
}

.selected-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx 10rpx;
}

.selected-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #303133;
}

.clear-btn {
	font-size: 26rpx;
	color: #02bdc4;
}

.selected-scroll {
	height: 120rpx;
}

.selected-jobs-list {
	display: flex;
	padding: 0 30rpx 20rpx;
	gap: 20rpx;
	white-space: nowrap;
}

.selected-job-item {
	display: flex;
	align-items: center;
	padding: 12rpx 20rpx;
	background-color: #e3f2fd;
	border-radius: 30rpx;
	gap: 10rpx;
	flex-shrink: 0;
}

.selected-job-name {
	font-size: 26rpx;
	color: #02bdc4;
	white-space: nowrap;
}

/* 底部操作区域 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	width: 90%;
	text-align: center;
	height: 100rpx;
	background-color: #fff;
	padding: 20rpx 5%;
	// padding: 30rpx 0;
	border-top: 1rpx solid #ebeef5;
	flex-shrink: 0;
	// .confirm-button{
	// 	width: 50%;
	// 	margin: 0 20rpx;
	// }
}
</style>

