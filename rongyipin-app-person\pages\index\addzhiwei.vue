<template>
	<view class="position-container">
		<!-- 顶部导航 -->
		<!-- <u-popup  :show="positionflag" mode="right"> -->
           <u-navbar @leftClick="handleBack" title="" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
            <!-- <div class="home-container" style='width:100%'>
                wetwqyuet
            </div> -->
            <view class="zw-container">
                <div class="zw-text"> 想 找 什 么 工 作 ？</div>
                <div>{{zwlist.length}}/5</div>
            </view>
            <view class="zw-tip">添加多个求职期望，可获得更多精准高薪工作机会</view>
            <view class="zw-list">
               <view v-for="item in zwlist" :key="item.id" class="zw-item" @click="zwClick(item)">
                    <div class="zw-item-content">
                        <div><span class="yuan"></span> {{item.job_classname}}</div>
                        <u-icon name="arrow-right"></u-icon>
                    </div>
                    <div class="zw-item-desc">{{item.minsalary}}-{{item.maxsalary}}｜{{item.type_text}}｜{{item.city_classname}}</div>
                    <div class="zw-item-line"></div>
               </view>

               <view class="zw-add" @click="addzhiwei">
                    <u-icon name="plus-circle" color="#02bdc4"></u-icon>
                    <text class="add-text">添 加 求 职 期 望</text>
               </view>

                
                <div class="zw-item-content" @click="editWordText">
                    <div><span class="yuan"></span> 求职状态</div>
                    <div class="right-content"> {{qzState}} <u-icon name="arrow-right"></u-icon></div> 
                </div>
                
                <div class="zw-item-line"></div>
                
                <div class="zw-item-content">
                    <div><span class="yuan"></span> 电话助手</div>
                    <u-switch v-model="value" @change="change"></u-switch>
                </div>
                <div class="zw-item-desc">接受电话的手机号为：{{phone}}</div>
               
            </view>
        <!-- </u-popup> -->
        
            <view>
                <u-picker :show="showWordText" :columns="columns" keyName="name" @confirm="confirmPicker"></u-picker>
            </view>
        

	</view>
</template>

<script>
// import { onLoad } from 'uview-ui/libs/mixin/mixin'
import { userinfoApi, dictApi } from '@/utils/api'
// import registration from "./registration/index.vue"
export default {
   
	data() {
		return {
            showWordText: false,//是否编辑求职状态
            columns:[],
            value:false,
			// positionflag:false,
            zwlist:[{
                name:'修图师'
            }],
            positionHopeFlag:false,
            zhiwei:'全职',
            moneyShow:false,//薪资选择
           
            wrokCity:'',
            wrokPosition:'',
            moneyhope:'',
            qzState:'',
            phone:''

		}
	},
	components: {
		
	},
    onLoad(options){
        console.log(options, '这是options.city');
        // this.wrokCity=options.city;
       this.wrokCity= options.city;
        this.wrokPosition=options.position;
        this.moneyhope=options.money;
        this.filterData();
    },
    onShow(){
        this.userInfo = uni.getStorageSync('userInfo');
        this.UserResumeExpect();
    },
	
	methods: {
        //编辑求职状态
		editWordText(){
			this.showWordText = true;
		},
         //请求字典数据
		async filterData(){
			console.log("开始请求数据")
			
			await dictApi.getAllDictionary().then(res=>{
				uni.hideLoading();
				if(res.code==200){
					console.log('上市时间1',this.columns)
					this.columns=[[...res.data.user_jobstatus.data]];
					console.log('上市时间2',this.columns)
				}else{
					uni.showToast({
						title:res.msg,
						icon:'none'
					})
				}
			
				
				console.log(this.categories)
			})
		},
        //编辑求职状态提交
		async confirmPicker(val){
			console.log(val.indexs[0])
			

			const res = await userApi.userSave({ work_id:val.indexs[0] })
            uni.hideLoading();
            if (res.code == 200) {
				this.resumeData.word_text=val.value[0];
				this.showWordText = false
                let userInfo = uni.getStorageSync('userInfo') || {};
                userInfo.advantage = this.postContent.trim();
                uni.setStorageSync('userInfo', userInfo);
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                });

                // 延迟返回上一页
                setTimeout(() => {
                    uni.navigateBack();
                }, 1500);
            } else {
                uni.showToast({
                    title: res.msg || '保存失败，请重试',
                    icon: 'none'
                });
            }



		},
        //用户求职意向
        UserResumeExpect(){
            userinfoApi.getUserResumeExpect().then(res => {
                console.log(res, 'qqqqq');
                // res.data.map(item => {
                //     item['name']=item.job_classname;
                //      item['id']=item.job_classid;
                // })
                this.zwlist=res.data.data;
                this.qzState=res.data.userdata.workrtpe;
                this.phone=res.data.userdata.mobile;
                // this.job_active=res.data[0];
                // this.currentCity = res.result.ad_info
                // this.currentCityName=res.result.ad_info.city.slice(0,-1)
                uni.hideLoading();
               
                //   this.surrounding(res.result.location.lat, res.result.location.lng)
            })
        },
        //编辑职位
        zwClick(item){ 
            // const itemData = JSON.stringify(item)
            if(this.zwlist.length > 1){
                uni.navigateTo({
                    url:'/pages/index/addinfo?id='+item.id+'&del=true'
                })
            }else{
                uni.navigateTo({
                    url:'/pages/index/addinfo?id='+item.id+'&del=false'
                })
            }
            
        },
        change(checked) {
            console.log('开关状态:', checked);
            this.value = checked; // 可以同步更新状态（如果需要）
        },
       
        openpop(){
            this.positionflag=true;
        },
        // closecity(){
        //     this.positionflag=false;
        // },
        //添加职位
        addzhiwei(){
            uni.navigateTo({
				url:"./addinfo"
			})
        },
        //返回职位
        handleBack(){
            console.log("返回");
            //  uni.navigateBack();
            uni.switchTab({
               url:"/pages/index/index"
            })
        },
        
	}
}
</script>

<style lang="scss" scoped>
.position-container{
    
}
::v-deep .u-slide-right-enter-active{
    width: 100%;

}
::v-deep .u-popup__content{
     background-color: #e0fffd;
}

.home-container{
    width: 100%;
    height: 100vh;
}
.back-icon{
    margin: 10px 10px;
}
.zw-container{
    padding:10px 20px;
    display: flex;
    justify-content: space-between;
    font-weight: 700;
    
    
}
.claas-pad{
    height: 55px;
    line-height: 55px;

}
.zw-tip{
        font-size: 12px;
        margin:2px 20px;
}
.zw-list{
    margin: 20px;
    background-color: #f5f5f5;
    border-radius: 5px;
    padding: 10px;
}
.zw-item{
    font-weight: 700;
    font-size: 15px;

}
 .zw-item-content{
    font-weight: 700;
    font-size: 15px;
        display: flex;
        justify-content: space-between;
    }
    .yuan{
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #02bdc4;
        margin-right: 5px;
    }
    .zw-item-desc{
        padding:10px 0;
        color: #a1a1a1;
        font-size: 12px;
    }
    .zw-item-line{
        width: 100%;
        height: 1px;
        background-color: #ddd;
        margin: 10px 0;
    }
    .right-content{
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #a1a1a1;
    }
.zw-add{
        width: 130px;
        margin: 0 auto;
        background-color: #ffffff;
        border-radius: 20px;
        padding: 13px 20px;
        display: flex;
        font-size:13px;
        color: #02bdc4;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        font-weight: 700;
        .add-text{
            margin-left: 10px;
        }
    }
    .add-position-hope{
        ::v-deep .u-fade-enter-active{
            z-index: 10075!important;
        }
        .save-btn{
            width: 100px;
            text-align: center;
            margin: 0 auto;
        }
    }
    
</style>  