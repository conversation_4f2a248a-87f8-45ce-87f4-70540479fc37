<template>
    <view class="container">
        <u-navbar height="44px" :autoBack="true" leftIconSize="30" leftIconColor="#333" :safeAreaInsetTop="true" placeholder :fixed="true">
            <view class="u-nav-slot" slot="center">
                <text class="u-nav-title">上传简历</text>
            </view>
        </u-navbar>

        <view class="content">
            <view class="upload-area">
                <button @click="selectPDF" class="upload-btn">从文件管理器选择PDF</button>
                <button @click="selectFromGallery" class="gallery-btn">从相册选择文件</button>
                <button @click="testUpload" class="test-btn">测试上传功能</button>
            </view>

            <view class="tips">
                <text class="tip-text">使用说明：</text>
                <text class="tip-content">• "从文件管理器选择PDF" - 打开系统文件管理器选择PDF文件</text>
                <text class="tip-content">• "从相册选择文件" - 从相册或其他应用选择文件</text>
                <text class="tip-content">• "测试上传功能" - 创建测试文件验证上传功能</text>
                <text class="tip-content">• 只支持PDF格式文件上传</text>
            </view>
        </view>
    </view>
</template>

<script>
import { filelate } from '@/utils/api.js'

export default {
    data() {
        return {
            
        }
    },
    methods: {
        // 选择PDF文件
        selectPDF() {
            console.log('🚀 开始选择PDF文件')

            // #ifdef APP-PLUS
            // 使用原生Intent打开文件管理器
            this.openFileManager()
            // #endif

            // #ifdef H5
            // H5端使用input file
            this.createFileInput()
            // #endif
        },

        // 打开文件管理器
        openFileManager() {
            // #ifdef APP-PLUS
            try {
                const main = plus.android.runtimeMainActivity()
                const Intent = plus.android.importClass('android.content.Intent')
                const intent = new Intent(Intent.ACTION_GET_CONTENT)

                intent.addCategory(Intent.CATEGORY_OPENABLE)
                intent.setType('*/*') // 允许所有文件类型，让用户选择

                const REQUEST_CODE = 1001

                main.onActivityResult = (requestCode, resultCode, data) => {
                    console.log('📄 文件选择结果:', { requestCode, resultCode })

                    if (requestCode === REQUEST_CODE && resultCode === -1 && data) {
                        const uri = data.getData()
                        console.log('📎 选择的文件URI:', uri.toString())

                        // 转换URI为本地路径
                        this.convertUriToPath(uri)
                    }
                }

                main.startActivityForResult(intent, REQUEST_CODE)

            } catch (error) {
                console.error('❌ 打开文件管理器失败:', error)
                uni.showToast({
                    title: '打开文件管理器失败',
                    icon: 'none'
                })
            }
            // #endif
        },

        // 处理选择的URI - 使用uni.request上传
        convertUriToPath(uri) {
            // #ifdef APP-PLUS
            console.log('🔄 处理选择的URI:', uri.toString())
            uni.showLoading({ title: '处理文件中...' })

            try {
                // 使用uni.uploadFile直接上传URI
                this.uploadFileByURI(uri)

            } catch (error) {
                console.error('❌ 处理URI失败:', error)
                uni.hideLoading()
                uni.showToast({
                    title: '处理文件失败',
                    icon: 'none'
                })
            }
            // #endif
        },

        // 直接通过URI上传文件
        uploadFileByURI(uri) {
            // #ifdef APP-PLUS
            console.log('🚀 直接通过URI上传文件')

            const uriString = uri.toString()

            uni.uploadFile({
                url: 'http://8.130.152.121:82/jobapi/common/upload',
                filePath: uriString,
                name: 'file',
                success: (res) => {
                    console.log('✅ URI上传成功:', res)
                    uni.hideLoading()

                    try {
                        const data = JSON.parse(res.data)
                        if (data.code === 200) {
                            // 调用保存简历接口
                            this.saveResumeInfo({
                                attachment_name: 'selected_file.pdf',
                                storage_path: data.data.url
                            })
                        } else {
                            throw new Error(data.message || '上传失败')
                        }
                    } catch (e) {
                        console.error('❌ 解析上传结果失败:', e)
                        uni.showToast({
                            title: '上传结果解析失败',
                            icon: 'none'
                        })
                    }
                },
                fail: (error) => {
                    console.error('❌ URI上传失败:', error)
                    uni.hideLoading()

                    // 如果直接上传失败，尝试其他方法
                    this.showUploadOptions()
                }
            })
            // #endif
        },

        // 保存简历信息
        async saveResumeInfo(params) {
            try {
                console.log('💾 保存简历参数:', params)

                const saveResult = await filelate.saveResumeAttachment(params)
                console.log('✅ 保存简历结果:', saveResult)

                if (saveResult.code == 200 || saveResult.success) {
                    uni.showToast({
                        title: '上传成功',
                        icon: 'success'
                    })

                    // 延迟返回上一页
                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1500)
                } else {
                    throw new Error(saveResult.message || '保存简历信息失败')
                }
            } catch (error) {
                console.error('❌ 保存简历失败:', error)
                uni.showToast({
                    title: error.message || '保存失败',
                    icon: 'none'
                })
            }
        },

        // 显示上传选项
        showUploadOptions() {
            uni.showModal({
                title: '上传方式',
                content: '直接上传失败，请选择其他方式：\n1. 点击"测试上传功能"验证上传接口\n2. 尝试从其他位置选择文件',
                showCancel: true,
                confirmText: '测试上传',
                cancelText: '重新选择',
                success: (res) => {
                    if (res.confirm) {
                        this.testUpload()
                    } else {
                        // 用户可以重新选择文件
                        console.log('用户选择重新选择文件')
                    }
                }
            })
        },

        // 处理选择的文件路径
        processSelectedPath(filePath) {
            console.log('🔄 处理文件路径:', filePath)

            // 检查是否为PDF文件
            if (!filePath.toLowerCase().endsWith('.pdf')) {
                uni.showToast({
                    title: '请选择PDF文件',
                    icon: 'none'
                })
                return
            }

            uni.showLoading({ title: '读取文件中...' })

            // 使用plus.io读取文件
            plus.io.resolveLocalFileSystemURL(filePath, (entry) => {
                console.log('✅ 文件路径解析成功:', entry.name)

                entry.file((file) => {
                    console.log('📄 获取文件对象成功:', {
                        name: file.name,
                        size: file.size
                    })

                    const reader = new plus.io.FileReader()

                    reader.onloadend = (e) => {
                        console.log('✅ 文件读取完成')
                        uni.hideLoading()

                        // 创建文件对象
                        const fileObject = {
                            name: file.name,
                            size: file.size,
                            type: 'application/pdf',
                            data: e.target.result // ArrayBuffer
                        }

                        console.log('� 文件对象创建完成:', { 
                            name: fileObject.name,
                            size: fileObject.size,
                            dataSize: fileObject.data.byteLength
                        })

                        // 调用上传方法
                        this.handleFileUpload(fileObject)
                    }

                    reader.onerror = (error) => {
                        console.error('❌ 文件读取失败:', error)
                        uni.hideLoading()
                        uni.showToast({
                            title: '文件读取失败',
                            icon: 'none'
                        })
                    }

                    // 读取为ArrayBuffer
                    reader.readAsArrayBuffer(file)

                }, (error) => {
                    console.error('❌ 获取文件对象失败:', error)
                    uni.hideLoading()
                    uni.showToast({
                        title: '获取文件失败',
                        icon: 'none'
                    })
                })

            }, (error) => {
                console.error('❌ 文件路径解析失败:', error)
                uni.hideLoading()

                // 如果plus.io解析失败，尝试直接上传
                console.log('🔄 尝试直接上传文件路径')
                this.directUploadPath(filePath)
            })
        },

        // 直接上传文件路径
        directUploadPath(filePath) {
            console.log('🚀 直接上传文件路径:', filePath)

            uni.uploadFile({
                url: 'http://8.130.152.121:82/jobapi/common/upload',
                filePath: filePath,
                name: 'file',
                success: (res) => {
                    console.log('✅ 直接上传成功:', res)

                    try {
                        const data = JSON.parse(res.data)
                        if (data.code === 200) {
                            // 从文件路径提取文件名
                            const fileName = filePath.split('/').pop() || 'selected_file.pdf'

                            // 调用保存简历接口
                            this.saveResumeInfo({
                                attachment_name: fileName,
                                storage_path: data.data.url
                            })
                        } else {
                            throw new Error(data.message || '上传失败')
                        }
                    } catch (e) {
                        console.error('❌ 解析上传结果失败:', e)
                        uni.showToast({
                            title: '上传结果解析失败',
                            icon: 'none'
                        })
                    }
                },
                fail: (error) => {
                    console.error('❌ 直接上传失败:', error)
                    uni.showToast({
                        title: '上传失败，请重试',
                        icon: 'none'
                    })
                }
            })
        },

        // APP端打开文件选择器 - 备用方案
        openFileSelector() {
            // #ifdef APP-PLUS
            uni.showToast({
                title: '请从相册或文件管理器选择PDF文件',
                icon: 'none',
                duration: 3000
            })

            // 简化版本：直接提示用户使用其他方式
            console.log('📱 使用备用文件选择方案')
            // #endif
        },

        // H5端创建文件输入
        createFileInput() {
            // #ifdef H5
            const input = document.createElement('input')
            input.type = 'file'
            input.accept = '.pdf,application/pdf'
            input.style.display = 'none'

            input.onchange = (e) => {
                const file = e.target.files[0]
                if (file) {
                    console.log('H5端选择的文件:', file)
                    this.handleFileUpload({
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        file: file // 原始File对象
                    })
                }
            }

            document.body.appendChild(input)
            input.click()
            document.body.removeChild(input)
            // #endif
        },

        // 从相册选择文件
        selectFromGallery() {
            console.log('🖼️ 从相册选择文件')

            // #ifdef APP-PLUS
            // 使用ACTION_PICK打开相册/文件选择器
            try {
                const main = plus.android.runtimeMainActivity()
                const Intent = plus.android.importClass('android.content.Intent')
                const intent = new Intent(Intent.ACTION_PICK)

                // 设置为选择所有类型的文件
                intent.setType('*/*')

                const REQUEST_CODE = 1002

                main.onActivityResult = (requestCode, resultCode, data) => {
                    console.log('📄 相册选择结果:', { requestCode, resultCode })

                    if (requestCode === REQUEST_CODE && resultCode === -1 && data) {
                        const uri = data.getData()
                        console.log('📎 选择的文件URI:', uri.toString())

                        // 转换URI为本地路径
                        this.convertUriToPath(uri)
                    }
                }

                main.startActivityForResult(intent, REQUEST_CODE)

            } catch (error) {
                console.error('❌ 打开相册失败:', error)
                uni.showToast({
                    title: '打开相册失败',
                    icon: 'none'
                })
            }
            // #endif

            // #ifdef H5
            this.createFileInput()
            // #endif
        },

        // 处理文件上传 - 支持二进制数据
        async handleFileUpload(file) {
            console.log('🚀 开始上传文件:', file)
            console.log('📄 文件类型:', typeof file)
            console.log('📊 文件大小:', file.size || 'unknown')
            
            uni.showLoading({
                title: '上传中...'
            })
            
            try {
                let uploadResult
                
                // 判断是否为包含二进制数据的文件对象
                if (file.data && file.name) {
                    console.log('📱 APP端二进制文件上传')
                    uploadResult = await this.uploadBinaryFileToServer(file)
                } else if (file.file) {
                    console.log('🌐 H5端文件上传')
                    uploadResult = await this.uploadH5FileToServer(file)
                } else {
                    console.log('🌐 标准文件上传')
                    uploadResult = await this.uploadFileToServer(file)
                }
                
                if (uploadResult.success) {
                    const params = {
                        attachment_name: file.name,
                        storage_path: uploadResult.data.url
                    }
                    
                    console.log('💾 保存简历参数:', params)
                    
                    // 第二步：调用保存简历接口
                    const saveResult = await filelate.saveResumeAttachment(params)
                    console.log('✅ 保存简历结果:', saveResult)

                    if (saveResult.code == 200 || saveResult.success) {
                        uni.hideLoading()
                        uni.showToast({
                            title: '上传成功',
                            icon: 'success'
                        })
                    } else {
                        throw new Error(saveResult.message || '保存简历信息失败')
                    }
                } else {
                    throw new Error(uploadResult.message || '文件上传失败')
                }
            } catch (error) {
                console.error('❌ 上传失败:', error)
                uni.hideLoading()
                uni.showToast({
                    title: error.message || '上传失败',
                    icon: 'none'
                })
            }
        },

        // 上传二进制文件到服务器 - APP端专用
        uploadBinaryFileToServer(fileObject) {
            return new Promise((resolve, reject) => {
                console.log('🚀 开始二进制文件上传')
                
                try {
                    // 将ArrayBuffer转换为Blob
                    const blob = new Blob([fileObject.data], { type: 'application/pdf' })
                    
                    // 创建FormData
                    const formData = new FormData()
                    formData.append('file', blob, fileObject.name)
                    
                    // 使用XMLHttpRequest上传
                    const xhr = new XMLHttpRequest()
                    
                    xhr.onload = function() {
                        console.log('📤 上传响应状态:', xhr.status)
                        console.log('📤 上传响应内容:', xhr.responseText)
                        
                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText)
                                if (response.code === 200 || response.success) {
                                    resolve({
                                        success: true,
                                        data: response.data || response
                                    })
                                } else {
                                    reject(new Error(response.message || '上传失败'))
                                }
                            } catch (e) {
                                reject(new Error('响应解析失败'))
                            }
                        } else {
                            reject(new Error(`上传失败，状态码: ${xhr.status}`))
                        }
                    }
                    
                    xhr.onerror = function() {
                        console.error('❌ 上传请求失败')
                        reject(new Error('网络请求失败'))
                    }
                    
                    xhr.ontimeout = function() {
                        console.error('❌ 上传超时')
                        reject(new Error('上传超时'))
                    }
                    
                    // 设置超时时间
                    xhr.timeout = 60000 // 60秒
                    
                    // 发送请求
                    xhr.open('POST', 'http://8.130.152.121:82/jobapi/common/upload')
                    xhr.send(formData)
                    
                } catch (error) {
                    console.error('❌ 二进制上传异常:', error)
                    reject(error)
                }
            })
        },

        // H5端文件上传
        uploadH5FileToServer(fileObject) {
            return new Promise((resolve, reject) => {
                console.log('🚀 开始H5文件上传')

                try {
                    // 创建FormData
                    const formData = new FormData()
                    formData.append('file', fileObject.file, fileObject.name)

                    // 使用XMLHttpRequest上传
                    const xhr = new XMLHttpRequest()

                    xhr.onload = function() {
                        console.log('📤 上传响应状态:', xhr.status)
                        console.log('📤 上传响应内容:', xhr.responseText)

                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText)
                                if (response.code === 200 || response.success) {
                                    resolve({
                                        success: true,
                                        data: response.data || response
                                    })
                                } else {
                                    reject(new Error(response.message || '上传失败'))
                                }
                            } catch (e) {
                                reject(new Error('响应解析失败'))
                            }
                        } else {
                            reject(new Error(`上传失败，状态码: ${xhr.status}`))
                        }
                    }

                    xhr.onerror = function() {
                        console.error('❌ 上传请求失败')
                        reject(new Error('网络请求失败'))
                    }

                    xhr.ontimeout = function() {
                        console.error('❌ 上传超时')
                        reject(new Error('上传超时'))
                    }

                    // 设置超时时间
                    xhr.timeout = 60000 // 60秒

                    // 发送请求
                    xhr.open('POST', 'http://8.130.152.121:82/jobapi/common/upload')
                    xhr.send(formData)

                } catch (error) {
                    console.error('❌ H5上传异常:', error)
                    reject(error)
                }
            })
        },

        // 标准文件上传 - H5端使用
        uploadFileToServer(file) {
            return new Promise((resolve, reject) => {
                uni.uploadFile({
                    url: 'http://8.130.152.121:82/jobapi/common/upload',
                    filePath: file.path || file.url,
                    name: 'file',
                    success: (res) => {
                        console.log('上传成功:', res)
                        try {
                            const data = JSON.parse(res.data)
                            if (data.code === 200) {
                                resolve({
                                    success: true,
                                    data: data.data
                                })
                            } else {
                                reject(new Error(data.message || '上传失败'))
                            }
                        } catch (e) {
                            reject(new Error('响应解析失败'))
                        }
                    },
                    fail: (error) => {
                        console.error('上传失败:', error)
                        reject(new Error('上传失败'))
                    }
                })
            })
        },

        // 测试上传
        testUpload() {
            console.log('🧪 测试PDF上传功能')
            
            // 创建一个简单的PDF内容
            const pdfContent = '%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\nxref\n0 2\n0000000000 65535 f \n0000000009 00000 n \ntrailer\n<<\n/Size 2\n/Root 1 0 R\n>>\nstartxref\n74\n%%EOF'
            
            const encoder = new TextEncoder()
            const uint8Array = encoder.encode(pdfContent)
            
            const testFile = {
                name: 'test-resume.pdf',
                size: uint8Array.length,
                type: 'application/pdf',
                data: uint8Array.buffer // 转换为ArrayBuffer
            }
            
            console.log('🧪 测试文件对象:', testFile)
            this.handleFileUpload(testFile)
        }
    }
}
</script>

<style scoped>
.container {
    height: 100vh;
    background-color: #f5f5f5;
}

.content {
    padding: 20px;
    padding-top: 100px;
}

.upload-area {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
}

.upload-btn, .test-btn, .gallery-btn {
    width: 200px;
    height: 50px;
    background-color: #007AFF;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    margin-bottom: 10px;
}

.test-btn {
    background-color: #FF9500;
}

.gallery-btn {
    background-color: #34C759;
}

.tips {
    margin-top: 40px;
    padding: 20px;
    background-color: #f8f8f8;
    border-radius: 8px;
}

.tip-text {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 10px;
}

.tip-content {
    font-size: 14px;
    color: #666;
    display: block;
    margin-bottom: 5px;
    line-height: 1.5;
}
</style>
