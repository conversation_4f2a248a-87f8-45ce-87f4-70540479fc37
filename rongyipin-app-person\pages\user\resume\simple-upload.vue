<template>
    <view class="container">
        <u-navbar height="44px" :autoBack="true" leftIconSize="30" leftIconColor="#333" :safeAreaInsetTop="true" placeholder :fixed="true">
            <view class="u-nav-slot" slot="center">
                <text class="u-nav-title">简历上传</text>
            </view>
            <view slot="right" class="navbar-right">
                <u-icon name="more-dot-fill" size="20" color="#333"></u-icon>
            </view>
        </u-navbar>

        <view class="content">
            <!-- 上传说明区域 -->
            <view class="info-section">
                <view class="info-card">
                    <u-icon name="info-circle" size="20" color="#14B19E" style="margin-right: 8px;"></u-icon>
                    <text class="info-text">支持PDF、Word、图片格式，建议使用PDF格式以获得最佳效果</text>
                </view>
            </view>

            <!-- 分割线 -->
            <view class="divider"></view>

            <!-- 上传区域 -->
            <view class="upload-section">
                <view class="section-title">选择上传方式</view>

                <!-- 上传方式选择 -->
                <view class="upload-methods">
                    <view class="method-card" @click="selectFromFiles">
                        <view class="method-icon">
                            <u-icon name="folder-add" size="32" color="#14B19E"></u-icon>
                        </view>
                        <view class="method-info">
                            <text class="method-title">从文件选择</text>
                            <text class="method-desc">选择本地PDF、Word文档</text>
                        </view>
                    </view>

                    <view class="method-card" @click="selectFromAlbum">
                        <view class="method-icon">
                            <u-icon name="photo" size="32" color="#14B19E"></u-icon>
                        </view>
                        <view class="method-info">
                            <text class="method-title">从相册选择</text>
                            <text class="method-desc">选择简历图片或截图</text>
                        </view>
                    </view>

                    <view class="method-card" @click="takePhoto">
                        <view class="method-icon">
                            <u-icon name="camera" size="32" color="#14B19E"></u-icon>
                        </view>
                        <view class="method-info">
                            <text class="method-title">拍照上传</text>
                            <text class="method-desc">现场拍摄简历照片</text>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 分割线 -->
            <view class="divider" v-if="selectedFiles.length > 0"></view>

            <!-- 已选择的文件列表 -->
            <view v-if="selectedFiles.length > 0" class="file-section">
                <view class="section-title">已选择文件 ({{ selectedFiles.length }})</view>
                <view class="file-list">
                    <view v-for="(file, index) in selectedFiles" :key="index" class="file-item">
                        <view class="file-icon">
                            <u-icon :name="getFileIcon(file)" size="24" :color="getFileIconColor(file)"></u-icon>
                        </view>
                        <view class="file-info">
                            <text class="file-name">{{ file.name || '未知文件' }}</text>
                            <text class="file-size">{{ formatFileSize(file.size) }}</text>
                        </view>
                        <view class="file-actions">
                            <u-icon name="eye" size="18" color="#666" @click="previewFile(file, index)" style="margin-right: 12px;"></u-icon>
                            <u-icon name="close-circle-fill" size="18" color="#ff4757" @click="removeFile(index)"></u-icon>
                        </view>
                    </view>
                </view>
            </view>

            <!-- 上传进度区域 -->
            <view v-if="isUploading" class="progress-section">
                <view class="divider"></view>
                <view class="section-title">上传进度</view>
                <view class="progress-card">
                    <u-line-progress
                        :percent="uploadProgress"
                        :showPercent="true"
                        activeColor="#14B19E"
                        height="8"
                    ></u-line-progress>
                    <text class="progress-text">{{ uploadStatusText }}</text>
                </view>
            </view>

            <!-- 上传成功区域 -->
            <view v-if="uploadedFiles.length > 0" class="success-section">
                <view class="divider"></view>
                <view class="section-title">上传成功 ({{ uploadedFiles.length }})</view>
                <view class="success-list">
                    <view v-for="(file, index) in uploadedFiles" :key="index" class="success-item">
                        <u-icon name="checkmark-circle-fill" size="20" color="#5cb85c"></u-icon>
                        <view class="success-info">
                            <text class="success-name">{{ file.name }}</text>
                            <text class="success-url" @click="previewUploadedFile(file)">点击预览</text>
                        </view>
                    </view>
                </view>
            </view>
        </view>

        <!-- 底部提交按钮 -->
        <view class="bottom-section">
            <button
                class="submit-btn"
                :class="{ 'active': canSubmit }"
                :disabled="!canSubmit || isUploading"
                @click="submitResume"
            >
                {{ isUploading ? '上传中...' : '提交简历' }}
            </button>
        </view>

        <!-- 文件预览弹窗 -->
        <u-popup v-model="showPreview" mode="center" width="90%" height="80%" border-radius="12">
            <view class="preview-container">
                <view class="preview-header">
                    <text class="preview-title">文件预览</text>
                    <u-icon name="close" size="20" color="#666" @click="closePreview"></u-icon>
                </view>
                <view class="preview-content">
                    <image v-if="previewType === 'image'" :src="previewUrl" mode="aspectFit" class="preview-image"></image>
                    <view v-else class="preview-placeholder">
                        <u-icon :name="getFileIcon(previewFile)" size="48" color="#ccc"></u-icon>
                        <text class="placeholder-text">{{ previewFile?.name || '无法预览此文件类型' }}</text>
                        <text class="placeholder-desc">请在提交后通过系统查看完整内容</text>
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { fileApi } from '@/utils/api.js'

export default {
    data() {
        return {
            selectedFiles: [], // 已选择的文件列表
            uploadedFiles: [], // 上传成功的文件列表
            isUploading: false, // 是否正在上传
            uploadProgress: 0, // 上传进度
            uploadStatusText: '准备上传...', // 上传状态文本
            showPreview: false, // 是否显示预览弹窗
            previewFile: null, // 预览的文件
            previewUrl: '', // 预览URL
            previewType: '', // 预览类型
            maxFileSize: 10 * 1024 * 1024, // 最大文件大小 10MB
            allowedTypes: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'txt'] // 允许的文件类型
        }
    },
    computed: {
        // 是否可以提交
        canSubmit() {
            return this.uploadedFiles.length > 0 && !this.isUploading
        }
    },
    methods: {
        // 从文件管理器选择文件
        selectFromFiles() {
            console.log('� 从文件管理器选择文件')

            // #ifdef APP-PLUS
            this.openFileManager()
            // #endif

            // #ifdef H5
            this.createFileInput(['pdf', 'doc', 'docx'])
            // #endif

            // #ifdef MP-WEIXIN
            this.selectWeixinFile()
            // #endif
        },

        // 从相册选择文件
        selectFromAlbum() {
            console.log('🖼️ 从相册选择文件')

            uni.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'],
                sourceType: ['album'],
                success: (res) => {
                    const tempFilePath = res.tempFilePaths[0]
                    this.processImageFile(tempFilePath)
                },
                fail: (error) => {
                    console.error('选择图片失败:', error)
                    uni.showToast({
                        title: '选择图片失败',
                        icon: 'none'
                    })
                }
            })
        },

        // 拍照上传
        takePhoto() {
            console.log('📷 拍照上传')

            uni.chooseImage({
                count: 1,
                sizeType: ['original', 'compressed'],
                sourceType: ['camera'],
                success: (res) => {
                    const tempFilePath = res.tempFilePaths[0]
                    this.processImageFile(tempFilePath)
                },
                fail: (error) => {
                    console.error('拍照失败:', error)
                    uni.showToast({
                        title: '拍照失败',
                        icon: 'none'
                    })
                }
            })
        },

        // 处理图片文件
        processImageFile(filePath) {
            console.log('🖼️ 处理图片文件:', filePath)

            // 获取文件信息
            uni.getFileInfo({
                filePath: filePath,
                success: (res) => {
                    const fileName = `resume_image_${Date.now()}.jpg`
                    const fileObj = {
                        name: fileName,
                        path: filePath,
                        size: res.size,
                        type: 'image/jpeg',
                        extension: 'jpg'
                    }

                    if (this.validateFile(fileObj)) {
                        this.selectedFiles.push(fileObj)
                        this.uploadFile(fileObj)
                    }
                },
                fail: (error) => {
                    console.error('获取文件信息失败:', error)
                    uni.showToast({
                        title: '获取文件信息失败',
                        icon: 'none'
                    })
                }
            })
        },

        // 微信小程序选择文件
        selectWeixinFile() {
            // #ifdef MP-WEIXIN
            wx.chooseMessageFile({
                count: 1,
                type: 'file',
                success: (res) => {
                    const file = res.tempFiles[0]
                    const fileObj = {
                        name: file.name,
                        path: file.path,
                        size: file.size,
                        type: file.type || this.getFileTypeByName(file.name),
                        extension: this.getFileExtension(file.name)
                    }

                    if (this.validateFile(fileObj)) {
                        this.selectedFiles.push(fileObj)
                        this.uploadFile(fileObj)
                    }
                },
                fail: (error) => {
                    console.error('选择文件失败:', error)
                    uni.showToast({
                        title: '选择文件失败',
                        icon: 'none'
                    })
                }
            })
            // #endif
        },

        // 打开文件管理器 (APP端)
        openFileManager() {
            // #ifdef APP-PLUS
            try {
                const main = plus.android.runtimeMainActivity()
                const Intent = plus.android.importClass('android.content.Intent')
                const intent = new Intent(Intent.ACTION_GET_CONTENT)

                intent.addCategory(Intent.CATEGORY_OPENABLE)
                intent.setType('*/*')

                const REQUEST_CODE = 1001

                main.onActivityResult = (requestCode, resultCode, data) => {
                    if (requestCode === REQUEST_CODE && resultCode === -1 && data) {
                        const uri = data.getData()
                        this.processSelectedUri(uri)
                    }
                }

                main.startActivityForResult(intent, REQUEST_CODE)

            } catch (error) {
                console.error('打开文件管理器失败:', error)
                uni.showToast({
                    title: '打开文件管理器失败',
                    icon: 'none'
                })
            }
            // #endif
        },

        // 处理选择的URI
        processSelectedUri(uri) {
            // #ifdef APP-PLUS
            console.log('处理选择的URI:', uri.toString())

            // 直接使用URI上传
            const fileName = `selected_file_${Date.now()}`
            const fileObj = {
                name: fileName,
                path: uri.toString(),
                size: 0, // URI无法直接获取大小
                type: 'application/octet-stream',
                extension: 'unknown'
            }

            this.selectedFiles.push(fileObj)
            this.uploadFileByUri(uri, fileObj)
            // #endif
        },

        // H5端创建文件输入
        createFileInput(acceptTypes = ['pdf', 'doc', 'docx']) {
            // #ifdef H5
            const input = document.createElement('input')
            input.type = 'file'
            input.accept = acceptTypes.map(type => {
                switch(type) {
                    case 'pdf': return '.pdf,application/pdf'
                    case 'doc': return '.doc,application/msword'
                    case 'docx': return '.docx,application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                    case 'jpg': case 'jpeg': return '.jpg,.jpeg,image/jpeg'
                    case 'png': return '.png,image/png'
                    default: return `.${type}`
                }
            }).join(',')
            input.style.display = 'none'

            input.onchange = (e) => {
                const file = e.target.files[0]
                if (file) {
                    const fileObj = {
                        name: file.name,
                        size: file.size,
                        type: file.type,
                        extension: this.getFileExtension(file.name),
                        file: file // 保存原始File对象
                    }

                    if (this.validateFile(fileObj)) {
                        this.selectedFiles.push(fileObj)
                        this.uploadFile(fileObj)
                    }
                }
            }

            document.body.appendChild(input)
            input.click()
            document.body.removeChild(input)
            // #endif
        },

        // 文件验证
        validateFile(file) {
            // 检查文件大小
            if (file.size > this.maxFileSize) {
                uni.showToast({
                    title: `文件大小不能超过${this.formatFileSize(this.maxFileSize)}`,
                    icon: 'none'
                })
                return false
            }

            // 检查文件类型
            const extension = file.extension?.toLowerCase()
            if (extension && !this.allowedTypes.includes(extension)) {
                uni.showToast({
                    title: '不支持的文件类型',
                    icon: 'none'
                })
                return false
            }

            return true
        },

        // 上传文件
        uploadFile(fileObj) {
            this.isUploading = true
            this.uploadProgress = 0
            this.uploadStatusText = '开始上传...'

            // #ifdef APP-PLUS
            if (fileObj.path && fileObj.path.startsWith('content://')) {
                this.uploadFileByUri(fileObj.path, fileObj)
            } else {
                this.uploadFileByPath(fileObj)
            }
            // #endif

            // #ifdef H5
            this.uploadH5File(fileObj)
            // #endif

            // #ifdef MP-WEIXIN
            this.uploadWeixinFile(fileObj)
            // #endif
        },

        // 通过URI上传文件 (APP端)
        uploadFileByUri(uri, fileObj) {
            // #ifdef APP-PLUS
            uni.uploadFile({
                url: 'http://8.130.152.121:82/jobapi/common/upload',
                filePath: uri,
                name: 'file',
                formData: {
                    type: 5 // 简历文件类型
                },
                success: (res) => {
                    this.handleUploadSuccess(res, fileObj)
                },
                fail: (error) => {
                    this.handleUploadError(error, fileObj)
                }
            })
            // #endif
        },

        // 通过路径上传文件 (APP端)
        uploadFileByPath(fileObj) {
            // #ifdef APP-PLUS
            uni.uploadFile({
                url: 'http://8.130.152.121:82/jobapi/common/upload',
                filePath: fileObj.path,
                name: 'file',
                formData: {
                    type: 5 // 简历文件类型
                },
                success: (res) => {
                    this.handleUploadSuccess(res, fileObj)
                },
                fail: (error) => {
                    this.handleUploadError(error, fileObj)
                }
            })
            // #endif
        },

        // H5端上传文件
        uploadH5File(fileObj) {
            // #ifdef H5
            const formData = new FormData()
            formData.append('file', fileObj.file, fileObj.name)
            formData.append('type', '5')

            const xhr = new XMLHttpRequest()

            xhr.upload.onprogress = (e) => {
                if (e.lengthComputable) {
                    this.uploadProgress = Math.round((e.loaded / e.total) * 100)
                    this.uploadStatusText = `上传中... ${this.uploadProgress}%`
                }
            }

            xhr.onload = () => {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText)
                        this.handleUploadSuccess({ data: xhr.responseText }, fileObj)
                    } catch (e) {
                        this.handleUploadError(new Error('响应解析失败'), fileObj)
                    }
                } else {
                    this.handleUploadError(new Error(`上传失败，状态码: ${xhr.status}`), fileObj)
                }
            }

            xhr.onerror = () => {
                this.handleUploadError(new Error('网络请求失败'), fileObj)
            }

            xhr.open('POST', 'http://8.130.152.121:82/jobapi/common/upload')
            xhr.send(formData)
            // #endif
        },

        // 微信小程序上传文件
        uploadWeixinFile(fileObj) {
            // #ifdef MP-WEIXIN
            uni.uploadFile({
                url: 'http://8.130.152.121:82/jobapi/common/upload',
                filePath: fileObj.path,
                name: 'file',
                formData: {
                    type: 5
                },
                success: (res) => {
                    this.handleUploadSuccess(res, fileObj)
                },
                fail: (error) => {
                    this.handleUploadError(error, fileObj)
                }
            })
            // #endif
        },

        // 处理上传成功
        handleUploadSuccess(res, fileObj) {
            try {
                const data = JSON.parse(res.data)
                if (data.code === 200) {
                    this.uploadProgress = 100
                    this.uploadStatusText = '上传成功'

                    // 添加到上传成功列表
                    this.uploadedFiles.push({
                        ...fileObj,
                        url: data.data.url,
                        uploadTime: new Date().toLocaleString()
                    })

                    // 从选择列表中移除
                    const index = this.selectedFiles.findIndex(f => f.name === fileObj.name)
                    if (index > -1) {
                        this.selectedFiles.splice(index, 1)
                    }

                    this.isUploading = false

                    uni.showToast({
                        title: '上传成功',
                        icon: 'success'
                    })
                } else {
                    throw new Error(data.message || '上传失败')
                }
            } catch (e) {
                this.handleUploadError(new Error('响应解析失败'), fileObj)
            }
        },

        // 处理上传失败
        handleUploadError(error, fileObj) {
            console.error('上传失败:', error)
            this.isUploading = false
            this.uploadProgress = 0
            this.uploadStatusText = '上传失败'

            uni.showToast({
                title: error.message || '上传失败',
                icon: 'none'
            })
        },

        // 移除文件
        removeFile(index) {
            this.selectedFiles.splice(index, 1)
        },

        // 预览文件
        previewFile(file, index) {
            this.previewFile = file

            if (file.type && file.type.startsWith('image/')) {
                this.previewType = 'image'
                this.previewUrl = file.path || file.url || ''
            } else {
                this.previewType = 'document'
                this.previewUrl = ''
            }

            this.showPreview = true
        },

        // 预览上传成功的文件
        previewUploadedFile(file) {
            if (file.type && file.type.startsWith('image/')) {
                this.previewType = 'image'
                this.previewUrl = file.url
                this.previewFile = file
                this.showPreview = true
            } else {
                // 对于非图片文件，可以尝试打开外部应用
                // #ifdef APP-PLUS
                plus.runtime.openURL(file.url)
                // #endif

                // #ifdef H5
                window.open(file.url, '_blank')
                // #endif

                // #ifdef MP-WEIXIN
                uni.showToast({
                    title: '请在浏览器中查看',
                    icon: 'none'
                })
                // #endif
            }
        },

        // 关闭预览
        closePreview() {
            this.showPreview = false
            this.previewFile = null
            this.previewUrl = ''
            this.previewType = ''
        },

        // 提交简历
        async submitResume() {
            if (this.uploadedFiles.length === 0) {
                uni.showToast({
                    title: '请先上传简历文件',
                    icon: 'none'
                })
                return
            }

            uni.showLoading({ title: '提交中...' })

            try {
                // 使用第一个上传成功的文件
                const firstFile = this.uploadedFiles[0]
                const params = {
                    attachment_name: firstFile.name,
                    storage_path: firstFile.url
                }

                // 调用保存简历接口
                const result = await fileApi.postUpload(params)

                if (result.code === 200 || result.success) {
                    uni.hideLoading()
                    uni.showToast({
                        title: '提交成功',
                        icon: 'success'
                    })

                    setTimeout(() => {
                        uni.navigateBack()
                    }, 1500)
                } else {
                    throw new Error(result.message || '提交失败')
                }
            } catch (error) {
                console.error('提交失败:', error)
                uni.hideLoading()
                uni.showToast({
                    title: error.message || '提交失败',
                    icon: 'none'
                })
            }
        },

        // 获取文件图标
        getFileIcon(file) {
            if (!file || !file.extension) return 'file-text'

            const ext = file.extension.toLowerCase()
            switch (ext) {
                case 'pdf': return 'file-pdf'
                case 'doc': case 'docx': return 'file-word'
                case 'jpg': case 'jpeg': case 'png': return 'image'
                case 'txt': return 'file-text'
                default: return 'file'
            }
        },

        // 获取文件图标颜色
        getFileIconColor(file) {
            if (!file || !file.extension) return '#666'

            const ext = file.extension.toLowerCase()
            switch (ext) {
                case 'pdf': return '#ff4757'
                case 'doc': case 'docx': return '#2e86de'
                case 'jpg': case 'jpeg': case 'png': return '#5cb85c'
                case 'txt': return '#666'
                default: return '#666'
            }
        },

        // 格式化文件大小
        formatFileSize(bytes) {
            if (bytes === 0) return '0 B'
            const k = 1024
            const sizes = ['B', 'KB', 'MB', 'GB']
            const i = Math.floor(Math.log(bytes) / Math.log(k))
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
        },

        // 获取文件扩展名
        getFileExtension(fileName) {
            return fileName.split('.').pop()?.toLowerCase() || ''
        },

        // 根据文件名获取文件类型
        getFileTypeByName(fileName) {
            const ext = this.getFileExtension(fileName)
            switch (ext) {
                case 'pdf': return 'application/pdf'
                case 'doc': return 'application/msword'
                case 'docx': return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                case 'jpg': case 'jpeg': return 'image/jpeg'
                case 'png': return 'image/png'
                case 'txt': return 'text/plain'
                default: return 'application/octet-stream'
            }
        }
    }
}
</script>

<style scoped>
.container {
    height: 100vh;
    background-color: #f5f5f5;
}

.content {
    padding: 20px;
    padding-top: 100px;
    padding-bottom: 100px;
}

.navbar-right {
    padding-right: 15px;
}

/* 信息区域 */
.info-section {
    margin-bottom: 20px;
}

.info-card {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-text {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
}

/* 分割线 */
.divider {
    height: 1px;
    background-color: #e5e5e5;
    margin: 20px 0;
}

/* 区域标题 */
.section-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 15px;
}

/* 上传方式选择 */
.upload-section {
    margin-bottom: 20px;
}

.upload-methods {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.method-card {
    display: flex;
    align-items: center;
    padding: 16px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.method-card:active {
    transform: scale(0.98);
    background-color: #f8f9fa;
}

.method-icon {
    margin-right: 15px;
}

.method-info {
    flex: 1;
}

.method-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 4px;
}

.method-desc {
    font-size: 13px;
    color: #999;
    display: block;
}

/* 文件列表 */
.file-section {
    margin-bottom: 20px;
}

.file-list {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.file-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
    border-bottom: none;
}

.file-icon {
    margin-right: 12px;
}

.file-info {
    flex: 1;
}

.file-name {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 4px;
    word-break: break-all;
}

.file-size {
    font-size: 12px;
    color: #999;
    display: block;
}

.file-actions {
    display: flex;
    align-items: center;
}

/* 上传进度 */
.progress-section {
    margin-bottom: 20px;
}

.progress-card {
    padding: 15px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-text {
    font-size: 13px;
    color: #666;
    text-align: center;
    margin-top: 8px;
    display: block;
}

/* 上传成功 */
.success-section {
    margin-bottom: 20px;
}

.success-list {
    background-color: #fff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.success-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #f0f0f0;
}

.success-item:last-child {
    border-bottom: none;
}

.success-info {
    flex: 1;
    margin-left: 12px;
}

.success-name {
    font-size: 14px;
    font-weight: bold;
    color: #333;
    display: block;
    margin-bottom: 4px;
}

.success-url {
    font-size: 12px;
    color: #14B19E;
    display: block;
}

/* 底部提交按钮 */
.bottom-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px 20px;
    background-color: #fff;
    border-top: 1px solid #e5e5e5;
    z-index: 100;
}

.submit-btn {
    width: 100%;
    height: 48px;
    background-color: #ccc;
    color: #fff;
    border: none;
    border-radius: 24px;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.submit-btn.active {
    background-color: #14B19E;
}

.submit-btn:disabled {
    opacity: 0.6;
}

/* 预览弹窗 */
.preview-container {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e5e5e5;
}

.preview-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
}

.preview-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.preview-image {
    max-width: 100%;
    max-height: 100%;
}

.preview-placeholder {
    text-align: center;
}

.placeholder-text {
    font-size: 16px;
    color: #666;
    display: block;
    margin: 15px 0 8px;
}

.placeholder-desc {
    font-size: 13px;
    color: #999;
    display: block;
}
</style>
