<template>
	<view class="online-resume-page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<u-navbar  title="在线简历" :autoBack="true" :leftIconSize="30" 
				:leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
				<template #right>
					<!-- <view class="nav-right" @click="toggleEditMode">
						<u-icon :name="isEditMode ? 'checkmark' : 'edit-pen'" size="20" color="#333"></u-icon>
						<text class="nav-text">{{ isEditMode ? '完成' : '编辑' }}</text>
					</view> -->
				</template>
			</u-navbar>
		</view>

		<!-- 内容区域 -->
		<scroll-view class="content-container" scroll-y>
			<!-- 个人信息 -->
			<view class="resume-section">
				<!-- <view class="section-header">
					<view class="header-left">
						<u-icon name="account" size="20" color="#14B19E"></u-icon>
						<text class="section-title">个人信息</text>
					</view>
					<view  class="edit-btn" @click="editPersonalInfo">
						<u-icon name="edit-pen" size="28" color="#999"></u-icon>
					</view>
				</view> -->
				<view class="personal-info" @click="editPersonalInfo">
					<view class="avatar-section">
						<image class="avatar" :src="resumeData.avatar || '@/static/default-avatar.png'" mode="aspectFill"></image>
						<view class="basic-info">
							<text class="name">{{ resumeData.name || '姓名' }}</text>
							<text class="age-gender">{{ resumeData.first_word_time }}年经验 . {{ resumeData.age }}岁 </text>
						</view>
					</view>
					<view class="contact-info">
						<view class="contact-item">
							<u-icon name="phone" size="28" color="#666"></u-icon>
							<text class="contact-text">{{ resumeData.telephone || '手机号码' }}</text>
							<u-icon name="email" size="28" color="#666"></u-icon>
							<text class="contact-text">{{ resumeData.wechat || '微信' }}</text>
						</view>
						<view class="contact-item">
							
						</view>
					</view>
				</view>
			</view>
			<!-- 个人优势 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<!-- <u-icon name="setting" size="20" color="#14B19E"></u-icon> -->
						<text class="section-title">个人优势</text>
					</view>
					<view class="edit-btn" @click="editAdvantage">
						<u-icon name="edit-pen" size="28" color="#999"></u-icon>
					</view>
				</view>
				<view class="skills">
					<text class="">{{resumeData.advantage}}</text>
				</view>
			</view>
			<!-- 求职状态 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<!-- <u-icon name="setting" size="20" color="#14B19E"></u-icon> -->
						<text class="section-title">求职状态</text>
					</view>
					<!-- <view class="edit-btn">
						<u-icon name="edit-pen" size="28" color="#999"></u-icon>
					</view> -->
				</view>
				<view class="status" @click="editWordText">
					<text class="">{{resumeData.word_text}}</text>
					<uni-icons type="right" size="18" color="#C0C4CC"></uni-icons>
				</view>
			</view>

			<!-- 求职意向 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<!-- <u-icon name="star" size="20" color="#14B19E"></u-icon> -->
						<text class="section-title">求职意向</text>
					</view>
					<view class="edit-btn" >
						<u-icon v-if="resumeData.resume&&resumeData.resume.length<5" name="plus" size="28" color="#999" @click="addzhiwei"></u-icon>
					</view>
				</view>
				<view class="job-intention">
					<view class="job-intention-item" v-for="item in resumeData.resume" :key="item.id" @click="zwClick(item)">
						<view>
							<view>
								<text class="item-job_classname">{{item.job_classname}}</text>
								<text class="item-salary">{{item.minsalary}}-{{ item.maxsalary }}</text>
							</view>
							<view class="item-ip-hy">
								<text class="item-ip">{{item.city_classname}}</text>
								<text class="item-hy_name">{{item.hy_name}}</text>
							</view>
						</view>
						<view>
							<uni-icons type="right" size="18" color="#C0C4CC"></uni-icons>
						</view>
						
					</view>
					
				</view>
			</view>

			<!-- 工作经历 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<!-- <u-icon name="briefcase" size="20" color="#14B19E"></u-icon> -->
						<text class="section-title">工作经历</text>
					</view>
					<view class="edit-btn" @click="addWorkExperience">
						<u-icon name="plus" size="28" color="#999"></u-icon>
					</view>
				</view>
				<view class="work-experience">
					<view v-for="(work, index) in resumeData.resume_work" :key="index" class="work-item" @click="editWorkExperience(work)">
						<view class="work-header">
							<view class="work-title">
								<text class="company-name">{{ work.name }}</text>
								
								<text class="work-time">{{ work.sdate }} - {{ work.edate }}</text>
								<text class="position-name">{{ work.title }}</text>
							</view>
							<uni-icons type="right" size="18" color="#C0C4CC"></uni-icons>
						</view>
						<view class="work-description">{{ work.content }}</view>
						<!-- <view class="item-actions">
							<u-icon name="edit-pen" size="14" color="#999" @click="editWorkExperience(index)"></u-icon>
							<u-icon name="trash" size="14" color="#ff4444" @click="deleteWorkExperience(index)"></u-icon>
						</view> -->
					</view>
				</view>
			</view>

			<!-- 项目经历 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<!-- <u-icon name="folder" size="20" color="#14B19E"></u-icon> -->
						<text class="section-title">项目经历</text>
					</view>
					<view class="edit-btn" @click="addProjectExperience">
						<u-icon name="plus" size="28" color="#999"></u-icon>
					</view>
				</view>
				<view class="project-experience">
					<view v-for="(project, index) in resumeData.resume_project" :key="index" class="project-item" @click="editProjectExperience(project)">
						<view class="work-header">
							<view class="work-title">
								<text class="company-name">{{ project.name }}</text>
								
								<text class="work-time">{{ project.sdate }} - {{ project.edate }}</text>
								<text class="position-name">{{ project.title }}</text>
							</view>
							<uni-icons type="right" size="18" color="#C0C4CC"></uni-icons>
						</view>
						<!-- <view class="project-header">
							<text class="project-name">{{ project.name }}</text>
							<text class="project-time">{{ project.time }}</text>
						</view> -->
						<view class="project-description">{{ project.content }}</view>
						<!-- <view class="item-actions">
							<u-icon name="edit-pen" size="14" color="#999" @click="editProjectExperience(index)"></u-icon>
							<u-icon name="trash" size="14" color="#ff4444" @click="deleteProjectExperience(index)"></u-icon>
						</view> -->
					</view>
				</view>
			</view>

			<!-- 教育经历 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<!-- <u-icon name="school" size="20" color="#14B19E"></u-icon> -->
						<text class="section-title">教育经历</text>
					</view>
					<view class="edit-btn" @click="addEducation">
						<u-icon name="plus" size="28" color="#999"></u-icon>
					</view>
				</view>
				<view class="education">
					<view v-for="(edu, index) in resumeData.resume_edu" :key="index" class="education-item" @click="editEducation(edu, index)">
						<view class="education-header">
							<view class="education-info">
								<text class="school-name">{{ edu.name }}</text>
								<text class="work-time">{{ edu.sdate }} - {{ edu.edate }}</text>
								<text class="major-name">{{ edu.education_name }}</text>
								<text class="education-time">{{ edu.specialty }}</text>
							</view>
							<uni-icons type="right" size="18" color="#C0C4CC"></uni-icons>
						</view>
						
					</view>
				</view>
			</view>

			

			<!-- 专业技能 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<!-- <u-icon name="setting" size="20" color="#14B19E"></u-icon> -->
						<text class="section-title">专业技能</text>
					</view>
					<view class="edit-btn" @click="editskillAdvantage">
						<u-icon name="edit-pen" size="28" color="#999"></u-icon>
					</view>
				</view>
				<view class="skills">
					<text class="">{{resumeData.skill_advantage}}</text>
				</view>
			</view>

			<!-- 资格证书 -->
			<view class="resume-section">
				<view class="section-header">
					<view class="header-left">
						<!-- <u-icon name="heart" size="20" color="#14B19E"></u-icon> -->
						<text class="section-title">资格证书</text>
					</view>
					<view class="edit-btn" @click="editCertificate">
						<u-icon name="edit-pen" size="28" color="#999"></u-icon>
					</view>
				</view>
				<view class="hobbies">
					<view v-for="(hobby, index) in resumeData.certificate_label_text" :key="index" class="hobby-tag">
						<text class="hobby-text">{{ hobby.name }}</text>
						<!-- <u-icon  name="close" size="12" color="#999" @click="deleteHobby(index)"></u-icon> -->
					</view>
				</view>
			</view>
		</scroll-view>
		<view>
			<u-picker :show="showWordText" :columns="columns" keyName="name" @confirm="confirmPicker"></u-picker>
		</view>
		
		
	</view>
</template>

<script>
import { onlineResume,dictApi,userApi } from '@/utils/api.js'
export default {
	data() {
		return {
			isEditMode: false,
			showWordText: false,//是否编辑求职状态
			columns:[['已离职，寻求新工作', '在职，考虑更好职位','其他']],
			resumeData: {
				avatar: '',
				name: '张三',
				age: '25',
				gender: '男',
				phone: '138****8888',
				email: '<EMAIL>',
				expectedPosition: '前端开发工程师',
				expectedSalary: '8K-12K',
				workLocation: '北京市',
				familyMembers: [
					{ relation: '父亲', name: '张XX', work: '教师' },
					{ relation: '母亲', name: '李XX', work: '医生' }
				],
				workExperience: [
					{
						company: 'ABC科技有限公司',
						position: '前端开发工程师',
						startTime: '2022-03',
						endTime: '至今',
						description: '负责公司前端项目开发，参与产品需求分析，完成页面开发和优化工作。'
					}
				],
				projectExperience: [
					{
						name: '电商管理系统',
						time: '2022-06 至 2022-12',
						description: '负责电商后台管理系统的前端开发，使用Vue.js技术栈。'
					}
				],
				education: [
					{
						school: '北京大学',
						major: '计算机科学与技术',
						time: '2018-09 至 2022-06'
					}
				],
				awards: [
					{
						name: '优秀员工奖',
						time: '2023-12',
						description: '年度优秀员工，工作表现突出'
					}
				],
				skills: [
					{ name: 'JavaScript', level: 4 },
					{ name: 'Vue.js', level: 4 },
					{ name: 'React', level: 3 },
					{ name: 'Node.js', level: 3 }
				],
				hobbies: ['阅读', '运动', '旅行', '摄影']
			}
		}
	},
	onLoad() { 
		this.filterData();
	},
	onShow() {
		this.loadResumeData()
	},
	methods: {
		//
		 //请求字典数据
		async filterData(){
			console.log("开始请求数据")
			
			await dictApi.getAllDictionary().then(res=>{
				uni.hideLoading();
				if(res.code==200){
					
					this.columns=[[...res.data.user_jobstatus.data]];
					console.log('dasd',this.columns)
				}else{
					uni.showToast({
						title:res.msg,
						icon:'none'
					})
				}
			
				
				console.log(this.categories)
			})
		},
		//编辑求职状态
		editWordText(){
			this.showWordText = true;
		},
		//编辑求职状态提交
		async confirmPicker(val){
			
			const res = await userApi.userSave({ work_id:val.value[0].id })
            uni.hideLoading();
            if (res.code == 200) {
				this.resumeData.word_text=val.value[0].name;
				this.showWordText = false
                let userInfo = uni.getStorageSync('userInfo') || {};
                userInfo.work_id =val.indexs[0];
                uni.setStorageSync('userInfo', userInfo);
                uni.showToast({
                    title: '保存成功',
                    icon: 'success'
                });

                // 延迟返回上一页
                // setTimeout(() => {
                //     uni.navigateBack();
                // }, 1500);
            } else {
                uni.showToast({
                    title: res.msg || '保存失败，请重试',
                    icon: 'none'
                });
            }



		},
		// 切换编辑模式
		toggleEditMode() {
			this.isEditMode = !this.isEditMode
			if (!this.isEditMode) {
				// 退出编辑模式时保存数据
				this.saveResumeData()
			}
		},
		//编辑求职意向
        zwClick(item){ 
            // const itemData = JSON.stringify(item)
            if(this.resumeData.resume.length > 1){
                uni.navigateTo({
                    url:'/pages/index/addinfo?id='+item.id+'&del=true'
                })
            }else{
                uni.navigateTo({
                    url:'/pages/index/addinfo?id='+item.id+'&del=false'
                })
            }
            
        },
		//添加职位
        addzhiwei(){
            uni.navigateTo({
				url:"/pages/index/addinfo"
			})
        },

		// 加载简历数据
		async loadResumeData() {
			await onlineResume.resume().then(res => {
				uni.hideLoading();
				this.resumeData = res.data;

			}).catch(err => {
				console.error("获取简历数据失败:", err)
				uni.showToast({
					title: '加载简历数据失败',
					icon: 'none'
				})
			})
		},

		// 保存简历数据
		async saveResumeData() {
			try {
				uni.showLoading({ title: '保存中...' })
				// TODO: 调用API保存简历数据
				// await this.$api.saveResumeData(this.resumeData)
				uni.hideLoading()
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				})
			} catch (error) {
				uni.hideLoading()
				console.error('保存简历数据失败:', error)
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				})
			}
		},
		//个人优势
		editAdvantage() {
			uni.navigateTo({
				url: '/pages/user/resume/personAdvantage'
			})
		},
		//个人优势
		editskillAdvantage() {
			uni.navigateTo({
				url: '/pages/user/resume/skillAdvantage'
			})
		},

		// 编辑个人信息
		editPersonalInfo() {
			uni.navigateTo({
				url: '/pages/user/resume/editPersonalInfo'
			})
		},

		// 编辑求职意向
		editJobIntention() {
			uni.navigateTo({
				url: '/pages/user/resume/editJobIntention'
			})
		},

		// 编辑家庭成员
		editFamilyMembers() {
			this.showEditModal('家庭成员', 'familyMembers')
		},

		// 编辑家庭成员
		editFamilyMember(index) {
			console.log('编辑家庭成员:', index)
		},

		// 删除家庭成员
		deleteFamilyMember(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个家庭成员吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.familyMembers.splice(index, 1)
					}
				}
			})
		},

		// 添加工作经历
		addWorkExperience() {
			uni.navigateTo({
				url: '/pages/user/resume/experience'
			})
		},

		// 编辑工作经历
		editWorkExperience(item) {
			uni.navigateTo({
				url: '/pages/user/resume/experience?id=' + item.id
			})
		},

		// 删除工作经历
		deleteWorkExperience(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条工作经历吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.workExperience.splice(index, 1)
					}
				}
			})
		},

		// 添加项目经历
		addProjectExperience() {
			uni.navigateTo({
				url: '/pages/user/resume/projectExperience'
			})
			console.log('添加项目经历')
		},

		// 编辑项目经历
		editProjectExperience(item) {
			uni.navigateTo({
				url: '/pages/user/resume/projectExperience?id=' + item.id
			})
		},

		// 删除项目经历
		deleteProjectExperience(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个项目经历吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.projectExperience.splice(index, 1)
					}
				}
			})
		},

		// 添加教育经历
		addEducation() {
			uni.navigateTo({
				url: '/pages/user/resume/education'
			})
			console.log('添加教育经历')
		},

		// 编辑教育经历
		editEducation(item) {
			uni.navigateTo({
				url: '/pages/user/resume/education?id=' + item.id
			})
			// console.log('编辑教育经历:', index)
		},

		// 删除教育经历
		deleteEducation(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这条教育经历吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.education.splice(index, 1)
					}
				}
			})
		},

		// 添加获奖经历
		addAward() {
			console.log('添加获奖经历')
		},

		// 编辑获奖经历
		editAward(index) {
			console.log('编辑获奖经历:', index)
		},

		// 删除获奖经历
		deleteAward(index) {
			uni.showModal({
				title: '确认删除',
				content: '确定要删除这个获奖经历吗？',
				success: (res) => {
					if (res.confirm) {
						this.resumeData.awards.splice(index, 1)
					}
				}
			})
		},

		// 编辑专业技能
		editSkills() {
			console.log('编辑专业技能')
		},

		// 编辑资格证书
		editCertificate() {
			uni.navigateTo({
				url: '/pages/user/resume/certificate'
			})
			// console.log('编辑资格证书')
		},

		// 删除兴趣爱好
		deleteHobby(index) {
			this.resumeData.hobbies.splice(index, 1)
		}
	}
}
</script>

<style lang="scss" scoped>
.online-resume-page {
	min-height: 100vh;
	background-color: #f8f8f8;
}

/* 顶部导航栏 */
// .navbar {
// 	position: fixed;
// 	top: 0;
// 	left: 0;
// 	right: 0;
// 	z-index: 999;
// 	background-color: #fff;
// }

.nav-right {
	display: flex;
	align-items: center;
	gap: 8rpx;
}

.nav-text {
	font-size: 28rpx;
	color: #333;
}

/* 内容容器 */
.content-container {
	height: calc(100vh - 88rpx);
	// padding: 30rpx;
	box-sizing: border-box;
	background: white;
}

/* 简历模块 */
.resume-section {
	background: white;
	// border-radius: 16rpx;
	margin-bottom: 20rpx;
	overflow: hidden;
	border-bottom: 1rpx solid #eee;
	// box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.section-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 30rpx 0;
	// border-bottom: 1rpx solid #f0f0f0;
	// background: linear-gradient(135deg, #f8fffe 0%, #f0fdfb 100%);
}

.header-left {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.section-title {
	font-size: 32rpx;
	font-weight: 500;
	color: #333;
}

.edit-btn {
	padding: 8rpx;
	border-radius: 8rpx;
	// background: rgba(20, 177, 158, 0.1);
}

/* 个人信息 */
.personal-info {
	padding: 30rpx;
}

.avatar-section {
	display: flex;
	align-items: center;
	margin-bottom: 30rpx;
}

.avatar {
	width: 120rpx;
	height: 120rpx;
	border-radius: 60rpx;
	margin-right: 30rpx;
	background: #f5f5f5;
}

.basic-info {
	flex: 1;
}

.name {
	display: block;
	font-size: 36rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 12rpx;
}

.age-gender {
	font-size: 28rpx;
	color: #666;
}

.contact-info {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.contact-item {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.contact-text {
	font-size: 28rpx;
	color: #666;
}
.status{
	display: flex;
	justify-content: space-between;
	padding: 30rpx;
}

/* 求职意向 */
.job-intention {
	padding: 30rpx;
}

.intention-item {
	display: flex;
	align-items: center;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.intention-label {
	font-size: 28rpx;
	color: #666;
	width: 160rpx;
}

.intention-value {
	font-size: 28rpx;
	color: #333;
	flex: 1;
}
.job-intention-item{
	display: flex;
	justify-content: space-between;
	font-size: 24rpx;
	padding: 20rpx 0;
	.item-ip-hy{
		color: #999;
	}
}

/* 家庭成员 */
.family-members {
	padding: 30rpx;
}

.family-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.family-info {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex: 1;
}

.family-relation {
	font-size: 26rpx;
	color: #14B19E;
	background: rgba(20, 177, 158, 0.1);
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	min-width: 80rpx;
	text-align: center;
}

.family-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.family-work {
	font-size: 26rpx;
	color: #666;
}

.item-actions {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

/* 工作经历 */
.work-experience {
	padding: 30rpx;
}

.work-item {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.work-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	margin-bottom: 16rpx;
}

.work-title {
	flex: 1;
}

.company-name {
	display: block;
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.position-name {
	font-size: 26rpx;
	color: #14B19E;
	display: inline-block;
    margin-left: 20rpx;
}

.work-time {
	font-size: 24rpx;
	color: #999;
	white-space: nowrap;
}

.work-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 项目经历 */
.project-experience {
	padding: 30rpx;
}

.project-item {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.project-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.project-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.project-time {
	font-size: 24rpx;
	color: #999;
}

.project-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 教育经历 */
.education {
	padding: 30rpx;
}

.education-item {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.education-header {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
}

.education-info {
	flex: 1;
}

.school-name {
	display: block;
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
	margin-bottom: 8rpx;
}

.major-name {
	font-size: 26rpx;
	color: #14B19E;
	display: inline-block;
    margin-left: 9px;
}

.education-time {
	font-size: 24rpx;
	color: #999;
}

/* 获奖经历 */
.awards {
	padding: 30rpx;
}

.award-item {
	position: relative;
	padding: 20rpx 0;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.award-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 16rpx;
}

.award-name {
	font-size: 30rpx;
	font-weight: 600;
	color: #333;
}

.award-time {
	font-size: 24rpx;
	color: #999;
}

.award-description {
	font-size: 26rpx;
	color: #666;
	line-height: 1.6;
}

/* 专业技能 */
.skills {
	padding: 30rpx;
}

.skill-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 24rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.skill-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.skill-level {
	display: flex;
	gap: 8rpx;
}

.skill-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: #e0e0e0;

	&.active {
		background: #14B19E;
	}
}

/* 兴趣爱好 */
.hobbies {
	padding: 30rpx;
	display: flex;
	flex-wrap: wrap;
	gap: 16rpx;
}

.hobby-tag {
	display: flex;
	align-items: center;
	gap: 8rpx;
	background: rgba(20, 177, 158, 0.1);
	color: #14B19E;
	padding: 12rpx 20rpx;
	border-radius: 20rpx;
	font-size: 26rpx;
}

.hobby-text {
	font-size: 26rpx;
	color: #14B19E;
}
</style>
