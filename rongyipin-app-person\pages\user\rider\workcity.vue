<template>
    <view class="city-select-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
          <u-navbar height="44px" title="选择工作城市" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed >
            </u-navbar>
        </view>

        <!-- 城市选择内容 -->
        <view class="content-container">
            <!-- 省份和城市列表 -->
            <view class="city-container">
                <!-- 左侧省份列表 -->
                <scroll-view class="province-list" scroll-y="true" :scroll-top="provinceScrollTop">
                    <view v-for="(province, index) in provinceList" :key="province.id" class="province-item"
                        :class="{ 'active': selectedProvinceIndex === index }" @click="selectProvince(index)">
                        <text class="province-name">{{ province.name }}</text>
                    </view>
                </scroll-view>

                <!-- 右侧城市列表 -->
                <scroll-view class="city-list" scroll-y="true" :scroll-top="cityScrollTop">
                    <view v-for="city in currentCities" :key="city.id" class="city-item"
                        :class="{ 'selected': selectedCity && selectedCity.id === city.id }" @click="selectCity(city)">
                        <text class="city-name">{{ city.name }}</text>
                        <view v-if="selectedCity && selectedCity.id === city.id" class="check-icon">✓</view>
                    </view>
                </scroll-view>
            </view>
        </view>

        <!-- 底部完成按钮 -->
        <view class="bottom-section">
            <view class="complete-btn" :class="{ 'active': selectedCity }" @click="handleComplete">
                <text class="btn-text">完成</text>
            </view>
        </view>
    </view>
</template>

<script>
import { homeApi } from "@/utils/api"
export default {
    data() {
        return {
            selectedProvinceIndex: 0, // 默认选择第一个省份
            selectedCity: null,
            provinceList: [],
            loading: false,
            provinceScrollTop: 0, // 省份列表滚动位置
            cityScrollTop: 0 // 城市列表滚动位置
        }
    },
    computed: {
        // 当前选中省份的城市列表
        currentCities() {
            if (this.provinceList.length > 0 && this.selectedProvinceIndex >= 0) {
                return this.provinceList[this.selectedProvinceIndex].children || []
            }
            return []
        }
    },
    async mounted() {
        uni.showLoading({
            title: '加载中'
        });
        console.log('111')
        try {
            // TODO: 替换为实际的API接口
            const response = await homeApi.getcitylate()
            // this.provinceList = response.data
            if (response.code === 200) {
                this.provinceList = response.data

                // 默认选择第一个省份的第一个城市
                if (this.provinceList.length > 0 && this.currentCities.length > 0) {
                    this.selectedCity = this.currentCities[0]
                }
                uni.hideLoading();
            } else {
                uni.showToast({
                    title: response.msg,
                    icon: 'none'
                })
                uni.hideLoading();
            }

        } catch (error) {
            console.error('加载城市数据失败:', error)
            uni.showToast({
                title: '加载城市数据失败',
                icon: 'none'
            })
        }
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack()
        },

        // 加载城市数据
        // async loadCityData() {
        //     this.loading = true
        //     try {
        //         // TODO: 替换为实际的API接口
        //         const response = await homeApi.getcitylate()
        //         // this.provinceList = response.data
        //         if (response.code === 200) {
        //             this.provinceList = response.data

        //             // 默认选择第一个省份的第一个城市
        //             if (this.provinceList.length > 0 && this.currentCities.length > 0) {
        //                 this.selectedCity = this.currentCities[0]
        //             }
        //             uni.hideLoading();
        //         } else {
        //             uni.showToast({
        //                 title: response.msg,
        //                 icon: 'none'
        //             })
        //             uni.hideLoading();
        //         }

        //     } catch (error) {
        //         console.error('加载城市数据失败:', error)
        //         uni.showToast({
        //             title: '加载城市数据失败',
        //             icon: 'none'
        //         })
        //     } finally {
        //         this.loading = false
        //     }
        // },

        // 选择省份
        selectProvince(index) {
            this.selectedProvinceIndex = index
            this.selectedCity = null // 清空已选城市

            // 重置城市列表滚动位置
            this.cityScrollTop = 0

            // 默认选择该省份的第一个城市
            if (this.currentCities.length > 0) {
                this.selectedCity = this.currentCities[0]
            }
        },

        // 选择城市
        selectCity(city) {
            this.selectedCity = city
        },

        // 完成选择
        handleComplete() {
            if (!this.selectedCity) {
                uni.showToast({
                    title: '请选择工作城市',
                    icon: 'none'
                })
                return
            }

            // 返回选中的城市数据
            const selectedData = {
                province: this.provinceList[this.selectedProvinceIndex],
                city: this.selectedCity
            }

            console.log('选中的城市:', selectedData)

            // TODO: 这里可以调用API保存选择的城市
            // await this.$api.saveWorkCity(selectedData)

            // 返回上一页并传递数据
            uni.$emit('citySelected', selectedData)
            uni.navigateBack()
        },
    }
}
</script>

<style lang="scss" scoped>
.city-select-page {
    height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 顶部导航栏 */
.navbar {
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // height: 88rpx;
    // background-color: #fff;
    // padding: 0 30rpx;
    // position: fixed;
    // top: 0;
    // left: 0;
    // right: 0;
    // z-index: 999;
    // border-bottom: 1rpx solid #f0f0f0;

    // .nav-left,
    // .nav-right {
    //     width: 60rpx;
    //     height: 60rpx;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    // }

    // .nav-title {
    //     flex: 1;
    //     text-align: center;

    //     .title-text {
    //         font-size: 32rpx;
    //         font-weight: 500;
    //         color: #333;
    //     }
    // }
}

/* 内容容器 */
.content-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 城市选择容器 */
.city-container {
    flex: 1;
    display: flex;
    background-color: #fff;
    overflow: hidden;
}

/* 左侧省份列表 */
.province-list {
    width: 240rpx;
    height: 100%;
    background-color: #f8f8f8;
    border-right: 1rpx solid #e8e8e8;
}

.province-item {
    padding: 30rpx 20rpx;
    border-bottom: 1rpx solid #e8e8e8;
    position: relative;

    .province-name {
        font-size: 28rpx;
        color: #333;
    }

    &.active {
        background-color: #fff;

        .province-name {
            color: #14B19E;
            font-weight: 500;
        }

        &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 6rpx;
            background-color: #14B19E;
        }
    }
}

/* 右侧城市列表 */
.city-list {
    flex: 1;
    height: 100%;
    background-color: #fff;
}

.city-item {
    padding: 30rpx 40rpx;
    border-bottom: 1rpx solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .city-name {
        font-size: 28rpx;
        color: #333;
    }

    .check-icon {
        font-size: 32rpx;
        color: #14B19E;
        font-weight: bold;
    }

    &.selected {
        .city-name {
            color: #14B19E;
            font-weight: 500;
        }
    }

    &:active {
        background-color: #f8f8f8;
    }
}

/* 底部区域 */
.bottom-section {
    background-color: #fff;
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
}

/* 完成按钮 */
.complete-btn {
    background-color: #999;
    border-radius: 8rpx;
    padding: 28rpx;
    text-align: center;
    transition: background-color 0.3s ease;

    .btn-text {
        font-size: 32rpx;
        color: #fff;
        font-weight: 500;
    }

    /* 激活状态 - 选择了城市 */
    &.active {
        background-color: #14B19E;
    }
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>