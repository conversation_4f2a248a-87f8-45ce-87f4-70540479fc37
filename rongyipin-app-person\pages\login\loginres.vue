<template>
	<view style="height: 100vh; position: relative;">
		<u-navbar class="pub_pading" left-arrow @click-left="onClickLeft" title="" />
		<div class="top_jl ">
			<u-cell-group inset>
				<u-cell center :border="false">
					<template slot="title">
						<view class="top_jl_box">
							<div>忘记密码</div>
						</view>
					</template>
					<template slot="label">
						<view class="">
							不要慌张，密码重置一下就好啦
						</view>
					</template>
				</u-cell>
			</u-cell-group>
		</div>
		<u-cell-group inset style="margin-top: 50rpx;" :border="false">
			<u-cell>
				<template slot="title">
					<view>+86</view>
				</template>
				<template slot="value">
					<view style="text-align: left;">
						<input placeholder="请输入手机号码" type="text" v-model="phone" @blur="validatePhone" />
					</view>
				</template>
			</u-cell>
			<u-cell>
				<template slot="title">
					<view class="">
						<input type="text" v-model="sms" placeholder="请输入短信验证码" />
					</view>
				</template>
				<template slot="value">
					<view>
						<view class="" @click="validateSms" style="color: #11C289; font-weight: 800;">
							获取验证码
						</view>

					</view>
				</template>

			</u-cell>

			<u-cell :border="false">
				<template slot="title">
					<view class="">
						<u-button @click="handleLogin" color="#00E698" type="large"
							style="border: none; border-radius: 20rpx;">下一步</u-button>
					</view>
				</template>
			</u-cell>
		</u-cell-group>
	</view>
</template>

<script>
	import {
		phoneSend,
		resetpwd,
		phoneCheck
	} from '../../utils/api.js'
	import { userApi, priseApi } from '../../utils/api'
	export default {
		data() {
			return {
				phone: '',
				sms: ''
			}
		},
		methods: {
			onClickLeft() {
				uni.navigateBack();
			},
			validatePhone(e) {
				console.log("我看看手机号", this.phone);
				// 手机号格式验证
				if (!/^1[3-9]\d{9}$/.test(this.phone)) {
					uni.showToast({
						title: '请输入正确手机号',
						icon: 'error',
						duration: 2000
					});
					return false;
				}
				return true;
			},
			validateSms() {
				userApi.Captcha({
					event: 'joblogin',
					phone: this.phone,
				}).then(res => {
					uni.showToast({
						title: res.msg,
						icon: res.code === 0 ? 'success' : 'error', // 根据返回结果判断提示类型
						duration: 2000
					});
					console.log("res", res);
				})
			},
			handleLogin() {
				// uni.navigateTo({
				// 				url: `./respassword?phone=${this.phone}`
				// 			})
				// 在提交前进行所有校验
				if (this.validatePhone()) {
					userApi.phoneCheck({
						phone: this.phone,
						event: "joblogin",
						code: this.sms
					}).then(res => {
						if (res.code == 200) {
							uni.navigateTo({
								url: `./respassword?phone=${this.phone}`
							})

						} else {
							uni.showToast({
								title: res.msg,
								icon: res.code === 0 ? 'success' : 'error', // 根据返回结果判断提示类型
								duration: 2000
							});
						}
						console.log(res);
					})
					// 校验通过，执行登录逻辑
					console.log("手机号:", this.phone);
					console.log("验证码:", this.sms);
					// 你可以在这里添加你的登录逻辑
				}
			}
		}
	}
</script>

<style scoped>
	.top_jl_box {
		display: flex;
		align-items: center;
		font-weight: 800;
		font-size: 36rpx;
	}

	.top_jl {
		margin-top: 20rpx;
	}

	.top_jl /deep/ .u-cell__title {
		flex: 1;
	}

	.zp_img {
		width: 80rpx;
		height: 80rpx;
	}

	.zp_img image {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.mg_lt /deep/ .u-cell__title {
		margin-left: 20px;
	}

	text {
		color: #11C289;
	}

	.icon_phone {
		width: 50rpx;
		height: 50rpx;
	}

	.icon_phone image {
		width: 100%;
		height: 100%;
	}

	.login-options {
		display: flex;
		align-items: center;
		/* 垂直居中 */
		/* 靠下对齐 */
		flex-direction: column;
		/* 垂直排列 */
		/* 添加这个来让内容推到下方 */
	}

	.title {
		font-size: 28rpx;
		font-weight: 600;
		margin-bottom: 10rpx;
		/* 调整标题和图标之间的间距 */
	}
</style>

