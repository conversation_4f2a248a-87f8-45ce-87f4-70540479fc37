<template>
    <view class="interview-list-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="面试记录" :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'"
                safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- Tab切换 -->
        <view class="tab-container">
            <view class="tab-wrapper">
                <view v-for="(tab, index) in tabs" :key="index" class="tab-item"
                    :class="{ active: currentTab === index }" @click="switchTab(index)">
                    <text class="tab-text">{{ tab.name }}</text>
                </view>
            </view>
            <view class="tab-indicator" :style="{ left: indicatorLeft }"></view>
        </view>

        <!-- 内容区域 -->
        <scroll-view class="content-container" scroll-y>
            <!-- 面试列表 -->
            <view v-if="currentList.length > 0" class="interview-list">
                <view v-for="(item, index) in currentList" :key="item.id" class="interview-item" @click="toJobInfo(item)">
                    <!-- 公司图标 -->
                    <view class="company-icon">
                        <image class="icon-img" :src="item.companyLogo || '/static/default-company.png'"
                            mode="aspectFill"></image>
                    </view>

                    <!-- 面试信息 -->
                    <view class="interview-info">
                        <view class="job-title" v-if="item.job_info&&item.job_info.company_name">{{ item.job_info.company_name }}
                        </view>
                        <view class="job-details">{{ item.job_info&&item.job_info.name }} {{ item.job_info&&item.job_info.min_salary }}-{{
                            item.job_info&&item.job_info.max_salary }} </view>
                    </view>
                    <!-- 状态和时间 -->
                    <view class="interview-status">
                        <view class="status-tag" :class="getStatusClass(item.checkin_status)">
                            <text class="status-text">{{ getStatusText(item.checkin_status) }}</text>
                        </view>
                        <view class="interview-time">{{ item.interview_time }}</view>
                    </view>
                </view>
            </view>

            <!-- 空状态 -->
            <view v-else class="empty-state">
                <view class="empty-icon">
                    <u-icon name="calendar" size="80" color="#E5E5E5"></u-icon>
                </view>
                <text class="empty-text">暂无面试记录</text>
                <text class="empty-desc">{{ currentTab === 0 ? '您还没有未面试的记录' : '您还没有已过期的面试记录' }}</text>
            </view>
        </scroll-view>
    </view>
</template>

<script>
import { interview } from "@/utils/api"
export default {
    data() {
        return {
            currentTab: 0,
            tabs: [
                { name: '未面试', key: 'pending' },
                { name: '已过期', key: 'expired' }
            ],
            // 未面试列表
            pendingList: [
                {
                    id: 1,
                    jobTitle: 'xxx传媒',
                    salary: '面谈·4-6k',
                    location: 'xx广场',
                    status: 'pending',
                    time: '10:00',
                    companyLogo: '/static/company-logo1.png'
                },
                {
                    id: 2,
                    jobTitle: 'xxx传媒',
                    salary: '面谈·4-6k',
                    location: 'xx广场',
                    status: 'pending',
                    time: '10:00',
                    companyLogo: '/static/company-logo2.png'
                },
                {
                    id: 3,
                    jobTitle: 'xxx传媒',
                    salary: '面谈·4-6k',
                    location: 'xx广场',
                    status: 'pending',
                    time: '10:00',
                    companyLogo: '/static/company-logo3.png'
                },
                {
                    id: 4,
                    jobTitle: 'xxx传媒',
                    salary: '面谈·4-6k',
                    location: 'xx广场',
                    status: 'pending',
                    time: '10:00',
                    companyLogo: '/static/company-logo4.png'
                }
            ],
            // 已过期列表
            expiredList: [
                {
                    id: 5,
                    jobTitle: 'xxx传媒',
                    salary: '面谈·4-6k',
                    location: 'xx广场',
                    status: 'expired',
                    time: '10:00',
                    companyLogo: '/static/company-logo1.png'
                },
                {
                    id: 6,
                    jobTitle: 'xxx传媒',
                    salary: '面谈·4-6k',
                    location: 'xx广场',
                    status: 'expired',
                    time: '10:00',
                    companyLogo: '/static/company-logo2.png'
                },
                {
                    id: 7,
                    jobTitle: 'xxx传媒',
                    salary: '面谈·4-6k',
                    location: 'xx广场',
                    status: 'expired',
                    time: '10:00',
                    companyLogo: '/static/company-logo3.png'
                },
                {
                    id: 8,
                    jobTitle: 'xxx传媒',
                    salary: '面谈·4-6k',
                    location: 'xx广场',
                    status: 'expired',
                    time: '10:00',
                    companyLogo: '/static/company-logo4.png'
                }
            ],
            page: 1,
            size: 10
        }
    },
    computed: {
        // 当前显示的列表
        currentList() {
            return this.currentTab === 0 ? this.pendingList : this.expiredList
        },
        // Tab指示器位置
        indicatorLeft() {
            return this.currentTab * 50 + '%'
        }
    },
    onLoad() {
        this.switchTab(0)
    },
    methods: {
        //去面试详情页面
        toJobInfo(item){
            console.log('去面试详情页面:', item)
            uni.navigateTo({
                url: '/pages/user/usercontont/InterviewDetail?id='+item.id
            })
        },
        // 切换Tab
        async switchTab(index) {
            this.currentTab = index
            if (index === 1) {
                const params = {
                    page: this.page,
                    size: this.size,
                    type: 2
                }
                const response = await interview.interview(params)
                if (response.code == 200) {
                    console.log(response.data.data)
                    this.expiredList = response.data.data
                } else {
                    uni.showToast({
                        title: response.msg,
                        icon: 'none',
                    });
                }
                uni.hideLoading();
            } else {
                const params = {
                    page: this.page,
                    size: this.size,
                    type:1
                }
                const response = await interview.interview(params)
                if (response.code == 200) {
                    console.log(response.data.data)
                    this.pendingList = response.data.data
                } else {
                    uni.showToast({
                        title: response.msg,
                        icon: 'none',
                    });
                }
                uni.hideLoading();
            }
        },

        // 获取状态样式类
        getStatusClass(status) {
            switch (status) {
                case 0:
                    return 'status-pending'
                case 1:
                    return 'status-expired'
                default:
                    return ''
            }
        },

        // 获取状态文本
        getStatusText(status) {
            switch (status) {
                case 0:
                    return '未面试'
                case 1:
                    return '已过期'
                default:
                    return ''
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.interview-list-page {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background-color: #fff;
}

/* Tab切换 */
.tab-container {
    position: relative;
    background: white;
    margin-top: 88rpx;
    padding: 20rpx 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
}

.tab-wrapper {
    display: flex;
    align-items: center;
}

.tab-item {
    flex: 1;
    text-align: center;
    padding: 20rpx 0;
    position: relative;
}

.tab-text {
    font-size: 32rpx;
    color: #666;
    transition: color 0.3s ease;
}

.tab-item.active .tab-text {
    color: #14B19E;
    font-weight: 600;
}

.tab-indicator {
    position: absolute;
    bottom: 0;
    width: 50%;
    height: 4rpx;
    background: linear-gradient(90deg, #14B19E 0%, #0FA085 100%);
    border-radius: 2rpx;
    transition: left 0.3s ease;
}

/* 内容容器 */
.content-container {
    flex: 1;
    padding: 30rpx;
    box-sizing: border-box;
}

/* 面试列表 */
.interview-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.interview-item {
    display: flex;
    align-items: center;
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    border-left: 6rpx solid #14B19E;
}

.company-icon {
    width: 80rpx;
    height: 80rpx;
    margin-right: 24rpx;
    border-radius: 12rpx;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-img {
    width: 100%;
    height: 100%;
    border-radius: 12rpx;
}

.interview-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.job-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    line-height: 1.2;
}

.job-details {
    font-size: 26rpx;
    color: #666;
    line-height: 1.2;
}

.interview-status {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8rpx;
}

.status-tag {
    padding: 8rpx 16rpx;
    border-radius: 20rpx;
    font-size: 24rpx;
}

.status-pending {
    background: rgba(20, 177, 158, 0.1);
    color: #14B19E;
}

.status-expired {
    background: rgba(255, 71, 87, 0.1);
    color: #FF4757;
}

.status-text {
    font-size: 24rpx;
    font-weight: 500;
}

.interview-time {
    font-size: 24rpx;
    color: #999;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 60rpx;
    text-align: center;
}

.empty-icon {
    margin-bottom: 40rpx;
    opacity: 0.6;
}

.empty-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.empty-desc {
    font-size: 28rpx;
    color: #999;
    line-height: 1.4;
}
</style>