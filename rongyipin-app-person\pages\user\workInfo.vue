<template>
	<view class="position-container">
		<!-- 顶部导航 -->
        <!-- <u-popup  :show="positionHopeFlag" mode="right" class="add-position-hope"> -->
            <u-navbar @leftClick="handleBack" title="" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
            <!-- <u-icon name="arrow-left" size="40" class="back-icon" @click="closehope"></u-icon>  -->
            <!-- <div class="home-container" style='width:100%'>
                wetwqyuet
            </div> -->
            <view class="zw-container">
                <div class="zw-text"> {{id?'编辑':'添加'}}求职期望？</div>
            </view>
            <view class="zw-tip">完善求职期望获得更多机会</view>
            <view class="zw-list">
                <view class="zw-item-content claas-pad">
                    <view>求职类型</view>
                    <view>
                        <u-radio-group 
                            v-model="zwtype"
                            activeColor="#06E698" 
                            iconColor="#06E698"  
                            inactiveColor="#dddddd"
                            iconPlacement="left"
                            @change="changeZwtype"
                            >
                            <u-radio 
                            style="margin-right: 10px;"
                            size="25" 
                            labelSize="20"
                                label="全职"
                                name='0'
                            ></u-radio>
                            <u-radio size="25" 
                            labelSize="20" label="兼职" name='1'></u-radio>
                        </u-radio-group>
                    </view>
                </view>
                <view class="zw-item-content claas-pad" @click="hopecity">
                    <div>工作城市</div>
                    <div class="right-content">{{ wrokCity.name }} <u-icon name="arrow-right"></u-icon></div>
                </view>
                <u-popup :show="cityHopeShow" mode="right" @close="closescity">
                    <view>
                        <div class="city-title">
                            <u-icon name="arrow-left" size="30" @click="closescity"></u-icon>
                        </div>
                        <ChoseCity ref="searchscity" @citydatafunc="citydatafunc"></ChoseCity>
                    
                    </view>
                </u-popup>
                <view class="zw-item-content claas-pad" @click="hopeposition">
                    <div>期望职位</div>
                    <div class="right-content">{{ wrokPosition.name }} <u-icon name="arrow-right"></u-icon></div>
                </view>
                <u-popup :show="positionHopeShow" mode="right" @close="closesposition">
                    <view>
                        <div class="city-title">
                            <u-icon name="arrow-left" size="30" @click="closesposition"></u-icon>
                        </div>
                        <Positionhope ref="searchsPosition" @positiondatafunc="positiondatafunc"></Positionhope>
                    
                    </view>
                </u-popup>
                <view class="zw-item-content claas-pad" @click="moneyShowfunc">
                    <div>薪资要求</div>
                    <div class="right-content">{{ moneyhope }} <u-icon name="arrow-right"></u-icon></div>
                </view>
                <view class="zw-item-content claas-pad" @click="skillfunc">
                    <div>您具备哪些技能</div>
                    <div class="right-content">
                        <div class="skill">
                            <view v-for="(item, index) in skills" :key="index" class="skill-item">
                                {{ item.name }}
                            </view>
                        </div>
                        <u-icon name="arrow-right"></u-icon>
                    </div>
                </view>
                <u-popup :show="skillshow" mode="right" @close="closeskill">
                    <view>
                        <div class="city-title">
                            <u-icon name="arrow-left" size="30" @click="closeskill"></u-icon>
                        </div>
                        <Skill ref="searchskill" @skilldatafunc="skilldatafunc"></Skill>
                    
                    </view>
                </u-popup>
                <view class="zw-item-content claas-pad" @click="industryfunc">
                    <div>期望行业</div>
                    <div class="right-content">{{ industry }} <u-icon name="arrow-right"></u-icon></div>
                    <!-- <div class="right-content">
                        <div class="skill" v-if="industry.length>0">
                            <view v-for="(item, index) in industry" :key="index" class="skill-item">
                                {{ item.name }}
                            </view>
                        </div>
                        <div v-else>不限</div>
                        <u-icon name="arrow-right"></u-icon>
                    </div> -->
                </view>
                <u-popup :show="industryshow" mode="right" @close="closeindustry">
                    <view>
                        <div class="city-title">
                            <u-icon name="arrow-left" size="30" @click="closeindustry"></u-icon>
                        </div>
                        <Industry ref="searchindustry" @industrydatafunc="industrydatafunc"></Industry>
                    
                    </view>
                </u-popup>
               
                
            </view>
            <div class="save-btn">
                <u-button  
                v-show="id"
                color="#ddd"
                text="删除" size="small" @click="delete_btn"></u-button>
                <u-button type="primary" 
                color="#02bdc4"
                text="确定" size="small"  @click="savePosition"></u-button>
            </div>
            
            <!-- <u-picker :show="moneyShow" :columns="columns"
                confirmText="确认"
                confirmColor="#02bdc4"
                itemHeight="100"
                @cancel="moneyclose"
                @confirm="moneyconfirm"
            ></u-picker> -->
            <u-picker 
            :show="moneyShow" 
            :columns="salaryColumns"
            :defaultIndex="defaultIndex"
            confirmText="确认"
            confirmColor="#02bdc4"
            itemHeight="100"
             @cancel="moneyclose"
            @confirm="moneyconfirm"
            @change="onSalaryChange"
           ></u-picker>
            
        <!-- </u-popup> -->

        

	</view>
</template>

<script>
// import { onLoad } from 'uview-ui/libs/mixin/mixin'
import {addExpect,jobApi } from '@/utils/api'
import Industry from '../index/industry.vue'
import Skill from '../index/skill.vue'
import Positionhope from '../index/positionhope.vue'
import ChoseCity from '../index/choseCity.vue'
// import registration from "./registration/index.vue"
export default {
   
	data() {
		return {
            industryshow:false,//行业选择框flag
            skillshow:false,//技能选择框flag
            positionHopeShow:false,//职位期望选择框flag
            cityHopeShow:false,//城市期望选择框flag
			// positionHopeFlag:false,
            zwtype:'0',
            moneyShow:false,//薪资选择
            defaultIndex:[0,0],
            salaryColumns: [
                this.generateBaseSalaries(), // 第一列：基础薪资
                [] // 第二列：动态生成
            ],
            selectedMinSalaryIndex:0,
             salaryRange: '' ,
            wrokCity:{},
            wrokPosition:{},
            moneyhope:'',
            skills:[],//技能
            industry:'',//行业，
            industryids:'',//行业id
            industrydata:{},//行业数据
            id:'',
            item:{},
            deleteflag:true,
            work:'',
		}
	},
	components: {
		Industry,
        Skill,
        Positionhope,
        ChoseCity
	},

	onLoad(options){
        console.log(options.id,'guygyg')
        if(options.id){
            this.id=options.id;
            this.joblist(options.id);
        }
        this.deleteflag=options.del;
        console.log(this.deleteflag,'deleteflag');
        this.work=options.work?options.work:'';
    },
//     mounted() {
//   if (this.moneyhope) {
//     const [min] = this.moneyhope.split('-');
//     const newMax = this.generateMaxSalaries(min);
//     this.$set(this.salaryColumns, 1, newMax);
//   }
// },
    computed: {
    defaultSalaryIndex() {
        if (!this.moneyhope || this.salaryColumns[0].length === 0) return [0, 0];

        const [min, max] = this.moneyhope.split('-').map(Number);

        const minIndex = this.salaryColumns[0].indexOf(`${min}`);
        let maxIndex = -1;

        if (this.salaryColumns[1].length > 0) {
            maxIndex = this.salaryColumns[1].indexOf(`${max}`);
        }

        return minIndex !== -1 ? [minIndex, maxIndex !== -1 ? maxIndex : 0] : [0, 0];
        }
    },
	methods: {
        //薪资回显
        huuixian(){
            if (this.moneyhope && this.salaryColumns[0].length > 0) {
                const [min] = this.moneyhope.split('-');
                console.log(min,'最小值');
                const minIndex = this.salaryColumns[0].indexOf(min);
                
                if (minIndex !== -1) {
                const newMax = this.generateMaxSalaries(min);
                console.log(newMax,'最大值');
                this.$set(this.salaryColumns, 1, newMax);
                
                }
            }
        },
        //
        changeZwtype(val){
            this.item.type=val;
        },
        // 职位详情
        async joblist(id){
            await jobApi.getUserResumeList({id:id}).then(res=>{ 
                console.log(res,"joblist");
                uni.hideLoading();
                if(res.code==200){ 
                     this.wrokCity={
                        name:res.data.city_classname,
                        id:res.data.city_classid
                    };
                    this.wrokPosition={
                        name:res.data.job_classname,
                        id:res.data.job_classid
                    };
                    this.industry=res.data.hy_name;
                    this.industryids=res.data.hy_id;
                    this.skills=res.data.skillsMap;
                    this.moneyhope=res.data.minsalary+'-'+res.data.maxsalary;
                    this.zwtype=res.data.type.toString();
                    // this.huuixian()


                }else{
                    uni.showToast({
                        title:res.msg,
                        icon:'none'
                    });
                }
                
                //  this.item=res.data;
                 console.log("看看成功了吗", this.zwtype);
                //  job_classid: this.wrokPosition.id,
                //     // minsalary: this.moneyhope.split('-')[0],
                //     // maxsalary: this.moneyhope.split('-')[1],
                //     city_classid: this.wrokCity.id,
                //     type:this.zwtype,
                //     hy_id:hy_ids.join(','),
                //     skill_ids:skill_ids.join(','),
            })
        },
       closehope(){
            this.positionHopeFlag=false;
        },
        moneyShowfunc(){
            const [min,max] = this.moneyhope.split('-');
            console.log(min,max,'dashdsahu')
            const newMax = this.generateMaxSalaries(min);
            const newMin = this.generateBaseSalaries();
            const maxindex=newMax.indexOf(max);
            const minindex=newMin.indexOf(min);
            console.log(maxindex,minindex,'index')
            this.$set(this.salaryColumns, 1, newMax);
            this.defaultIndex=[minindex,maxindex];
            this.moneyShow=true;

           
            
        },
        generateBaseSalaries() {
            const salaries = [];
            for (let i = 1; i <= 50; i++) {
                salaries.push(`${i * 1000}`);
            }
            return salaries;
        },

        generateMaxSalaries(minValue) {
            const min = parseInt(minValue);
            const maxList = [];

            for (let i = 1; i <= 5; i++) {
                const step = 1000 * i;
                if (min + step <= 50000) {
                maxList.push(`${min + step}`);
                }
            }

            return maxList;
        },
         onSalaryChange(e) {
            // console.log(e,'highduigdhg');
            const { columnIndex, value, index } = e;
            // console.log(columnIndex, value, index);

            if (columnIndex === 0) {
                // 用户选择了第一列（最小薪资）
                const minSalary = value[0];
                const newMaxSalaries = this.generateMaxSalaries(minSalary);
                //  console.log('第一列选中:', minSalary);
                // console.log('第二列生成:', newMaxSalaries);

                // ✅ 使用 $set 强制更新第二列
                this.$set(this.salaryColumns, 1, newMaxSalaries);
                this.selectedMinSalaryIndex = index[0];
               
            }
        },
        moneyconfirm(e) {
             const [min, max] = e.value;
            this.moneyhope = `${min}-${max}`;
            this.moneyShow = false;
                        // console.log('confirm', e)
                // this.moneyhope=e.value[0];
                // this.moneyShow = false
		},
        moneyclose(){
            this.moneyShow = false;
            
        },
         // 生成薪资选项
        generateSalaryOptions() {
            const options = [];
            for (let i = 1; i <= 50; i++) { // 生成 1K 到 50K 的选项
                options.push(`${i}K`);
            }
            return options;
        },

        // 初始化薪资选择器
        initSalaryPicker() {
            const options = this.generateSalaryOptions();
            this.salaryColumns = [options, options];
        },
        // 城市选择
        hopecity(){
            this.cityHopeShow=true;
             this.$nextTick(() => {
                 this.$refs.searchscity.getCurrentCity();
            })
            // console.log("cccccc")
            // uni.navigateTo({
			// 	url:"./choseCity"
			// })
        },
        // 城市选择关闭
        closescity(){
            this.cityHopeShow=false;
        },
        // 城市选择data
        citydatafunc(data){
            this.cityHopeShow=false;
            this.wrokCity=data;
        },
        // 职位
        hopeposition(){
            this.positionHopeShow=true;
            this.$nextTick(() => {
                 this.$refs.searchsPosition.getJobCategories();
            })
            //  uni.navigateTo({
			// 	url:"./positionhope"
            //     // url:"./ai"
			// })
        },
        // 职位选择关闭
         closesposition(){
            this.positionHopeShow=false;
        },
        // 职位data
        positiondatafunc(data){
            this.positionHopeShow=false;
            this.wrokPosition=data;
            //职位改变技能清空
            this.skills=[];
        },
    
       
        handleBack(){ 
            if(this.work=='no'){
               uni.showToast({
                    title: '首次登陆必须填写一个求职意向',
                    icon: 'none'
                });
                return;
            }
            uni.navigateBack();
            //  uni.navigateTo({
			// 	url:"./addzhiwei"
            //     // url:"./ai"
			// })
        },
        // 技能
        skillfunc(){
            console.log("我 skillfunc",this.wrokPosition);
            if( !this.wrokPosition.id){
                uni.showToast({
                    title: '请选择先期望职位',
                    icon: 'none'
                });
                return;
            }else{
                this.skillshow=true;
                this.$nextTick(() => {
                    const searchskillRef = this.$refs.searchskill;
                    if (searchskillRef && searchskillRef.initDefaultCategory) {
                        this.$refs.searchskill.initDefaultCategory(this.wrokPosition.id);
                    } else {
                        console.error('searchskill 组件未正确加载');
                    }
                })
            }
        },
        closeskill(){
            this.skillshow=false;
        },
        // 技能data
        skilldatafunc(data){
            this.skillshow=false;
            this.skills=data;
        },
        // 行业选择
        industryfunc(){
            this.industryshow=true;
            this.$nextTick(() => {
                const searchindustryRef = this.$refs.searchindustry;
                if (searchindustryRef && searchindustryRef.initDefaultCategory) {
                    this.$refs.searchindustry.initDefaultCategory();
                } else {
                    console.error('Industry 组件未正确加载');
                }
            })
        },
        closeindustry(){
             this.industryshow=false;
        },
        //获取行业data
        industrydatafunc(data){ 
            console.log('获取行业data',data);
            this.industrydata = data;
            this.industryshow=false;
             const hy_ids=[];
                this.industrydata.map(item=>{
                    hy_ids.push(item.name)
                })
            this.industry=hy_ids.join(',');
        },
        //保存求职期望
        async savePosition(){
            
            
            if(this.wrokPosition.id==undefined){ 
                 uni.showToast({
                        title: '期望职位不能为空',
                        icon: 'none'
                    });
                    return;
            }
            if(this.wrokCity.id==undefined){ 
                 uni.showToast({
                        title: '工作城市不能为空',
                        icon: 'none'
                    });
                    return;
            }
            if(this.moneyhope.split('-')[0]==undefined||this.moneyhope.split('-')[1]==undefined){ 
                 uni.showToast({
                        title: '薪资不能为空',
                        icon: 'none'
                    });
                    return;
            }

            const skillarr=this.skills;
            const skill_ids=[];
            skillarr.map(item=>{
                skill_ids.push(item.id)
            })
            
            
            let hy_ids=[];
            if(this.industrydata.length>0){
                const hyarr=this.industrydata;
                hyarr.map(item=>{
                    hy_ids.push(item.id)
                })
                this.industryids=hy_ids.join(',');
            }
            
            
            const params={
                job_classid: this.wrokPosition.id,
                minsalary: this.moneyhope.split('-')[0],
                maxsalary: this.moneyhope.split('-')[1],
                city_classid: this.wrokCity.id,
                type:this.zwtype,
                hy_id:this.industryids,
                skill_ids:skill_ids.join(','),
            }
            if(this.id==''){
                await jobApi.addExpect({...params}).then(res => {
                    console.log(res, 'city');
                    uni.hideLoading();
                    // uni.removeStorageSync('hopecity','');
                    // uni.removeStorageSync('industry','');
                    // uni.removeStorageSync('skills','');
                    // uni.removeStorageSync('hopejob','');
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                    if(res.code==200){ 
                    //   uni.navigateBack();
                        uni.reLaunch({
                            url: `/pages/index/addzhiwei?city=${this.wrokCity}&position=${this.wrokPosition}&money=${this.moneyhope}`
                            // url:./addzhiwei"
                            // url:"./ai"
                        })
                    }
                    
                })
            }else{
                // delete this.item.skillsMap;
                // delete this.item.city_classname;
                // delete this.item.hy_name;
                // delete this.item.job_classname;
                // delete this.item.type_text;
                console.log(params,'shuju')
                await jobApi.updateExpect({...params,id:this.id}).then(res => {
                    console.log(res, 'city');
                    // uni.removeStorageSync('hopecity','');
                    // uni.removeStorageSync('industry','');
                    // uni.removeStorageSync('skills','');
                    // uni.removeStorageSync('hopejob','');
                    // uni.showToast({
                    //     title: res.msg,
                    //     icon: 'none'
                    // });
                    // this.currentCity = res.result.ad_info.city
                    uni.hideLoading();
                    // this.surrounding(res.result.location.lat, res.result.location.lng)
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    });
                    if(res.code==200){ 
                        uni.navigateBack();
                        // uni.navigateTo({
                        //     url: `./addzhiwei`
                        //     // url:./addzhiwei"
                        //     // url:"./ai"
                        // })
                    }
                })
            }
           

           
        },
        //
        delete_btn(){
            console.log("删除",this.id,this.deleteflag);
            const id=this.id;
            if(this.deleteflag){
                uni.showModal({
                    content: '确定要删除吗？',
                    success: function (res) {
                        if (res.confirm) {
                            jobApi.UserResumeDelete({id:id}).then(ress=>{ 
                                uni.hideLoading();
                                uni.showToast({
                                    title: ress.msg,
                                    icon: 'none'
                                });
                                uni.navigateBack();
                                // uni.navigateTo({
                                //     url: `./addzhiwei`
                                //     // url:./addzhiwei"
                                //     // url:"./ai"
                                // })

                            })
                        } else if (res.cancel) {
                            console.log('用户点击取消')
                        }
                    }
                })
            }else{
                uni.showToast({
                    title: '求职期望不能少于一个',
                    icon: 'none'
                });
            }
            
        }
	}
}
</script>

<style lang="scss" scoped>
::v-deep .u-slide-right-enter-active{
    width: 100%;
}
.city-title{
    padding: 40rpx;
}
.back-icon{
    margin: 10px 10px;
}
.zw-container{
    padding:10px 20px;
    display: flex;
    justify-content: space-between;
    font-weight: 700;
    
    
}
.claas-pad{
    height: 55px;
    line-height: 55px;

}
.zw-tip{
        font-size: 12px;
        margin:2px 20px;
}
.zw-list{
    margin: 20px;
    background-color: #f5f5f5;
    border-radius: 5px;
    padding: 10px;
}
.zw-item{
    font-weight: 700;
    font-size: 15px;

}
 .zw-item-content{
    font-weight: 700;
    font-size: 15px;
        display: flex;
        justify-content: space-between;
    }
    .yuan{
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #02bdc4;
        margin-right: 5px;
    }
    .zw-item-desc{
        padding:10px 0;
        color: #a1a1a1;
        font-size: 12px;
    }
    .zw-item-line{
        width: 100%;
        height: 1px;
        background-color: #ddd;
        margin: 10px 0;
    }
    .right-content{
        display: flex;
        align-items: center;
        font-size: 12px;
        color: #a1a1a1;
    }
.zw-add{
        width: 130px;
        margin: 0 auto;
        background-color: #ffffff;
        border-radius: 20px;
        padding: 13px 20px;
        display: flex;
        font-size:13px;
        color: #02bdc4;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        font-weight: 700;
        .add-text{
            margin-left: 10px;
        }
    }
    .add-position-hope{
        ::v-deep .u-fade-enter-active{
            z-index: 10075!important;
        }
        
    }
    .save-btn{
        display: flex;
            width: 100%;
            text-align: center;
            margin: 0 auto;
        }
    .skill{
        max-width: 384rpx;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }
    .skill-item{
        display: inline-block;
        font-size: 12px;
        color: #a1a1a1;
        margin-right: 10px;
        
    }

    
</style>  