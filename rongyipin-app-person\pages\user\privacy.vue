<template>

  <view class="userinfo-container">
    <u-navbar :autoBack="true" title="容翼聘隐私政策协议" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <view class="privacy" v-html="privacy">  </view>


    
   
  </view>

</template>
<script>
import { userApi } from '@/utils/api.js'
export default {
  data() {
    return {
      privacy: ''
    }
  },
  onLoad() {
    this.privacyData()
  },
  methods: {
    privacyData() {
      userApi.getSysConfig().then(res => {
        uni.hideLoading()
        this.privacy=res.data.privacy_content;
      })
    }
  }
   
   
};
</script>
<style lang="scss" scoped>
.privacy{
    padding: 0 40rpx 20rpx;
}
</style>
