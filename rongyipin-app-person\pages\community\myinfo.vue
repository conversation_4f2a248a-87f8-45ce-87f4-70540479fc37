<template>
  <view class="container">
    <u-navbar :autoBack="true" title="个人信息" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <!-- 页面标题 -->
    <!-- <view class="header">
      <text class="title">个人信息</text>
    </view> -->
    
    <!-- 表单区域 -->
    <view class="form-container">
      <!-- 头像上传 -->
      <view class="form-item">
        <text class="label">头像</text>
        <view class="avatar-upload" @click="chooseAvatar">
          <u-avatar :src="form.pic" size="80"></u-avatar>
          <u-icon name="arrow-right" color="#999" size="28"></u-icon>
        </view>
      </view>
      
      <!-- 姓名 -->
      <view class="form-item" @click="showName">
        <view>
            <text class="label">姓名</text>
            <view class="name">{{form.username}}</view>
        </view>
        
        <!-- <u-input v-model="form.username" placeholder="请输入姓名" border="none" input-align="right"></u-input> -->
         <u-icon name="arrow-right" color="#999" size="28"></u-icon>
      </view>
      
      <!-- 简介 -->
      <view class="form-item" @click="introduce">
        <view>
            <text class="label">简介</text>
            <view class="name">{{form.introduction==''?'添加简介':form.introduction}}</view>
        </view>
        
        <!-- <u-input v-model="form.username" placeholder="请输入姓名" border="none" input-align="right"></u-input> -->
         <u-icon name="arrow-right" color="#999" size="28"></u-icon>
        <!-- <text class="label"></text>
        <u-input v-model="form.introduction" placeholder="添加简介" border="none" input-align="right"></u-input> -->
      </view>
      
      <!-- 账号类型 -->
      <view class="form-item">
         <view>
            <text class="label">账号类型</text>
            <view class="name">{{form.type==0?'个人':'企业'}}</view>
        </view>
        <!-- <text class="label">账号类型</text>
        <text class="value">个人</text> -->
      </view>
    </view>
    <!-- 修改名字 -->
     <u-popup :show="nameShow" mode="right" @close="closesname">
        <view>
            <div class="pages-title">
                <u-icon name="arrow-left" size="30" @click="closesname"></u-icon>
            </div>
            <myName ref="myName" @newname="newname"></myName>
        </view>
    </u-popup>
    <!-- 修改简介 -->
     <u-popup :show="introduceShow" mode="right" @close="closesintroduce">
        <view>
            <div class="pages-title">
                <u-icon name="arrow-left" size="30" @click="closesintroduce"></u-icon>
            </div>
            <myIntroduce ref="myintroduce" @newintroduce="newintroduce"></myIntroduce>
        </view>
    </u-popup>
    <!-- 保存按钮 -->
    <view class="footer">
      <u-button color="#059f9f"  type="primary" @click="saveInfo">保存</u-button>
    </view>
  </view>
</template>

<script>
import myName from './myname.vue'
import myIntroduce from './myintroduce.vue'
import {communityApi } from '@/utils/api'
import { onLoad } from 'uview-ui/libs/mixin/mixin'
export default {
    components: { myName,myIntroduce },
   
  data() {
    return {
        nameShow: false,
        introduceShow: false,
      form: {
        avatar: '/static/default-avatar.png', // 默认头像
        name: '张xx',
        bio: '',
        accountType: '个人'
      }
    }
  },
  onLoad() {
    this.getUserInfo()
  },
  methods: {
    async getUserInfo(){
       await communityApi.user().then(res => {
        uni.hideLoading();
			this.form = res.data
		})
    },
    // 关闭介绍
    closesintroduce(){
        this.introduceShow=false;
    },
        // 新增介绍
    newintroduce(val){
         this.form.introduction=val;
        this.introduceShow=false;
    },
    // 显示介绍
    introduce(){
        this.introduceShow=true;
        this.$nextTick(()=>{
            // console.log('this.form.username',this.form.username);
            this.$refs.myintroduce.initbio(this.form.introduction);
        })
    },
    // 新增昵称
    newname(val){
        console.log(val,'val')
        this.form.username=val;
        this.nameShow=false;
    },

    // 显示昵称
    showName(){
        this.nameShow=true;
        this.$nextTick(()=>{
            console.log('this.form.username',this.form.username);
            this.$refs.myName.initname(this.form.username);
        })
        
        // uni.navigateTo({
        //   url: '/pages/community/myname'
        // })
    },
    // 关闭昵称
    closesname(){
        this.nameShow=false;
    },
    // 选择头像
    chooseAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.form.pic = res.tempFilePaths[0];
          // 这里可以添加上传逻辑
        }
      });
    },
    // 保存信息
    async saveInfo() {
      if (!this.form.username) {
        uni.showToast({
          title: '请输入姓名',
          icon: 'none'
        });
        return;
      }
      console.log(this.form,'保存信息');
      await communityApi.usersave(this.form).then(res => {
        uni.hideLoading();
        if (res.code == 200) {
          uni.showToast({
            title: '保存成功',
            icon: 'none'
          });
          //导航后退
          uni.navigateBack();
        //   uni.navigateTo({
        //     url:'/pages/community/mypage'
        //   })
        }
      }).catch(err => {
        console.error("保存用户信息失败:", err)
        uni.showToast({
          title: '保存用户信息失败',
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .u-slide-right-enter-active{
    width: 100%;
}
.container {
    width: 100%;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  padding: 30rpx;
  text-align: center;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
  }
}

.form-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 0 30rpx;
  
  .form-item {
    display: flex;
    // align-items: center;
    justify-content: space-between;
    padding: 30rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
    .name{
        color:#999
    }
    &:last-child {
      border-bottom: none;
    }
    
    .label {
      font-size: 30rpx;
      color: #333;
    }
    
    .value {
      font-size: 30rpx;
      color: #999;
    }
    
    .avatar-upload {
      display: flex;
      align-items: center;
    }
    
    ::v-deep  .u-input {
      flex: 1;
      text-align: right;
    }
  }
}

.footer {
  padding: 40rpx 30rpx;
  
  ::v-deep  .u-button {
    width: 100%;
  }
}
</style>