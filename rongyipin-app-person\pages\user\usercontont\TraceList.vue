<template>
    <view class="interview-list-page">
        <!-- 顶部导航栏 -->
        <!-- <view class="navbar"> -->
             <u-navbar :autoBack="true" title="我看过的记录" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
            <!-- <u-navbar height="44px" title="我看过的记录" :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'"
                safeAreaInsetTop placeholder fixed>
            </u-navbar> -->
        <!-- </view> -->

        <!-- Tab切换 -->
        <!-- <view class="tab-container">
            <view class="tab-wrapper">
                <view v-for="(tab, index) in tabs" :key="index" class="tab-item"
                    :class="{ active: currentTab === index }" @click="switchTab(index)">
                    <text class="tab-text">{{ tab.name }}</text>
                </view>
            </view>
            <view class="tab-indicator" :style="{ left: indicatorLeft }"></view>
        </view> -->

        <!-- 内容区域 -->
        <view class="job-list-container">
                <scroll-view class="list-container" scroll-y="true" refresher-enabled="true"
                    :refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
                    :lower-threshold="50" v-if="currentList.length > 0">
                    <!-- 职位列表 -->
                    <view class="job-item" v-for="(job, index) in currentList" :key="index" @click="goDetail(job)">
                        <!-- 职位名称和薪资 -->
                        <view class="job-header">
                            <view class="job-title">{{ job.name }}</view>
                            <view class="job-salary">{{ job.min_salary }}-{{ job.max_salary }}</view>
                        </view>

                        <!-- 公司信息 -->
                        <view class="company-info">
                            <text>{{ job.company_name }}</text>
                            <text>{{ job.companySize }}</text>
                            <text>{{ job.companyAddress }}</text>
                        </view>

                        <!-- 工作要求 -->
                        <view class="job-requirements">
                            <text>{{ job.experience }}</text>
                            <text>{{ job.education }}</text>
                            <text>{{ job.workContent }}</text>
                        </view>

                        <!-- HR 信息 -->
                        <view class="hr-info">
                            <u-avatar :src="job.hrAvatar" size="40"></u-avatar>
                            <text>{{ job.username }}</text>
                            <text>{{ job.hrPosition }}</text>

                        </view>

                       
                    </view>
                    <!-- 加载状态提示 -->
                    <view class="loading-status">
                        <view v-if="refreshing && currentList.length > 0" class="loading-more">
                            <u-loading-icon mode="spinner" color="#02bdc4" size="32"></u-loading-icon>
                            <text class="loading-text">加载中...</text>
                        </view>
                        <view v-else-if="noMore && currentList.length > 0" class="no-more">
                            <text>- 没有更多了 -</text>
                        </view>
                    </view>
                </scroll-view>
                <view v-else class="empty-state">
                    <view class="empty-icon">
                        <u-icon name="calendar" size="80" color="#E5E5E5"></u-icon>
                    </view>
                    <text class="empty-text">暂无看过记录</text>
                    <text class="empty-desc">您还没有看过的记录</text>
                </view>
        </view>
    </view>
</template>

<script>
import { interview,userApi } from "@/utils/api"
export default {
    data() {
        return {
            currentTab: 0,
            // tabs: [
            //     { name: '未面试', key: 'pending' },
            //     { name: '已过期', key: 'expired' }
            // ],
            // 未面试列表
            // pendingList: [
                
            // ],
            // // 已过期列表
            // expiredList: [
            // ],
            currentList:[],
            page: 1,
            noMore: false,
            // size: 10
        }
    },
    computed: {
        // 当前显示的列表
        // currentList() {
        //     return this.currentTab === 0 ? this.pendingList : this.expiredList
        // },
        // Tab指示器位置
        indicatorLeft() {
            return this.currentTab * 50 + '%'
        }
    },
    onLoad() {
        this.datalist();
        // this.switchTab(0)
    },
    methods: {
        //数据
        datalist(){
            console.log('获取kanguo列表')
          
            const params = {
                page: this.page,
                type: 1
            }
            userApi.views(params).then(res => {
                uni.hideLoading();
                this.refreshing = false;
                if (res.code == 200) {
                    if(res.data.list.length > 0){
                         this.currentList =[...this.currentList,...res.data.list] ;
                    }else{
                      this.noMore=true; 
                      this.page--; 

                    }
                   
                    // console.log(response.data.data)

                }
            })
        },
        onLoadMore() {
            //  console.log('触底了',this.page,this.job_active)
            // console.log("onLoadMore上拉加载更多 ");
            if(!this.noMore){
                this.page += 1;
                this.datalist();
            }
            

        },

        onRefresh() {
            // this.refreshing = true;
            this.page = 1;
            this.refreshing = true;
            this.datalist();
            //    this.refreshing = false;
            // console.log("onLoadMore下拉加载更多 ");
        },
        //去面试详情页面
        toJobInfo(item){
            console.log('去面试详情页面:', item)
            uni.navigateTo({
                url: '/pages/user/usercontont/InterviewDetail?id='+item.id
            })
        },
         goDetail(item) {
            uni.navigateTo({
                    url: '/pages/index/jobinfo?id=' + item.id
                })
            
        },
        getStatusClass(status) {
            switch (status) {
                case 0:
                    return 'status-pending'
                case 1:
                    return 'status-expired'
                default:
                    return ''
            }
        },

        // 获取状态文本
        getStatusText(status) {
            switch (status) {
                case 0:
                    return '未面试'
                case 1:
                    return '已过期'
                default:
                    return ''
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.interview-list-page {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background-color: #fff;
}

/* Tab切换 */
.job-list-container {

    padding: 20rpx;
    background-color: #f9f9f9;

    .list-container {
        height: calc(100vh - 150rpx);
    }

    .loading-more,
    .no-more {
        text-align: center;
        color: #afafaf;
    }
}

.job-item {
    position: relative;
    background-color: #fff;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;

    .job-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .job-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
        }

        .job-salary {
            font-size: 28rpx;
            color: #e74c3c;
        }
    }

    .company-info,
    .job-requirements,
    .hr-info {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;

        text {
            font-size: 24rpx;
            color: #666;
            margin-right: 10rpx;
        }
    }

    .hr-info {
        margin-bottom: 20rpx;

        .u-avatar {
            margin-right: 10rpx;
        }
    }

    .action-button {
        text-align: right;
        position: absolute;
        right: 20px;
        bottom: 37px;

        view {
            font-size: 24rpx;
            background-color: #14B19E;
            width: 100rpx;
            height: 50rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20rpx;
            color: white;
        }
    }
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 60rpx;
    text-align: center;
}

.empty-icon {
    margin-bottom: 40rpx;
    opacity: 0.6;
}

.empty-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.empty-desc {
    font-size: 28rpx;
    color: #999;
    line-height: 1.4;
}
</style>