<template>
	<view class="message-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="navbar-title"></view>
				<view class="navbar-title">聊天</view>
				<view class="navbar-right" @click="openSettings">
					<!-- <image class="settings-btn" src="/static/setting.png" @click="openSettings" /> -->
					设置
				</view>
			</view>
		</view>

		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<view class="search-icon">🔍</view>
				<input class="search-input" type="text" placeholder="搜索联系人、公司、聊天记录" v-model="searchKeyword"
					@input="onSearchInput" />
			</view>
		</view>

		<!-- Tab切换 -->
		<view class="tab-container">
			<view class="tab-item" v-for="(tab, index) in tabList" :key="index"
				:class="{ active: currentTab === index }" @click="switchTab(index)">
				{{ tab }}
				<!-- 只在全部Tab右侧显示筛选按钮 -->
				<template v-if="index === 0 && currentTab === 0">
					<!-- <image class="filter-btn" src="/static/filter.png" @click.stop="openFilter" /> -->
					<view style="margin-left: 10rpx;" @click.stop="show = true">
						<u-icon name="arrow-down" size="30"></u-icon>
					</view>
				</template>
			</view>
		</view>

		<!-- 消息列表区域 -->
		<scroll-view class="message-list-container" scroll-y="true" :style="{ height: scrollViewHeight + 'px' }"
			@scrolltoupper="onPullRefresh" :refresher-enabled="true" :refresher-triggered="refreshing"
			@refresherrefresh="onRefresh" @refresherrestore="onRefreshRestore">

			<!-- 只在全部Tab展示特殊消息项 -->
			<!-- <view v-if="currentTab === 0" class="special-section">
				<view class="special-message-list">
					<view class="special-message-item" @click="onSpecialClick('nearby')">
						<view class="special-icon-wrapper">
							<view class="special-icon thumbs-up">👍</view>
						</view>
						<view class="special-content">
							<view class="special-title">附近38个牛人在找工作</view>
							<view class="special-subtitle">点击查看详情</view>
						</view>
						<view class="special-arrow">›</view>
					</view>
					<view class="special-message-item" @click="onSpecialClick('interest')">
						<view class="special-icon-wrapper">
							<view class="special-icon heart">❤️</view>
						</view>
						<view class="special-content">
							<view class="special-title">王二等人对您感兴趣</view>
							<view class="special-subtitle">3人想和您聊聊</view>
						</view>
						<view class="special-arrow">›</view>
					</view>
					<view class="special-message-item" @click="onSpecialClick('viewed')">
						<view class="special-icon-wrapper">
							<view class="special-icon check">✔️</view>
						</view>
						<view class="special-content">
							<view class="special-title">张三等人看了您</view>
							<view class="special-subtitle">5人浏览了您的资料</view>
						</view>
						<view class="special-arrow">›</view>
					</view>
					<view class="special-message-item" @click="onSpecialClick('seen')">
						<view class="special-icon-wrapper">
							<view class="special-icon eye">👁️</view>
						</view>
						<view class="special-content">
							<view class="special-title">我看过</view>
							<view class="special-subtitle">最近浏览的候选人</view>
						</view>
						<view class="special-arrow">›</view>
					</view>
				</view>
			</view> -->

			<!-- 消息列表 -->
			<view class="message-list">
				<view class="message-item" v-for="item in messageList" :key="item.id" @click="openChat(item)">
					<view class="message-avatar">
						<image :src="item.avatarUrl" mode="aspectFill"></image>
						<!-- 消息状态角标 -->
						<view class="status-badge"
							v-if="item.status === 'sent' && item.last_message.from_type === 'recruiter'">
						</view>
					</view>
					<view class="message-content">
						<view class="message-header">
							<view class="user-info">
								<text class="user-name">{{ item.username }}</text>
								<text class="user-details">{{ item.age }}岁 {{ item.sex
									== 1 ?
									'男' : '女' }} </text>
							</view>
							<view class="message-time">{{ formatDate(item.last_message_time) }}</view>
						</view>
						<view class="message-preview" v-if="item.type === 'text'">{{ item.latest_message_content }}
						</view>
						<view class="message-preview" v-if="item.type === 'exchanged'">{{ item.latest_message_content }}
						</view>
						<view class="message-preview" v-else-if="item.type === 'image'">[图片]</view>
						<view class="message-preview" v-else-if="item.type === 'location'">[位置]</view>
						<view class="message-preview" v-else-if="item.type === 'invitation'">{{
							item.latest_message_content }}
						</view>
					</view>
					<view class="unread-indicator" v-if="item.latest_is_read === 0"></view>
				</view>
			</view>

			<!-- 底部提示 -->
			<view class="bottom-tip">
				<text>- 仅保留30天内消息 -</text>
			</view>
		</scroll-view>

		<!-- 设置弹窗 -->
		<view v-if="showSettingsPopup" class="popup-mask" @click.self="closeSettings">
			<view class="popup-content settings-popup">
				<view class="popup-header">
					<view class="popup-title">设置</view>
					<view class="popup-close" @click="closeSettings">×</view>
				</view>
				<view class="popup-body">
					<view class="settings-option">
						<text class="option-label">消息通知</text>
						<switch class="option-switch" :checked="true" @change="toggleNotification" />
					</view>
					<view class="settings-option">
						<text class="option-label">声音提醒</text>
						<switch class="option-switch" :checked="true" @change="toggleSound" />
					</view>
					<view class="settings-option">
						<text class="option-label">震动提醒</text>
						<switch class="option-switch" :checked="true" @change="toggleVibration" />
					</view>
				</view>
			</view>
		</view>
		<u-popup :show="show" @close="close" @open="open" :round="20" mode="bottom">
			<view class="popup-content">
				<view class="popup-top">
					<h2>职位筛选</h2>
					<uni-icons @click="show = false" type="closeempty" size="22"></uni-icons>
				</view>
				<view class="popup-taball">
					<view class="taball">
						<view>
							<view style="font-size: 40rpx;color: black;">全部沟通过的职位职位</view>
						</view>
						<view>
							<u-button @click="selectJob(null)" class="code-btn" text="当前"
								:class="{ activeJob: selectedJobId === null }"></u-button>
						</view>


					</view>
				</view>
				<scroll-view class="popup-joblist-scroll" v-if="!loding" scroll-y="true"
					@scrolltolower="onJobListLoadMore" :lower-threshold="100" style="height: 400rpx;">
					<view class="popup-joblist">
						<view class="taball" v-for="(item, index) in jobList" :key="index">
							<view>
								<view style="font-size: 40rpx;color: black;font-weight: 600;">{{ item.name }}</view>
								<view style="color: #9a9ca4;margin-top: 10rpx; display: flex;align-items: center;">
									<view style="padding: 3rpx 10rpx;margin-right: 10rpx;border: 1px solid #f5f5f5;">{{
										item.merge_status == 0 ? '已暂停' : item.merge_status == 1 ? '招聘中' :
											item.merge_status
												== 2
												? '待审核' : '审核驳回' }}</view> 待处理候选人 {{ item.sign_num }}
								</view>
							</view>
							<view>
								<u-button @click="selectJob(item)" class="code-btn" text="当前"
									:class="{ activeJob: selectedJobId === item.id }"></u-button>
							</view>
						</view>

						<!-- 职位列表加载状态提示 -->
						<view class="job-loading-status">
							<view v-if="jobListLoading && jobList.length > 0" class="loading-more">
								<u-loading-icon mode="spinner" color="#02bdc4" size="32"></u-loading-icon>
								<text class="loading-text">加载中...</text>
							</view>
							<view v-else-if="!jobListHasMore && jobList.length > 0" class="no-more">
								<text>- 没有更多职位了 -</text>
							</view>
						</view>
					</view>
				</scroll-view>
				<view style="text-align: center;" v-else>
					<u-loading-icon></u-loading-icon>
					<view style="color: #9a9ca4;">加载中...</view>
				</view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { chat, applyApi, positionsApi } from "@/utils/api.js"
export default {
	data() {
		return {
			statusBarHeight: 0,
			scrollViewHeight: 0,
			showWechatBanner: true,
			showContactReminder: true,
			hasUnreadNotification: true,
			refreshing: false,
			searchKeyword: '',
			currentTab: 0,
			tabList: ['全部', '新招呼', '我发起', '有交换'],
			messageList: [],
			showSettingsPopup: false,
			realTimeData: '',
			show: false,
			selectedJobId: null,
			loding: false,
			jobList: [],
			jobListLoading: false, // 职位列表是否正在加载
			jobListHasMore: true, // 职位列表是否还有更多数据
			page: 1,
			size: 10,
			totallate: 0,
			pagelate: 1,
			sizelate: 10,
			total: 0
		}
	},
	computed: {
		messageLists() {
			return this.$store.state.yourDataList;
		}
	},
	watch: {
		messageLists(newVal) {
			this.messageList = newVal
		}
	},
	onLoad() {
		this.initPage()

		// 监听消息列表更新事件
		uni.$on('message-list-updated', this.handleMessageListUpdate)
	},
	async onShow() {
		const params = {
			page: this.page,
			size: this.size
		}
		let res = await chat.chatList(params)
		if (res.code == 200) {
			this.messageList = res.data.data
			if (res.data.Unread > 0) {
				uni.setTabBarBadge({
					index: 3,
					text: res.data.Unread.toString()
				})
			} else {
				uni.removeTabBarBadge({
					index: 3,
					text: res.data.Unread.toString()
				})
			}
		} else {
			uni.showToast({
				title: res.msg,
			});
		}

		uni.hideLoading();
	},
	onUnload() {
		// 移除事件监听
		uni.$off('message-list-updated', this.handleMessageListUpdate)
	},
	methods: {
		// 处理消息列表更新事件
		handleMessageListUpdate(data) {
			console.log('📱 收到消息列表更新事件:', data)

			// 只有在当前是"全部"tab且没有搜索关键词时才更新
			if (this.currentTab === 0 && !this.searchKeyword && this.tabList[0] === '全部') {
				console.log('✅ 更新消息列表数据')
				this.messageList = data.messageList || []
				this.totallate = data.total || 0

				// 强制更新视图
				this.$forceUpdate()
			} else {
				console.log('⏭️ 当前不在全部tab或有搜索条件，跳过更新')
			}
		},
		async selectJob(job) {
			this.page = 1;
			this.hasMore = true; // 重置hasMore状态
			this.activelaye = 100
			if (job == null) {
				this.selectedJobId = job
				this.job = job
				this.tabList[0] = '全部'
				let reslate = await chat.chatList({
					page: this.page,
					size: this.size
				})

				if (reslate.code === 200) {
					this.getList = (reslate.data.data || []).map(item => ({
						...item,
						showPopup: false
					}));
					this.messageList = reslate.data.data || [];
					this.totallate = reslate.data.total || 0; // 更新总数

					// 重新判断是否还有更多数据
					this.hasMore = this.getList.length >= this.size && this.getList.length < this.totallate;

					console.log(`切换职位完成: 当前${this.getList.length}条，总共${this.totallate}条，还有更多: ${this.hasMore}`);
				}

				setTimeout(function () {
					uni.hideLoading();
				}, 500);
			} else {
				this.selectedJobId = job.id; // 更新选中的职位 ID
				this.jobTitle = job
				this.tabList[0] = job.name
				let reslate = await chat.chatList({
					page: this.page,
					job_id: job.id,
					size: this.size
				})

				if (reslate.code === 200) {
					this.getList = (reslate.data.data || []).map(item => ({
						...item,
						showPopup: false
					}));
					console.log(this.getList, '%%%')
					this.messageList = reslate.data.data || [];
					this.totallate = reslate.data.total || 0; // 更新总数

					// 重新判断是否还有更多数据
					this.hasMore = this.getList.length >= this.size && this.getList.length < this.totallate;

					console.log(`切换职位完成: 当前${this.getList.length}条，总共${this.totallate}条，还有更多: ${this.hasMore}`);
				}

				setTimeout(function () {
					uni.hideLoading();
				}, 500);
			}
			this.show = false
		},
		async open() {
			// console.log('open');
			this.loding = true
			// 重置职位列表分页状态
			this.page = 1
			this.jobListHasMore = true

			const params = {
				page: this.page,
				size: this.size,
				status: 100,
			}
			let res = await positionsApi.postJobList(params)
			this.jobList = res.data.data || []
			this.totallate = res.data.total || 0

			// 判断是否还有更多职位数据
			this.jobListHasMore = this.jobList.length >= this.size && this.jobList.length < this.totallate
			console.log(this.jobListHasMore, '&&&')
			this.loding = false
			uni.hideLoading()
		},
		close() {
			this.show = false
			// console.log('close');
		},
		async onJobListLoadMore() {
			console.log('职位列表上拉加载更多');
			console.log(this.jobListHasMore, '%%%')
			if (this.jobListLoading || !this.jobListHasMore) {
				console.log('职位列表正在加载或没有更多数据');
				return;
			}

			this.jobListLoading = true;
			this.page++;

			try {
				const params = {
					page: this.page,
					size: this.size,
					status: 100,
				}

				console.log('职位列表加载更多请求参数:', params);
				let res = await positionsApi.postJobList(params);

				if (res.code === 200) {
					const newJobList = res.data.data || [];
					this.totallate = res.data.total || 0;

					// 追加新的职位数据
					this.jobList = [...this.jobList, ...newJobList];

					// 判断是否还有更多数据
					this.jobListHasMore = newJobList.length >= this.size && this.jobList.length < this.totallate;

					console.log(`职位列表加载更多完成: 当前${this.jobList.length}条，总共${this.totallate}条，还有更多: ${this.jobListHasMore}`);
				} else {
					throw new Error(res.msg || '获取职位数据失败');
				}
			} catch (error) {
				console.error('职位列表加载更多失败:', error);
				this.pagelate--; // 加载失败时回退页码
				uni.showToast({
					title: '加载失败',
					icon: 'none',
					duration: 1500
				});
			} finally {
				this.jobListLoading = false;
			}
		},
		formatDate(datetime) {
			const date = new Date(datetime);
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${month}-${day}`;
		},
		initPage() {
			const systemInfo = uni.getSystemInfoSync()
			this.statusBarHeight = systemInfo.statusBarHeight || 0
			this.calculateScrollViewHeight()
		},
		calculateScrollViewHeight() {
			this.$nextTick(() => {
				const systemInfo = uni.getSystemInfoSync()
				const windowHeight = systemInfo.windowHeight
				const navbarHeight = 88 + this.statusBarHeight
				const searchHeight = 60
				const tabHeight = 50
				const bannerHeight = this.showWechatBanner ? 60 : 0
				const notificationHeight = 80
				const tabBarHeight = 50
				this.scrollViewHeight = windowHeight - navbarHeight - searchHeight - tabHeight - bannerHeight - notificationHeight - tabBarHeight
			})
		},
		async onRefresh() {
			this.refreshing = true
			const params = {
				page: this.page,
				size: this.size
			}
			let res = await chat.chatList(params)
			if (res.code == 200) {
				this.messageList = res.data.data
				if (res.data.Unread > 0) {
					uni.setTabBarBadge({
						index: 3,
						text: res.data.Unread.toString()
					})
				} else {
					uni.removeTabBarBadge({
						index: 3,
						text: res.data.Unread.toString()
					})
				}
			} else {
				uni.showToast({
					title: res.msg,
				});
			}

			uni.hideLoading();
			setTimeout(() => {
				this.refreshing = false
				uni.showToast({
					title: '刷新成功',
					icon: 'success'
				})
			}, 1500)
		},
		onRefreshRestore() {
			this.refreshing = false
		},
		onPullRefresh() { },
		closeWechatBanner() {
			this.showWechatBanner = false
			this.$nextTick(() => {
				this.calculateScrollViewHeight()
			})
		},
		openSettings() {
			this.showSettingsPopup = true;
		},
		closeSettings() {
			this.showSettingsPopup = false;
		},
		toggleNotification(e) {
			console.log('通知开关:', e.detail.value);
		},
		toggleSound(e) {
			console.log('声音开关:', e.detail.value);
		},
		toggleVibration(e) {
			console.log('震动开关:', e.detail.value);
		},
		openChat(item) {
			const userInfo = encodeURIComponent(JSON.stringify(item))
			uni.navigateTo({
				url: `/pages/chat/chat?userInfo=${userInfo}`
			})
		},
		async onSearchInput() {
			console.log(this.searchKeyword)
			let reslate = await chat.chatList({
				page: this.page,
				size: this.size,
				name: this.searchKeyword
			})
			this.messageList = reslate.data.data || [];
			this.totallate = reslate.data.total || 0; // 更新总数
			uni.hideLoading();
			// this.searchKeyword =''
		},
		async switchTab(index) {
			console.log(index)
			this.currentTab = index
			if (this.currentTab == 1) {
				let reslate = await chat.chatList({
					page: this.page,
					size: this.size,
					type: 'new_greeting'
				})
				this.messageList = reslate.data.data || [];
				this.totallate = reslate.data.total || 0; // 更新总数
				uni.hideLoading();
			} else if (this.currentTab == 2) {
				let reslate = await chat.chatList({
					page: this.page,
					size: this.size,
					type: 'only_chat'
				})
				this.messageList = reslate.data.data || [];
				this.totallate = reslate.data.total || 0; // 更新总数
				uni.hideLoading();
			} else if (this.currentTab == 3) {
				let reslate = await chat.chatList({
					page: this.page,
					size: this.size,
					type: 'exchanged'
				})
				this.messageList = reslate.data.data || [];
				this.totallate = reslate.data.total || 0; // 更新总数
				uni.hideLoading();
			} else {
				console.log(this.tabList[0])
				if (this.tabList[0] == '全部') {
					let reslate = await chat.chatList({
						page: this.page,
						// job_id: job.id,
						size: this.size
					})

					if (reslate.code === 200) {
						this.getList = (reslate.data.data || []).map(item => ({
							...item,
							showPopup: false
						}));
						console.log(this.getList, '%%%')
						this.messageList = reslate.data.data || [];
						this.totallate = reslate.data.total || 0; // 更新总数

						// 重新判断是否还有更多数据
						this.hasMore = this.getList.length >= this.size && this.getList.length < this.totallate;

						console.log(`切换职位完成: 当前${this.getList.length}条，总共${this.totallate}条，还有更多: ${this.hasMore}`);
					}
					uni.hideLoading();
				} else {
					let reslate = await chat.chatList({
						page: this.page,
						job_id: this.jobTitle.id,
						size: this.size
					})

					if (reslate.code === 200) {
						this.getList = (reslate.data.data || []).map(item => ({
							...item,
							showPopup: false
						}));
						console.log(this.getList, '%%%')
						this.messageList = reslate.data.data || [];
						this.totallate = reslate.data.total || 0; // 更新总数

						// 重新判断是否还有更多数据
						this.hasMore = this.getList.length >= this.size && this.getList.length < this.totallate;

						console.log(`切换职位完成: 当前${this.getList.length}条，总共${this.totallate}条，还有更多: ${this.hasMore}`);
					}
					uni.hideLoading();
				}
			}
		},
		onSpecialClick(type) {
			uni.showToast({ title: '点击了特殊消息项: ' + type, icon: 'none' })
			uni.navigateTo({
				url: '/pages/index/index'
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.message-container {
	height: calc(100vh - 100rpx);
	background-color: #f8f9fa;
	display: flex;
	flex-direction: column;
}

.custom-navbar {
	background: linear-gradient(135deg, #1CBBB4 0%, #0081ff 100%);
	position: relative;
	z-index: 999;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

	.navbar-content {
		height: 88rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 32rpx;

		.navbar-title {
			font-size: 36rpx;
			font-weight: 600;
			color: #ffffff;
			text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
		}

		.navbar-right {
			.settings-btn {
				width: 48rpx;
				height: 48rpx;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.2);
				padding: 8rpx;
				backdrop-filter: blur(4px);
			}
		}
	}
}

.search-container {
	background-color: #f8f9fa;
	padding: 20rpx 32rpx;

	.search-box {
		background-color: #ffffff;
		border-radius: 16rpx;
		display: flex;
		align-items: center;
		padding: 0 24rpx;
		height: 72rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

		.search-icon {
			font-size: 32rpx;
			color: #999999;
			margin-right: 16rpx;
		}

		.search-input {
			flex: 1;
			font-size: 28rpx;
			color: #333333;
			border: none;
			outline: none;
			background: transparent;

			&::placeholder {
				color: #999999;
			}
		}
	}
}

.tab-container {
	background-color: #ffffff;
	display: flex;
	padding: 0 32rpx;
	// border-bottom: 1rpx solid rgba(0,0,0,0.05);
	align-items: center;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.02);

	.tab-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		text-align: center;
		padding: 24rpx 0;
		font-size: 28rpx;
		color: #666666;
		transition: all 0.3s ease;

		&.active {
			color: #1CBBB4;
			font-weight: 600;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 40rpx;
				height: 4rpx;
				background: linear-gradient(90deg, #1CBBB4 0%, #0081ff 100%);
				border-radius: 2rpx;
			}
		}

		.filter-btn {
			width: 32rpx;
			height: 32rpx;
			margin-left: 8rpx;
			opacity: 0.8;
		}
	}
}

.special-section {
	// padding: 24rpx 32rpx;
	background: #f8f9fa;
}

.special-message-list {
	background: #fff;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
	overflow: hidden;

	.special-message-item {
		display: flex;
		align-items: center;
		padding: 24rpx;
		// border-bottom: 1rpx solid rgba(0,0,0,0.05);
		position: relative;
		transition: background-color 0.2s;

		&:active {
			background-color: #f8f9fa;
		}

		&:last-child {
			border-bottom: none;
		}

		.special-icon-wrapper {
			width: 80rpx;
			height: 80rpx;
			border-radius: 20rpx;
			background: #f8f9fa;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 24rpx;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

			.special-icon {
				font-size: 40rpx;
			}
		}

		.thumbs-up {
			color: #1CBBB4;
		}

		.heart {
			color: #ff4757;
		}

		.check {
			color: #27ae60;
		}

		.eye {
			color: #a29bfe;
		}

		.special-content {
			flex: 1;

			.special-title {
				font-size: 30rpx;
				color: #333;
				font-weight: 500;
				margin-bottom: 8rpx;
			}

			.special-subtitle {
				font-size: 24rpx;
				color: #999;
			}
		}

		.special-arrow {
			font-size: 40rpx;
			color: #ccc;
			margin-left: 16rpx;
		}
	}
}

.popup-mask {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.4);
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 2000;
	backdrop-filter: blur(4px);
}


.filter-popup {
	.filter-option {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 0;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

		&:last-child {
			border-bottom: none;
		}

		.option-label {
			font-size: 28rpx;
			color: #333;
		}

		.option-value {
			font-size: 28rpx;
			color: #666;
		}
	}
}

.settings-popup {
	.settings-option {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 24rpx 0;
		border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);

		&:last-child {
			border-bottom: none;
		}

		.option-label {
			font-size: 28rpx;
			color: #333;
		}

		.option-switch {
			transform: scale(0.8);
		}
	}
}

.message-list-container {
	flex: 1;
	background-color: #f8f9fa;
}

.contact-reminder {
	background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
	margin: 24rpx 32rpx;
	padding: 24rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 16rpx rgba(255, 193, 7, 0.1);

	.reminder-icon {
		color: #e17055;
		font-size: 32rpx;
		margin-right: 16rpx;
	}

	.reminder-text {
		flex: 1;
		font-size: 28rpx;
		color: #856404;
	}

	.reminder-close {
		font-size: 32rpx;
		color: #856404;
		padding: 8rpx;
		opacity: 0.8;
	}
}

.message-list {
	// padding: 0 24rpx;

	.message-item {
		display: flex;
		padding: 24rpx;
		background: #fff;
		// border-radius: 16rpx;
		// margin-bottom: 24rpx;
		// box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.04);
		transition: transform 0.2s;

		&:active {
			transform: scale(0.98);
		}

		.message-avatar {
			margin-right: 24rpx;
			position: relative;

			image {
				width: 96rpx;
				height: 96rpx;
				border-radius: 24rpx;
				background: #f5f5f5;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
			}

			.status-badge {
				position: absolute;
				top: -4rpx;
				right: -4rpx;
				width: 20rpx;
				height: 20rpx;
				background: linear-gradient(135deg, #ff4757 0%, #ff6b81 100%);
				border-radius: 50%;
				border: 2rpx solid #ffffff;
				box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
			}
		}

		.message-content {
			flex: 1;

			.message-header {
				display: flex;
				justify-content: space-between;
				align-items: flex-start;
				margin-bottom: 12rpx;

				.user-info {
					.user-name {
						font-size: 32rpx;
						font-weight: 600;
						color: #333;
						margin-right: 16rpx;
					}

					.user-details {
						font-size: 24rpx;
						color: #999;
					}
				}

				.message-time {
					font-size: 24rpx;
					color: #999;
				}
			}

			.message-preview {
				font-size: 28rpx;
				color: #666;
				line-height: 1.4;
			}
		}

		.unread-indicator {
			position: absolute;
			top: 24rpx;
			right: 24rpx;
			width: 16rpx;
			height: 16rpx;
			background: linear-gradient(135deg, #ff4757 0%, #ff6b81 100%);
			border-radius: 50%;
			box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
		}
	}
}

.bottom-tip {
	text-align: center;
	padding: 40rpx 0;

	text {
		font-size: 24rpx;
		color: #999;
		opacity: 0.8;
	}
}

.popup-content {
	height: 1200rpx;
	padding: 30rpx 20rpx;
	background-color: #f6f7f9;

	.popup-top {
		width: 100%;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.code-btn::after {
		border: none !important;
	}

	// .popup-taball {
	//     display: flex;
	//     height: 80rpx;
	//     display: flex;
	//     align-items: center;


	// }

	.taball {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 40rpx 30rpx;
		background-color: white;
		border-radius: 20rpx;
		margin-top: 20rpx;

		.code-btn {
			width: 200rpx;
			margin-left: 30rpx;
			border: none !important;
			background-color: #f6f7f9;
			border-radius: 20rpx;
			color: #989fa4;
			font-weight: 600;
		}

		.activeJob {
			background-color: #02bdc4 !important;
			color: white;
			/* 高亮边框 */
		}
	}

	.popup-botom {
		display: flex;
		padding: 30rpx 0;
		color: black;
	}

	.popup-joblist-scroll {
		width: 100%;
		height: calc(100% - 250rpx) !important;
		margin-top: 30rpx;
	}

	.popup-joblist {}

	// 职位列表加载状态样式
	.job-loading-status {
		padding: 30rpx 0;
		text-align: center;

		.loading-more {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			.loading-text {
				margin-top: 15rpx;
				font-size: 26rpx;
				color: #999;
			}
		}

		.no-more {
			padding: 15rpx 0;
			font-size: 26rpx;
			color: #999;
			text-align: center;
		}
	}
}

::v-deep .u-popup {
	flex: none !important
}
</style>