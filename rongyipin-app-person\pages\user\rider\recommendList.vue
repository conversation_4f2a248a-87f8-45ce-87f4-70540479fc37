<template>
    <view class="recommend-list-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="推荐列表" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 推荐列表 -->
        <view class="list-container">
            <view v-for="(item, index) in recommendList" :key="item.id" class="list-item">
                <!-- 左侧职位信息 -->
                <view class="job-section">
                    <view class="job-title-row">
                        <text class="job-title">{{ item.job_name }}</text>
                        <text class="salary-range">{{ item.min_salary }}-{{ item.max_salary }} {{ item.salary_type_name }}</text>
                    </view>
                      <view class="job-details">
                        <text class="work-time">{{ item.company_name }}</text>
                        <text class="location">{{ item.size }}</text>
                        <text class="location">{{ item.company_address }}</text>
                    </view>
                </view>

                <!-- 右侧状态信息 -->
                <view class="status-section">
                    <text class="status-text">已推荐{{ item.recommended_name }}</text>
                    <text class="status-label">
                        {{ item.status_text }}
                        <!-- {{ item.status==0?"待联系":item.status==1?"已入职":item.status==2?"不合适":item.status==3?"离职":"平台介入" }} -->
                    </text>
                    <view v-if="item.status==2">
                        <u-button  color="#14B19E" size="mini" :disabled="!(item.appeal_status==null)" @click="goAppeal(item)" :text="item.appeal_status==null?'去申诉':'已申诉'"></u-button>
                        <text class="appeal-status" v-if="item.appeal_status||item.appeal_status==0">{{ item.appeal_status_text }}</text>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { jobApi } from '@/utils/api.js'
import { computed } from 'uview-ui/libs/mixin/mixin'
export default {
    data() {
        return {
            recommendList: [],
            // shensuFlag: false
        }
    },
    computed: {
       
    },
    onLoad() {
        this.loadRecommendData()
    },
    methods: {
        shensuFlag(item){
            return item.appeal_status?true:false
        },
        // 返回上一页
        goBack() {
            uni.navigateBack()
        },

        // 加载推荐数据
        async loadRecommendData() {
             await jobApi.recommendJobPersonList().then(res => {
                uni.hideLoading()
                console.log(res);
                this.recommendList=res.data.data;
            })
        },
        // 去申诉
        goAppeal(item) {
            uni.navigateTo({
                url: '/pages/user/rider/appeal?id=' + item.id
            })
        },



        // 模拟数据 - 实际使用时请删除
        getMockData() {
            return [
                {
                    id: 1,
                    jobTitle: '销售',
                    salary: '100-300/时',
                    companyInfo: 'xx市房租科技 30-100',
                    locationInfo: 'xx区 xx广场',
                    statusText: '已推荐给王xx',
                    statusLabel: '推荐成功',
                    status: 'success'
                },
                {
                    id: 2,
                    jobTitle: '设计',
                    salary: '1000-3000/月',
                    companyInfo: 'xx市房租科技 30-100',
                    locationInfo: 'xx区 xx广场',
                    statusText: '已推荐给李xx',
                    statusLabel: '待审核',
                    status: 'pending'
                },
                {
                    id: 3,
                    jobTitle: '销售',
                    salary: '100-300/时',
                    companyInfo: 'xx市房租科技 30-100',
                    locationInfo: 'xx区 xx广场',
                    statusText: '已推荐给王xx',
                    statusLabel: '推荐成功',
                    status: 'success'
                },
                {
                    id: 4,
                    jobTitle: '销售',
                    salary: '100-300/时',
                    companyInfo: 'xx市房租科技 30-100',
                    locationInfo: 'xx区 xx广场',
                    statusText: '已推荐给王xx',
                    statusLabel: '推荐成功',
                    status: 'success'
                },
                {
                    id: 5,
                    jobTitle: '销售',
                    salary: '100-300/时',
                    companyInfo: 'xx市房租科技 30-100',
                    locationInfo: 'xx区 xx广场',
                    statusText: '已推荐给王xx',
                    statusLabel: '待审核',
                    status: 'pending'
                },
                {
                    id: 6,
                    jobTitle: '销售',
                    salary: '100-300/时',
                    companyInfo: 'xx市房租科技 30-100',
                    locationInfo: 'xx区 xx广场',
                    statusText: '已推荐给王xx',
                    statusLabel: '待审核',
                    status: 'pending'
                }
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
.recommend-list-page {
    min-height: 100vh;
    background-color: #f8f8f8;
}

/* 顶部导航栏 */

/* 列表容器 */
.list-container {
    // margin-top: 88rpx;
    background-color: #fff;
}

/* 列表项 */
.list-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;

    &:last-child {
        border-bottom: none;
    }
}
.job-details {
    display: flex;
    gap: 20rpx;
    margin-bottom: 16rpx;

    .work-time,
    .location {
        font-size: 26rpx;
        color: #666;
    }
}

/* 左侧职位信息 */
.job-section {
    flex: 1;
}

.job-title-row {
    display: flex;
    align-items: center;
    gap: 20rpx;
    margin-bottom: 12rpx;

    .job-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
    }

    .salary {
        font-size: 28rpx;
        font-weight: 500;
        color: #14B19E;
    }
}

.company-info {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 8rpx;
    display: block;
}

.location-info {
    font-size: 26rpx;
    color: #666;
    display: block;
}

/* 右侧状态信息 */
.status-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;
    min-width: 160rpx;
    // justify-content: center;
    .appeal-status{
        text-align: center;
        font-size: 24rpx;
        color: #666;
    }
}

.status-text {
    font-size: 24rpx;
    color: #666;
    text-align: right;
}

.status-label {
    font-size: 24rpx;
    font-weight: 500;
    padding: 6rpx 16rpx;
    border-radius: 20rpx;
    text-align: center;
    min-width: 120rpx;

    &.success {
        background-color: #f0f9f6;
        color: #14B19E;
        border: 1rpx solid #e6f7f1;
    }

    &.pending {
        background-color: #fff7e6;
        color: #ff9500;
        border: 1rpx solid #ffe6b3;
    }
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>
