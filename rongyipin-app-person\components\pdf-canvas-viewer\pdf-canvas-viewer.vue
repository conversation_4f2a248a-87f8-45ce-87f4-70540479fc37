<template>
    <view class="pdf-canvas-viewer">
        <!-- 工具栏 -->
        <view class="toolbar" v-if="showToolbar">
            <view class="toolbar-left">
                <button class="tool-btn" @click="zoomOut" :disabled="scale <= minScale">
                    <u-icon name="minus" size="16" color="#333"></u-icon>
                </button>
                <text class="zoom-text">{{ Math.round(scale * 100) }}%</text>
                <button class="tool-btn" @click="zoomIn" :disabled="scale >= maxScale">
                    <u-icon name="plus" size="16" color="#333"></u-icon>
                </button>
            </view>
            <view class="toolbar-center">
                <text class="page-info">{{ currentPage }} / {{ totalPages }}</text>
            </view>
            <view class="toolbar-right">
                <button class="tool-btn" @click="prevPage" :disabled="currentPage <= 1">
                    <u-icon name="arrow-left" size="16" color="#333"></u-icon>
                </button>
                <button class="tool-btn" @click="nextPage" :disabled="currentPage >= totalPages">
                    <u-icon name="arrow-right" size="16" color="#333"></u-icon>
                </button>
            </view>
        </view>

        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
            <u-loading-icon mode="spinner" size="40" color="#14B19E"></u-loading-icon>
            <text class="loading-text">{{ loadingText }}</text>
        </view>

        <!-- 错误状态 -->
        <view v-else-if="error" class="error-container">
            <u-icon name="close-circle" size="60" color="#ff4757"></u-icon>
            <text class="error-text">{{ errorMessage }}</text>
            <button class="retry-btn" @click="loadPDF">重新加载</button>
        </view>

        <!-- Canvas容器 -->
        <scroll-view v-else class="canvas-container" 
                     scroll-x="true" 
                     scroll-y="true"
                     :scroll-left="scrollLeft"
                     :scroll-top="scrollTop"
                     @scroll="onScroll">
            <view class="canvas-wrapper" 
                  :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }">
                <canvas 
                    class="pdf-canvas"
                    canvas-id="pdfCanvas"
                    :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }"
                    @touchstart="onTouchStart"
                    @touchmove="onTouchMove"
                    @touchend="onTouchEnd">
                </canvas>
            </view>
        </scroll-view>

        <!-- 页面切换指示器 -->
        <view v-if="totalPages > 1" class="page-indicator">
            <view class="indicator-dots">
                <view v-for="page in totalPages" 
                      :key="page" 
                      class="dot" 
                      :class="{ active: page === currentPage }"
                      @click="goToPage(page)">
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'PdfCanvasViewer',
    props: {
        // PDF文件URL
        pdfUrl: {
            type: String,
            required: true
        },
        // 是否显示工具栏
        showToolbar: {
            type: Boolean,
            default: true
        },
        // 初始缩放比例
        initialScale: {
            type: Number,
            default: 1
        },
        // 最小缩放比例
        minScale: {
            type: Number,
            default: 0.5
        },
        // 最大缩放比例
        maxScale: {
            type: Number,
            default: 3
        }
    },
    data() {
        return {
            loading: false,
            error: false,
            loadingText: '正在加载PDF...',
            errorMessage: '',
            
            // PDF相关
            pdfDoc: null,
            totalPages: 0,
            currentPage: 1,
            
            // Canvas相关
            canvasContext: null,
            canvasWidth: 0,
            canvasHeight: 0,
            
            // 缩放和滚动
            scale: 1,
            scrollLeft: 0,
            scrollTop: 0,
            
            // 触摸相关
            touchStartX: 0,
            touchStartY: 0,
            touchStartDistance: 0,
            touchStartScale: 1,
            
            // 系统信息
            systemInfo: {}
        }
    },
    mounted() {
        this.init()
    },
    watch: {
        pdfUrl(newUrl) {
            if (newUrl) {
                this.loadPDF()
            }
        }
    },
    methods: {
        // 初始化
        async init() {
            try {
                // 获取系统信息
                this.systemInfo = uni.getSystemInfoSync()
                
                // 设置初始缩放
                this.scale = this.initialScale
                
                // 获取Canvas上下文
                this.canvasContext = uni.createCanvasContext('pdfCanvas', this)
                
                // 加载PDF
                if (this.pdfUrl) {
                    await this.loadPDF()
                }
            } catch (error) {
                console.error('初始化失败:', error)
                this.showError('初始化失败')
            }
        },

        // 加载PDF
        async loadPDF() {
            this.loading = true
            this.error = false
            this.loadingText = '正在下载PDF文件...'
            
            try {
                // #ifdef H5
                await this.loadPDFForH5()
                // #endif
                
                // #ifdef APP-PLUS
                await this.loadPDFForApp()
                // #endif
                
                // #ifdef MP-WEIXIN
                await this.loadPDFForWeixin()
                // #endif
                
            } catch (error) {
                console.error('PDF加载失败:', error)
                this.showError(error.message || 'PDF加载失败')
            } finally {
                this.loading = false
            }
        },

        // H5端加载PDF
        async loadPDFForH5() {
            // #ifdef H5
            try {
                // 使用PDF.js库
                if (typeof pdfjsLib === 'undefined') {
                    throw new Error('PDF.js库未加载')
                }
                
                this.loadingText = '正在解析PDF...'
                
                // 加载PDF文档
                const loadingTask = pdfjsLib.getDocument(this.pdfUrl)
                this.pdfDoc = await loadingTask.promise
                this.totalPages = this.pdfDoc.numPages
                
                // 渲染第一页
                await this.renderPage(1)
                
            } catch (error) {
                throw new Error('H5端PDF加载失败: ' + error.message)
            }
            // #endif
        },

        // APP端加载PDF
        async loadPDFForApp() {
            // #ifdef APP-PLUS
            try {
                this.loadingText = '正在下载PDF文件...'
                
                // 下载PDF文件到本地
                const downloadResult = await this.downloadPDFFile()
                
                this.loadingText = '正在转换PDF为图片...'
                
                // 将PDF转换为图片
                await this.convertPDFToImages(downloadResult.tempFilePath)
                
            } catch (error) {
                throw new Error('APP端PDF加载失败: ' + error.message)
            }
            // #endif
        },

        // 微信小程序加载PDF
        async loadPDFForWeixin() {
            // #ifdef MP-WEIXIN
            try {
                this.loadingText = '正在处理PDF文件...'
                
                // 微信小程序需要特殊处理
                // 可以考虑将PDF转换为图片后显示
                await this.loadPDFAsImages()
                
            } catch (error) {
                throw new Error('微信小程序PDF加载失败: ' + error.message)
            }
            // #endif
        },

        // 下载PDF文件
        downloadPDFFile() {
            return new Promise((resolve, reject) => {
                uni.downloadFile({
                    url: this.pdfUrl,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            resolve(res)
                        } else {
                            reject(new Error('下载失败'))
                        }
                    },
                    fail: (error) => {
                        reject(new Error('下载失败: ' + error.errMsg))
                    }
                })
            })
        },

        // 将PDF转换为图片 (APP端)
        async convertPDFToImages(filePath) {
            // #ifdef APP-PLUS
            try {
                // 这里需要使用原生插件或第三方服务将PDF转换为图片
                // 由于uni-app原生不支持PDF解析，我们使用备用方案

                // 方案1: 使用在线PDF转图片服务
                await this.convertPDFViaOnlineService(filePath)

            } catch (error) {
                // 如果转换失败，显示错误信息
                throw new Error('PDF转换失败，请尝试其他方式查看')
            }
            // #endif
        },

        // 通过在线服务转换PDF
        async convertPDFViaOnlineService(filePath) {
            try {
                // 这里可以调用后端API将PDF转换为图片
                // 或者使用第三方服务如CloudConvert等

                // 模拟转换过程
                this.totalPages = 1 // 假设只有1页
                this.canvasWidth = this.systemInfo.windowWidth - 40
                this.canvasHeight = this.systemInfo.windowHeight - 200

                // 在Canvas上绘制提示信息
                this.drawPDFPlaceholder()

            } catch (error) {
                throw error
            }
        },

        // 加载PDF为图片 (微信小程序)
        async loadPDFAsImages() {
            // #ifdef MP-WEIXIN
            try {
                // 微信小程序可以通过后端API获取PDF的图片版本
                this.totalPages = 1
                this.canvasWidth = this.systemInfo.windowWidth - 40
                this.canvasHeight = this.systemInfo.windowHeight - 200

                this.drawPDFPlaceholder()

            } catch (error) {
                throw error
            }
            // #endif
        },

        // 绘制PDF占位符
        drawPDFPlaceholder() {
            if (!this.canvasContext) return

            // 设置Canvas背景
            this.canvasContext.setFillStyle('#ffffff')
            this.canvasContext.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

            // 绘制边框
            this.canvasContext.setStrokeStyle('#e5e5e5')
            this.canvasContext.setLineWidth(1)
            this.canvasContext.strokeRect(0, 0, this.canvasWidth, this.canvasHeight)

            // 绘制PDF图标和文字
            this.canvasContext.setFillStyle('#666666')
            this.canvasContext.setFontSize(16)
            this.canvasContext.setTextAlign('center')

            const centerX = this.canvasWidth / 2
            const centerY = this.canvasHeight / 2

            this.canvasContext.fillText('PDF文档', centerX, centerY - 20)
            this.canvasContext.fillText('正在加载中...', centerX, centerY + 20)

            this.canvasContext.draw()
        },

        // 渲染页面
        async renderPage(pageNum) {
            try {
                if (!this.pdfDoc) return

                this.loadingText = `正在渲染第${pageNum}页...`

                // 获取页面
                const page = await this.pdfDoc.getPage(pageNum)

                // 计算视口
                const viewport = page.getViewport({ scale: this.scale })

                // 设置Canvas尺寸
                this.canvasWidth = viewport.width
                this.canvasHeight = viewport.height

                // #ifdef H5
                // H5端直接渲染到Canvas
                const canvas = document.querySelector('#pdfCanvas')
                if (canvas) {
                    canvas.width = viewport.width
                    canvas.height = viewport.height

                    const context = canvas.getContext('2d')
                    const renderContext = {
                        canvasContext: context,
                        viewport: viewport
                    }

                    await page.render(renderContext).promise
                }
                // #endif

                this.currentPage = pageNum

            } catch (error) {
                console.error('页面渲染失败:', error)
                throw error
            }
        },

        // 显示错误
        showError(message) {
            this.error = true
            this.errorMessage = message
            this.loading = false
        },

        // 缩放控制
        zoomIn() {
            if (this.scale < this.maxScale) {
                this.scale = Math.min(this.scale + 0.25, this.maxScale)
                this.renderCurrentPage()
            }
        },

        zoomOut() {
            if (this.scale > this.minScale) {
                this.scale = Math.max(this.scale - 0.25, this.minScale)
                this.renderCurrentPage()
            }
        },

        // 页面导航
        prevPage() {
            if (this.currentPage > 1) {
                this.renderPage(this.currentPage - 1)
            }
        },

        nextPage() {
            if (this.currentPage < this.totalPages) {
                this.renderPage(this.currentPage + 1)
            }
        },

        goToPage(pageNum) {
            if (pageNum >= 1 && pageNum <= this.totalPages) {
                this.renderPage(pageNum)
            }
        },

        // 重新渲染当前页面
        async renderCurrentPage() {
            if (this.pdfDoc) {
                await this.renderPage(this.currentPage)
            }
        },

        // 滚动事件
        onScroll(e) {
            this.scrollLeft = e.detail.scrollLeft
            this.scrollTop = e.detail.scrollTop
        },

        // 触摸事件处理
        onTouchStart(e) {
            if (e.touches.length === 1) {
                // 单指触摸
                this.touchStartX = e.touches[0].clientX
                this.touchStartY = e.touches[0].clientY
            } else if (e.touches.length === 2) {
                // 双指缩放
                const touch1 = e.touches[0]
                const touch2 = e.touches[1]
                this.touchStartDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                )
                this.touchStartScale = this.scale
            }
        },

        onTouchMove(e) {
            if (e.touches.length === 2) {
                // 双指缩放
                const touch1 = e.touches[0]
                const touch2 = e.touches[1]
                const currentDistance = Math.sqrt(
                    Math.pow(touch2.clientX - touch1.clientX, 2) +
                    Math.pow(touch2.clientY - touch1.clientY, 2)
                )

                const scaleRatio = currentDistance / this.touchStartDistance
                const newScale = this.touchStartScale * scaleRatio

                if (newScale >= this.minScale && newScale <= this.maxScale) {
                    this.scale = newScale
                }
            }
        },

        onTouchEnd(e) {
            if (e.touches.length === 0) {
                // 触摸结束，重新渲染页面
                this.renderCurrentPage()
            }
        }
    }
}
</script>

<style scoped>
.pdf-canvas-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

/* 工具栏 */
.toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #fff;
    border-bottom: 1px solid #e5e5e5;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.tool-btn {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #e5e5e5;
    border-radius: 6px;
    font-size: 14px;
}

.tool-btn:disabled {
    opacity: 0.5;
}

.zoom-text {
    font-size: 14px;
    color: #666;
    min-width: 50px;
    text-align: center;
}

.page-info {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.loading-text,
.error-text {
    font-size: 14px;
    color: #666;
}

.retry-btn {
    padding: 8px 16px;
    background-color: #14B19E;
    color: #fff;
    border: none;
    border-radius: 4px;
    font-size: 14px;
}

/* Canvas容器 */
.canvas-container {
    flex: 1;
    background-color: #fff;
}

.canvas-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;
}

.pdf-canvas {
    display: block;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 页面指示器 */
.page-indicator {
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.8);
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    border-radius: 20px;
}

.indicator-dots {
    display: flex;
    gap: 8px;
}

.dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
}

.dot.active {
    background-color: #14B19E;
    transform: scale(1.2);
}
</style>
