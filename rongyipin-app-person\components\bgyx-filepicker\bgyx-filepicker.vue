<template>
	<view @click="handleClick"
		style="width: 100%;height: 100%;display: flex;align-items: center;justify-content: center;" :style="style">
		<slot></slot>
		<view @click="renderScript.emitData" :renderdata="renderdata" :change:renderdata="renderScript.receiveMsg"
			class="renderjs" id="bgyxrd"></view>
	</view>
</template>

<script>
	export default {
		name: 'bgyx-filepicker',
		data() {
			return {
				renderdata: {
					status: 0
				},
				percent: '',
			}
		},
		props: {
			style: {
				type: Object,
				default: () => {
					return {}
				}
			},
			url: {
				type: String,
				default: ''
			},
			filename: {
				type: String,
				default: 'file'
			}
		},
		mounted() {
			this.renderdata.uploadUrl = this.url
			this.renderdata.filename = this.filename
			console.log(this.url)
		},
		methods: {
			handleClick: function() {
				const _this = this
				console.log(_this.url)
				// #ifdef H5
				uni.chooseFile({
					count: 1,
					success(res) {
						_this.$emit('upload', {
							status: 'start'
						})
						const file = res.tempFiles[0]
						
						const uploadTask = uni.uploadFile({
							url: _this.url,
							file,
							formData: {
								type: 5
							},
							name: _this.filename,
							success(ret) {
								let retdata = ret.data
								if(typeof retdata == 'string'){
									retdata = JSON.parse(retdata)
								}
								_this.$emit('upload', {
									status: 'finish',
									data: retdata
								})
							}
						})

						uploadTask.onProgressUpdate((res) => {
							console.log('上传进度' + res.progress);
							_this.$emit('upload', {
								status: 'uploading',
								percent: res.progress
							})
						});
					}
				})


				// #endif

				// #ifndef H5
				if (this.renderdata.status == 1) {
					this.renderdata.status = 0
					return
				}
				this.renderdata.status = 1
				// #endif

			},
			persentchange: function(e) {
				console.log(e)
				this.percent = e
				this.$emit('upload', {
					status: 'uploading',
					percent: e
				})
			},
			getFile: function(e) {
				console.log(345345, e)
				this.renderdata.status = 0
				this.$emit('upload', {
					status: 'finish',
					data: e
				})
			},
			startUpload: function() {
				this.$emit('upload', {
					status: 'start'
				})
			},
		}
	}
</script>

<script module="renderScript" lang="renderjs">
	export default {
		data() {
			return {
				ownerVm: '',
				vm: '',
				uploadUrl: '',
				filename: 'file'
			}

		},
		mounted() {
			const view = document.getElementById('bgyxrd')
			const input = document.createElement('input')
			input.type = 'file'
			input.id = 'bgyxappendfile'
			input.onchange = this.onchange
			view.appendChild(input)

		},

		methods: {
			onchange: async function(e) {
				console.log(this.vm.$vm)
				console.log(e.target)
				const file = e.target.files[0]
				const _this = this

				const ret = await this.upload(file)

				this.ownerVm.callMethod('getFile', ret)

			},
			upload: function(file) {
				this.ownerVm.callMethod('startUpload', file)
				const _this = this
				return new Promise((r, j) => {
					// 初始化一个 XMLHttpRequest 实例对象
					const xhr = new XMLHttpRequest()
					// 创建了一个 FormData 实例
					const form = new FormData()
					// 追加文件数据
					form.append('file', file)
					// 初始化一个请求
					const url = _this.uploadUrl
					console.log(url)
					xhr.open('POST', `${url}`)
					// 请求完成
					xhr.onload = function() {
						// 接受服务端传来的数据
						const resp = JSON.parse(xhr.responseText)
						r(resp)
					}
					// 请求失败
					xhr.onerror = function(e) {
						r(false)
					}
					//设置超时时间 (可选)
					xhr.timeout = 30000;
					xhr.ontimeout = function(event) {
						r(false)
					}
					// 上传进度
					xhr.upload.onprogress = e => {
						const percent = Math.floor((e.loaded / e.total) * 100)

						_this.ownerVm.callMethod('persentchange', percent)
					}
					// 发送请求
					xhr.send(form)
				})
			},
			// 接收逻辑层发送的数据

			receiveMsg(newValue, oldValue, ownerVm, vm) {
				this.ownerVm = ownerVm
				this.vm = vm
				//数据变化

				console.log('newValue', newValue)
				this.uploadUrl = newValue.uploadUrl
				this.filename = newValue.filename
				if (newValue.status == 0) {
					return
				}

				document.getElementById('bgyxappendfile').click()

			},

			// 发送数据到逻辑层

			emitData(e, ownerVm) {

				ownerVm.callMethod('receiveRenderData', this.name)

			}

		}

	};
</script>

<style lang="scss" scoped>
	.renderjs {
		width: 1rpx;
		height: 1rpx;
		overflow: hidden;
	}
</style>