<template>
    <view class="home-container">
        <!-- 顶部导航 -->
        <!-- <u-scroll-view show-scrollbar="false"> -->
        <div class="top-header">
            <div class="nav-header">
                <u-tabs :list="list4" lineWidth="30" lineColor="#f56c6c00" :activeStyle="{
                    color: '#303133',
                    fontWeight: 'bold',
                    transform: 'scale(1.05)'
                }" :inactiveStyle="{
                        color: '#797979',
                        transform: 'scale(1)'
                    }" itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;" @change="jobclick">
                </u-tabs>
            </div>
            <div class="right-icons">
                <u-icon name="plus" size="40" style="margin-right: 10px;" @click="addzhiwei"></u-icon>
                <u-icon name="search" size="44" @click="search(currentCity)"></u-icon>
            </div>

        </div>
        <div>
        </div>
        <div class="content-top">
            <u-tabs :list="list1" @click="contentClick" lineColor="#f56c6c00"></u-tabs>
            <div>
                <div class="tag-wid" @click="location">
                    <span class="tag-text">{{ currentCityName }}</span>
                    <image src="@/static/zhijiao.png" class="tag-icon"></image>
                </div>
                <div class="tag-wid" @click="filter">
                    <span :class="filterNum > 0 ? 'tag-text tag-color' : 'tag-text'">筛选{{ filterNum > 0 ? '.' + filterNum : ''
                        }}</span>
                    <image src="@/static/zhijiao.png" class="tag-icon"></image>
                </div>

            </div>
        </div>
        <div>
            <view class="job-list-container">
                <scroll-view class="list-container" scroll-y="true" refresher-enabled="true"
                    :refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
                    :lower-threshold="50" v-if="jobList.length > 0">
                    <!-- 职位列表 -->
                    <view class="job-item" v-for="(job, index) in jobList" :key="index" @click="goDetail(job)">
                        <!-- 职位名称和薪资 -->
                        <view class="job-header">
                            <view class="job-title">{{ job.name }}</view>
                            <view class="job-salary">{{ job.min_salary }}-{{ job.max_salary }}</view>
                        </view>

                        <!-- 公司信息 -->
                        <view class="company-info">
                            <text>{{ job.company_name }}</text>
                            <text>{{ job.companySize }}</text>
                            <text>{{ job.companyAddress }}</text>
                        </view>

                        <!-- 工作要求 -->
                        <view class="job-requirements">
                            <text>{{ job.experience }}</text>
                            <text>{{ job.education }}</text>
                            <text>{{ job.workContent }}</text>
                        </view>

                        <!-- HR 信息 -->
                        <view class="hr-info">
                            <u-avatar :src="job.hrAvatar" size="40"></u-avatar>
                            <text>{{ job.username }}</text>
                            <text>{{ job.hrPosition }}</text>

                        </view>

                        <!-- 打招呼按钮 -->
                        <view class="action-button">
                            <view color="#059f9f" size="mini" @click.stop="handleHello(job)" shape="circle"
                                :hairline="false">打招呼</view>
                        </view>

                    </view>
                    <!-- 加载状态提示 -->
                    <view class="loading-status">
                        <view v-if="refreshing && jobList.length > 0" class="loading-more">
                            <u-loading-icon mode="spinner" color="#02bdc4" size="32"></u-loading-icon>
                            <text class="loading-text">加载中...</text>
                        </view>
                        <view v-else-if="noMore && jobList.length > 0" class="no-more">
                            <text>- 没有更多了 -</text>
                        </view>
                    </view>
                </scroll-view>
                <view v-else class="no-data">
                    <u-empty mode="list" icon="msg_bg.png"></u-empty>
                </view>
            </view>
            <!--  选择城市区县 -->
            <u-popup :show="cityshow" mode="bottom" @close="closecity">
                <view style="height: 100vh;">
                    <div class="city-title">
                        <u-icon name="arrow-left" size="30" @click="closecity"></u-icon>
                    </div>
                    <SearchJobIp ref="searchJobIp" @searchdata="searchdata"></SearchJobIp>
                </view>
            </u-popup>
            <!-- 筛选 -->
            <u-popup :show="filtershow" mode="bottom">
                <view style="height: 100vh;">
                    <div class="city-title">
                        <u-icon name="arrow-left" size="30" @click="closeFilter"></u-icon>
                    </div>
                    <Filters ref="Filterref" @filterData="filterData"></Filters>
                </view>
            </u-popup>
        </div>
        <!-- 没有求职意向时的提示框 -->
        <u-modal :show="noWrokShow" content='请先填写一个求职意向' @confirm="noWrokConfirm"></u-modal>


        <!-- </u-scroll-view> -->
    </view>
</template>

<script>
// import { onLoad } from 'uview-ui/libs/mixin/mixin'
// import { priseApi, applyApi } from '../../utils/api'
// import registration from "./registration/index.vue"
import AddZhiwei from "./addzhiwei.vue"
import SearchJobIp from "./searchJobIp.vue";
import Filters from './filter.vue';
import { homeApi, userinfoApi, jobApi,userApi } from '@/utils/api';
import search from "uview-ui/libs/config/props/search";
export default {
    data() {
        return {
            noWrokShow: false,
            currentCity: [],//筛选的城市区划
            currentCityName: '',//筛选的城市区划名称
            cityData: {},
            job_active: {},
            list4: [],
            list1: [{
                name: '推荐', none: 1
            }, {
                name: '附近', none: 2
            }, {
                name: '最新', none: 3
            }],
            activeGW: { name: '推荐', none: 1 },//推荐-1，附近-2，最新-3
            page: 1,
            cityshow: false,
            columns: [],
            filtershow: false,
            filterNum: 0,
            filNumData: {},//筛选

            jobList: [],
            total: {},
            city: {},
            refreshing: false,//下拉加载更多
            noMore: false,//没有更多数据

        }
    },
    components: {
        AddZhiwei,
        SearchJobIp,
        Filters
    },
    // onReachBottom() {
    //      console.log('触底了',this.page,this.job_active)
    //       console.log('触底了total',this.total[this.page])

    //     this.page += 1;
    //     if(this.total[this.page]) {
    //          this.twoloadJobList();             
    //     }
    //     this.page -= 1;




    // },

    created() {
        // this.jobip();
        this.UserResumeExpect();
    },
    onShow() {

        this.city = this.$store.state.city || {};
        if (this.list4.length > 0) {
            this.list4.map(item => {
                if (this.city.job_classid != undefined) {
                    if (item.job_classid == this.city.job_classid) {
                        // item['fujin']=this.city;     
                        this.$set(item, 'fujin', this.city);
                        this.job_active = item;
                        console.log("item1111111", item);
                    }
                }
            })
        }

        this.city.name ? this.currentCityName = this.city.name : '';
    },
    // mounted() {

    // },
    methods: {
        onLoadMore() {
            //  console.log('触底了',this.page,this.job_active)
            // console.log("onLoadMore上拉加载更多 ");
            this.page += 1;
            if (this.total[this.page]) {
                this.twoloadJobList();
            } else {
                this.page -= 1;
                this.noMore = true;
                console.log("没有更多数据了");
            }

        },

        onRefresh() {
            // this.refreshing = true;
            this.refreshing = true;
            this.jobListdata();
            //    this.refreshing = false;
            // console.log("onLoadMore下拉加载更多 ");
        },

        //
        goDetail(item) {
           const userInfo = uni.getStorageSync('userInfo');
           jobApi.addJobView({job_id: item.id,recruiter_id:userInfo.id}).then(res => {
                console.log("addJobView", res); 
                uni.navigateTo({
                    url: '/pages/index/jobinfo?id=' + item.id
                })
            })
            
        },
        //搜索
        search(city) {
            console.log("city", city);
            uni.navigateTo({
                url: '/pages/index/search?cityId=' + city[0].city_classid + '&cityName=' + city[0].city_classname.slice(0, -1)
            })
        },
        noWrokConfirm() {
            this.noWrokShow = false;
            uni.navigateTo({
                url: '/pages/index/addinfo?work=no'
            })
        },
        //第一页不传ids_page
        async twoloadJobList() {
            const params = {
                jobsalary: this.filNumData.where_salary,
                experience: this.filNumData.job_exp,
                education: this.filNumData.job_edu
            }
            console.log("twoloadJobList", this.page, this.job_active)
            await jobApi.getJobListByCompany(
                {
                    job3: this.job_active.id,
                    none: this.activeGW.none,
                    ids_page: this.total[this.page],
                    ...params
                }).then(res => {
                    // console.log("岗位列表",res);
                    uni.hideLoading();
                    this.jobList = [...this.jobList, ...res.data.data];
                    console.log("岗位列表", this.jobList)


                })
        },
        //岗位列表--第一页
        jobListdata() {
            const params = {
                jobsalary: this.filNumData.where_salary,
                experience: this.filNumData.job_exp,
                education: this.filNumData.job_edu
            }
            jobApi.getJobListByCompany({ job3: this.job_active.id, none: this.activeGW.none, ...params }).then(res => {
                // console.log("岗位列表",res);
                uni.hideLoading();
                this.refreshing = false;
                if (res.data.length == 0) {
                    this.jobList = [];
                } else {
                    this.jobList = res.data.data;
                    this.total = res.data.ids_page ? res.data.ids_page : {};
                    if (this.total[this.page]) {
                        this.noMore = false;
                    } else {
                        this.noMore = true;
                    }
                }


                //

            })
        },
        //打招呼
       async handleHello(item) {
            // console.log('打招呼:', item)
            const userInfo = uni.getStorageSync('userInfo');
            await userApi.addJobApply({job_id: item.id,recruiter_id:userInfo.id}).then(res => {
                uni.hideLoading();
                item.job_id =item.id
                console.log("item", item)
                const userInfo = encodeURIComponent(JSON.stringify(item))
                uni.navigateTo({
                    url: `/pages/chat/chat?userInfo=${userInfo}`
                })
             })
            
        },
        //用户求职意向
        UserResumeExpect() {
            userinfoApi.getUserResumeExpect().then(res => {
                uni.hideLoading();
                // console.log(res, 'qqqqq');
                if (res.data.length == 0) {
                    this.noWrokShow = true;
                } else {
                    res.data.data.length > 0 && res.data.data.map(item => {
                        item['name'] = item.job_classname;
                        item['id'] = item.job_classid;

                    })
                    this.list4 = res.data.data;
                    this.job_active = res.data.data.length > 0 ? res.data.data[0] : {};
                    console.log("26744444444", this.job_active);
                    this.currentCity = [{ ...res.data.data[0] }]
                    // this.currentCity = res.result.ad_info
                    this.currentCityName = res.data.data[0].city_classname.slice(0, -1)

                    this.jobListdata(res.data.data[0].id)
                }

                //   this.surrounding(res.result.location.lat, res.result.location.lng)
            })
        },
        //求职意向qiehuan
        jobclick(item) {
            console.log(item, 'itemaa');
            this.job_active = item;
            console.log('282', this.job_active);
            this.currentCity = [{ ...item }];
            this.currentCityName = item.city_classname.slice(0, -1);
            delete item.rect;
            this.filNumData = {};
            this.filterNum = 0;
            this.jobListdata(item);
            this.page = 1;
            this.noMore = false;

            // this.getJobListByCompanys()
        },
        //筛选的区划
        searchdata(data) {
            const namearr = [];
            const idarr = [];
            data.map(item => {
                namearr.push(item.name);
                item['city_classname'] = item.name;
                item['city_classid'] = item.id;
                idarr.push(item.id);
            })
            const datastr = namearr.join("/");
            this.currentCityName = datastr;
            this.currentCity = data;
            this.closecity();

            this.jobListdata({ ...this.job_active, area_id: idarr });
        },
        //
        // jobip(){
        //     homeApi.getCityLists({
        //       key: 'QMEBZ-QAUKB-HWGUE-NCFVQ-SKKBH-ULBYV',
        //       // ip: '*************',
        //     }).then(res => {
        //         console.log(res, 'city');
        //         this.currentCity = res.result.ad_info
        //         this.currentCityName=res.result.ad_info.city.slice(0,-1)
        //         uni.hideLoading();

        //         //   this.surrounding(res.result.location.lat, res.result.location.lng)
        //     })
        // },

        //新新增职位
        addzhiwei() {
            //    this.$refs.addzhiwei.openpop();
            uni.navigateTo({
                url: "./addzhiwei"
            })

        },
        contentClick(item) {
            console.log('item', item)
            this.activeGW = item;
            console.log('currentCity', this.currentCity)

            const arr = [];
            this.currentCity.map(itemcity => {
                arr.push(itemcity.city_classname)
            })
            console.log('arr', arr.join('/'))
            // 点附近时，这个求职意向有过定位就显示定位的位置，
            if (item.none == 2 && this.job_active.fujin != undefined) {
                this.currentCityName = this.city.name == undefined ? arr.join('/').slice(0, -1) : this.city.name;

            } else {
                this.currentCityName = arr.join('/').slice(0, -1)
            }
            console.log('this.job_active348', this.job_active)
            if (item.none == 2 && this.job_active.fujin == undefined) {
                this.nearIPhow = true;
                uni.navigateTo({
                    url: "./mapnear?job_classid=" + this.job_active.job_classid
                })

            } else {
                this.page = 1;
                //附近的时候要传经纬度，要是不传，后台会用登录人的经纬度，所以附近的页面也要写，，
                //有经纬度的时候就不用附近的定位，没有经纬度的时候，点进入地图搜索出来一些附近的地址
                this.jobListdata();
            }

        },
        location() {
            console.log('location', this.activeGW.none, this.city.name);
            if (this.activeGW.none != 2 && this.city.name != undefined) {
                this.cityshow = true;
                this.$nextTick(() => {
                    const searchJobIpRef = this.$refs.searchJobIp;
                    if (searchJobIpRef && typeof searchJobIpRef.getIpdata === 'function') {
                        searchJobIpRef.getIpdata(this.job_active, this.currentCity);
                    } else {
                        uni.showToast({ title: '组件加载失败', icon: 'none' });
                    }
                });
            } else {
                this.nearIPhow = true;
                uni.navigateTo({
                    url: "./mapnear"
                })
            }

        },
        //筛选
        filter() {
            this.filtershow = true;
            this.$nextTick(() => {
                console.log("我执行了1");
                const searchJobIpRef = this.$refs.Filterref;
                if (searchJobIpRef && typeof searchJobIpRef.filterData === 'function') {
                    console.log("我执行了2");
                    searchJobIpRef.filterData(this.filNumData);
                } else {
                    uni.showToast({ title: '组件加载失败', icon: 'none' });
                }
                // this.$refs.Filterref.datas();
            })
        },
        //筛选完成
        filterData(data) {
            this.filtershow = false;
            this.filterNum = 0;
            data.job_edu == 0 ? '' : this.filterNum++;
            data.job_exp == 0 ? '' : this.filterNum++
            data.where_salary == 0 ? '' : this.filterNum++;
            this.filNumData = data
            console.log('筛选完成', data);

            this.jobListdata();
        },
        //筛选完成
        closeFilter() {
            this.filtershow = false;
        },
        closecity() {
            this.cityshow = false;

        },

    }
}
</script>

<style lang="scss" scoped>
.top-header {
    display: flex;
    justify-content: space-between;
    margin-right: 10px;
    margin-top: 40rpx;
}

.nav-header {
    width: 70%;
    overflow: auto;
    /* 允许滚动 */
    scrollbar-width: none;
    /* Firefox */
    -ms-overflow-style: none;
}

.right-icons {
    width: 30%;
    display: flex;
    justify-content: right;
}

.content-top {
    display: flex;
    justify-content: space-between;
    margin: 0 20rpx;
}

.tag-wid {
    display: inline-block;
    max-width: 170rpx;
    height: 40rpx;
    border-radius: 5rpx;
    background: #dbdbdb;
    font-size: 12px;
    text-align: center;
    line-height: 40rpx;
    position: relative;
    margin: 28rpx 10rpx;
    padding: 0rpx 6rpx 4rpx;

    .tag-text {
        display: inline-block;
        max-width: 160rpx;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        padding: 4rpx 18rpx 4rpx 4rpx;
    }

    .tag-color {
        color: #00915e
    }

    ;

    .tag-icon {
        position: absolute;
        display: inline-block;
        width: 20rpx;
        top: 22rpx;
        right: 0;
        height: 10px;

        /* background: url(/static/zhijiao.png); */
        z-index: 2;
    }
}

.city-title {
    padding: 40rpx;

}

.city-filter {
    padding: 8px 14px;
    font-size: 12px;
    color: #06E698;

    .ft-icon {
        width: 10px;
    }
}

.city-content {
    padding: 10px 0;

    ::v-deep .u-grid-item {
        justify-content: flex-start;
    }

    ::v-deep .u-grid-item--hover-class {
        opacity: 1;
    }

    .city-item {
        display: inline-block;
        height: 40px;
    }
}

.no-data {
    display: flex;
    align-items: center;
    height: calc(100vh - 310rpx);
    justify-content: center;
}

.job-list-container {

    padding: 20rpx;
    background-color: #f9f9f9;

    .list-container {
        height: calc(100vh - 330rpx);
    }

    .loading-more,
    .no-more {
        text-align: center;
        color: #afafaf;
    }
}

.job-item {
    position: relative;
    background-color: #fff;
    border-radius: 10rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;

    .job-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10rpx;

        .job-title {
            font-size: 32rpx;
            font-weight: bold;
            color: #333;
        }

        .job-salary {
            font-size: 28rpx;
            color: #e74c3c;
        }
    }

    .company-info,
    .job-requirements,
    .hr-info {
        display: flex;
        align-items: center;
        margin-bottom: 10rpx;

        text {
            font-size: 24rpx;
            color: #666;
            margin-right: 10rpx;
        }
    }

    .hr-info {
        margin-bottom: 20rpx;

        .u-avatar {
            margin-right: 10rpx;
        }
    }

    .action-button {
        text-align: right;
        position: absolute;
        right: 20px;
        bottom: 37px;

        view {
            font-size: 24rpx;
            background-color: #14B19E;
            width: 100rpx;
            height: 50rpx;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 20rpx;
            color: white;
        }
    }
}
</style>