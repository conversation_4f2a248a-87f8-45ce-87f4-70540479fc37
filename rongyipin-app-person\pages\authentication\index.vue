<!-- <template>
    <view class="container">
        <view class="header">
            <u-navbar height="44px" @leftClick="handleBacklate" bgColor="#f54b4b" :autoBack="false" :leftIconSize="30"
                :leftIconColor="'#ffffff'" leftIcon="close"  safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>
        <view class="header-title">
            <text class="warning-icon"><img src="@/static/app/authentication/exclamation.png" alt=""></text>
            <text class="warning-text">未完成商家资质认证</text>
            <text class="warning-text">将无法进行招聘</text>
        </view>
        <view class="card">
            <view class="form-item">
                <text class="label"><text class="required">*</text>上传公司营业执照</text>
                <view class="tips">
                    <text>· 营业执照原件/电子版 · 营业执照复印件</text>
                </view>
                <view v-if="imagePath" class="upload-btn">
                    <image :src="imagePath" mode="aspectFit" style="width: 100%; height: 300rpx;" />
                    <view class="verify-options" @tap="chooseImage">
                        <img src="@/static/app/authentication/imgUpload.png" alt="">
                        <view> 重新上传</view>
                    </view>
                </view>
                <button v-if="!imagePath" @tap="chooseImage">拍照或上传照片</button>

            </view>
            <view class="form-item">
                <text class="label"><text class="required">*</text>上传企业LOGO </text>
                <view class="tips">
                    <text>· 企业LOGO原件/电子版 </text>
                </view>
                <view v-if="imageLOGO" class="upload-btn">
                    <image :src="imageLOGO" mode="aspectFit" style="width: 100%; height: 300rpx;" />
                    <view class="verify-options" @tap="chooseLogo">
                        <img src="@/static/app/authentication/imgUpload.png" alt="">
                        <view> 重新上传</view>
                    </view>
                </view>
                <button v-if="!imageLOGO" @tap="chooseLogo">拍照或上传照片</button>

            </view>
            <view class="form-item">
                <text class="label"><text class="required">*</text>统一社会信用代码</text>
                <input class="input" type="text" placeholder="请输入" v-model="creditCode" />
            </view>
            <view class="form-item">
                <text class="label"><text class="required">*</text>公司法人</text>
                <u--input placeholder="请输入联系方式" border="surround" v-model="legal"></u--input>
            </view>
            <view class="form-item">
                <text class="label"><text class="required">*</text>企业名称</text>
                <input class="input" type="text" placeholder="请输入" v-model="licenseName" />
            </view>
            <view class="form-item">
                <text class="label"><text class="required">*</text>企业简介</text>
                <u--textarea v-model="licenseIntro" placeholder="请输入公司介绍"></u--textarea>
            </view>


            <view class="form-item">
                <text class="label"><text class="required">*</text>企业性质</text>
                <view class="nature" @click="showPicker('perty')">
                    <p>{{ property.name }}</p>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>
            <view class="form-item">
                <text class="label"><text class="required">*</text>公司规模</text>
                <view class="nature" @click="showPicker('Scale')">
                    <p>{{ Scale.name }}</p>
                    <u-icon name="arrow-right"></u-icon>
                </view>
            </view>

            <view class="form-item">
                <text class="label"><text class="required">*</text>所属行业</text>
                <uni-section title="">
                    <uni-data-picker popup-title="请选择所属行呀" :localdata="dataTree" v-model="classes" @change="onchange"
                        @nodeclick="onnodeclick" @popupopened="onpopupopened" @popupclosed="onpopupclosed"
                        @clear="onclear">
                    </uni-data-picker>
                </uni-section>
            </view>
            <view class="form-item">
                <text class="label"><text class="required">*</text>注册资金</text>
                <u--input placeholder="请输入注册资金" border="surround" v-model="Funds"></u--input>
            </view>
            <view class="form-item">
                <text class="label"><text class="required">*</text>联系方式</text>
                <u--input placeholder="请输入联系方式" border="surround" v-model="entertel"></u--input>
            </view>

            <view class="form-item">
                <view class="address-section">
                    <text class="label"><text class="required">*</text>工作地址</text>
                    <view class="address-picker" @click="showAddressPicker">
                        <text>{{ selectedAddress || '请选择工作地址' }}</text>
                        <u-icon name="arrow-right" color="black" size="28"></u-icon>
                    </view>
                </view>
            </view>
            <view class="form-item">
                <text class="label"><text class="required">*</text>详细地址</text>
                <u--input placeholder="请输入联系方式" border="surround" v-model="detailed"></u--input>
            </view>
        </view>
        <view class="Submit">
            <button class="submit-btn" @tap="submitForm">提交审核</button>
        </view>
        <u-picker :show="show" :itemHeight="70" :columns="columns" @confirm="onPickerConfirm" @cancel="show = false"
            keyName="name"></u-picker>
        <u-picker :show="show1" :itemHeight="70" :columns="columnsScale" @confirm="onPickerConfirm"
            @cancel="show1 = false" keyName="name"></u-picker>

    </view>
</template> -->
<template>
    <view class="auth-container">
        <!-- 顶部导航 -->
        <view class="nav-bar">
            <u-navbar height="44px" bgColor="#fff" :autoBack="false" leftIcon="arrow-left" leftIconColor="#333"
                leftIconSize="28" @leftClick="goBack" fixed placeholder></u-navbar>
        </view>

        <!-- 标题和提示 -->
        <view class="auth-title-row">
            <img style="width: 40rpx;height: 40rpx;" src="@/static/app/authentication/exclamation.png" alt="">
            <text class="auth-title">商家资质认证</text>
        </view>
        <view class="auth-desc">
            为保证招聘过程的顺利进行，请完成商家资质认证，填写营业执照上的公司名称，并上传或拍摄营业执照照片
        </view>

        <!-- 表单内容 -->
        <view class="auth-form">
            <!-- 营业执照名称 -->
            <view class="form-item">
                <text class="label"><text class="required">*</text>营业执照名称</text>
                <input class="input" v-model="licenseName" placeholder="请输入" />
            </view>
            <!-- 上传营业执照 -->
            <view class="form-item">
                <text class="label"><text class="required">*</text>上传公司营业执照</text>
                <view class="upload-area" @click="chooseImage">
                    <image v-if="imagePath" :src="imagePath" class="license-img" mode="aspectFit" />
                    
                    <view v-else class="upload-placeholder">
                        <image class="upload-bg" src="@/static/app/authentication/license.png" />
                        <view class="upload-btn">
                            <u-icon name="camera" color="#fff" size="36"></u-icon>
                            <text>拍摄/上传</text>
                        </view>
                    </view>
                </view>
            </view>

            
            <!-- 注意事项 -->
            <view class="notice">
                <view class="notice-title">注意事项</view>
                <view class="notice-list">
                    <view>1.请上传清晰完整的营业执照照片</view>
                    <view>2.营业执照信息需与企业信息保持一致</view>
                    <view>3.仅支持jpg/png格式，大小不超过5M</view>
                    <view>4.如有疑问请联系客服</view>
                </view>
            </view>
        </view>

        <view class="Submit">
            <button class="submit-btn" @tap="submitForm">提交审核</button>
        </view>
    </view>
</template>
<script>
import { dictApi, fileApi, priseApi } from '@/utils/api.js'
export default {
    data() {
        return {
            licenseName: '',
            image: '',
            imagePath: '',
            imageLOGO: '',
            licenseIntro: '11',
            columns: [],
            show: false,
            property: '1',
            Scale: '1',
            rawData: [], // 原始数据
            classes: '1-2',
            dataTree: [],
            columnsScale: [],
            show1: false,
            Funds: '',
            selectedAddress: '',
            entertel: '',
            creditCode: '',

            imageConver: '',
            imageConver1: '',
            industry1: '',
            industry2: '',
            city: '',
            legal: '',
            detailed: ''
        }
    },
    watch: {
        '$store.state.cation'(newVal) {
            this.selectedAddress = newVal.title;
            this.city = newVal;
            console.log(this.city, 'watch 监听');
        }
    },
    async mounted() {
        // 获取企业性质字典数据
        const res = await dictApi.getDict()
        this.property = res.data.job_pr.data[0]
        this.Scale = res.data.job_mun.data[0]
        this.columns = [res.data.job_pr.data]
        this.columnsScale = [res.data.job_mun.data]
        const res1 = await fileApi.getCompany()
        this.rawData = res1.data;
        this.dataTree = this.transformToPickerData(this.rawData)
        uni.hideLoading()
    },
    methods: {
        handleBacklate() {
            uni.switchTab({
                url: '/pages/index/index'
            });
        },
        onclear() {
            console.log('清空');
        },
        transformToPickerData(raw) {
            return raw.map(item => ({
                text: item.name,
                value: item.id,
                children: (item.children || []).map(child => ({
                    text: child.name,
                    value: child.id
                }))
            }))
        },
        onnodeclick(e) {
        },
        onpopupopened(e) {
        },
        onpopupclosed(e) {
        },
        onchange(e) {
            console.log('onchange:', e);
            if (e.detail.value.length === 0) {
                this.industry1 = ''
                this.industry2 = ''
            } else {
                this.industry1 = e.detail.value[0].value
                this.industry2 = e.detail.value[1].value
            }

        },
        showAddressPicker() {
            uni.navigateTo({
                url: './positioning'
            });
        },
        showPicker(type) {
            console.log('选择器类型：', type);
            // 根据类型设置不同的选项
            switch (type) {
                case 'perty':
                    this.show = true; // 打开选择器
                    this.columns = this.columns;
                    break;
                case 'Scale':
                    this.show1 = true; // 打开选择器
                    this.columnsScale = this.columnsScale;
                    break;
            }
            this.currentPickerType = type; // 保存当前选择的类型
        },
        onPickerConfirm(value) {
            switch (this.currentPickerType) {
                case 'perty':
                    this.property = value.value[0];
                    console.log('选择的企业性质:', this.property);
                    this.show = false; // 关闭选择器
                    break;
                case 'Scale':
                    this.Scale = value.value[0];
                    this.show1 = false; // 关闭选择器
                    break;
            }
        },
        chooseImage() {
            uni.chooseImage({
                count: 1,
                success: (res) => {
                    this.image = res.tempFilePaths[0]
                }
            })
        },
        async submitForm() {
            if (!this.detailed || !this.selectedAddress || !this.entertel || !this.Funds || !this.imagePath || !this.imageLOGO || !this.creditCode || !this.licenseName || !this.licenseIntro || !this.property || !this.Scale || !this.Scale) {
                uni.showToast({
                    title: '请填写完整信息',
                    icon: 'none'
                })
                return
            }
            // 提交逻辑
            uni.showLoading({ title: '提交中...' })
            const params = {
                logo: this.imageConver1,
                name: this.licenseName,
                content: this.licenseIntro,
                property: this.property.id,
                industry1: this.industry1,
                industry2: this.industry2,
                size: this.Scale.id,
                reg_capital: this.Funds,
                area_id: this.city.adcode,
                legalname: this.legal,
                business_license: this.imageConver,
                code: this.creditCode,
                tel: this.entertel,
                address_name: this.city.title,
                formatted_address: this.city.address,
                address: this.detailed,
                lat: this.city.location.lat,
                lon: this.city.location.lng
            }
            let res = await fileApi.getCompanySave(params)
            console.log(res, '提交结果')
            if (res.code !== 200) {
                uni.hideLoading()
                uni.showToast({ title: res.msg, icon: 'none' })
                return
            }
            else {
                setTimeout(async () => {
                    uni.hideLoading()
                    uni.showToast({ title: '提交成功', icon: 'success' })
                    let resdata = await priseApi.getCompanyInfoe()
                    console.log(resdata, 'resdata')
                    if (resdata.data.is_auth == 0) {
                        uni.showToast({ title: resdata.msg, icon: 'error' })
                        setTimeout(() => {
                            uni.navigateTo({
                                url: '/pages/audits/index'
                            })
                        }, 2000)
                    } else if (resdata.data.is_auth == 1) {
                        uni.showToast({ title: resdata.msg, icon: 'success' })
                        setTimeout(() => {
                            uni.navigateTo({
                                url: '/pages/second/second'
                            })
                        }, 2000)
                    } else {
                        uni.showToast({ title: resdata.msg, icon: 'error' })
                    }
                }, 2000)
            }
        },
        chooseImage() {
            uni.chooseImage({
                count: 1,
                sizeType: ['compressed'],
                sourceType: ['camera', 'album'], // 支持拍照和相册
                success: (res) => {
                    this.imagePath = res.tempFilePaths[0]
                    this.uploadImage(this.imagePath)
                },
                fail: (err) => {
                    console.log('图片选择失败', err)
                }
            })
        },
        uploadImage(filePath) { 
            console.log('选择的图片路径:', filePath)
            uni.showLoading({ title: '上传中' })
            // #ifdef APP-PLUS
            uni.uploadFile({
                url: 'http://8.130.152.121:82/jobapi/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 4
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver1 = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif

            // #ifdef H5
            uni.uploadFile({
                url: '/jobapi/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 4
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver1 = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif
        },

        chooseLogo() {
            uni.chooseImage({
                count: 1,
                sizeType: ['compressed'],
                sourceType: ['camera', 'album'], // 支持拍照和相册
                success: (res) => {
                    this.imageLOGO = res.tempFilePaths[0]
                    this.uploadLogo(this.imageLOGO)
                },
                fail: (err) => {
                    console.log('图片选择失败', err)
                }
            })
        },
        uploadLogo(filePath) { 
            console.log('选择的图片路径:', filePath)
            uni.showLoading({ title: '上传中' })
            // #ifdef APP-PLUS
            uni.uploadFile({
                url: 'http://8.130.152.121:82/jobapi/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 2
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif

            // #ifdef H5
            uni.uploadFile({
                url: '/jobapi/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 2
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.imageConver = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif
        },
    }
}
</script>

<style scoped lang="scss">
// .container {
//     background-color: #f54b4b;
//     min-height: 100vh;
// }

// .header {
//     height: 88rpx;
//     /* display: flex;
//     justify-content: space-between;
//     align-items: center; */
//     background-color: #fff;

//     .navbar {
//         height: 100%;
//     }
// }

// .header-title {
//     display: flex;
//     width: 100%;
//     flex-wrap: wrap;

//     text {
//         width: 100%;
//         text-align: center;
//         color: #fff;
//         font-size: 50rpx;

//         img {
//             width: 80rpx;
//             height: 80rpx;
//         }
//     }
// }

// .warning-icon {
//     font-size: 40rpx;
//     display: block;
// }

// .warning-text {
//     font-size: 30rpx;
//     font-weight: bold;
//     margin-top: 10rpx;
// }

// .card {
//     background-color: #ffffff;
//     border-top-left-radius: 30rpx;
//     border-top-right-radius: 30rpx;
//     padding: 40rpx 30rpx;
//     margin-top: 40rpx;
//     height: calc(100vh - 607rpx);
//     overflow-y: auto;
// }

// .form-item {
//     margin-bottom: 40rpx;
// }

// .label {
//     font-size: 28rpx;
//     font-weight: 600;
//     margin-bottom: 20rpx;
//     display: block;
// }

// .required {
//     color: #f00;
//     margin-right: 6rpx;
// }

// .nature {
//     display: flex;
//     align-items: center;
//     height: 30rpx;
//     justify-content: space-between;
//     border-bottom: 1px solid #f8f8f8;
//     padding: 10rpx;
// }

// .input {
//     border: 1rpx solid #ddd;
//     border-radius: 10rpx;
//     padding: 20rpx;
//     font-size: 28rpx;
// }

// .tips text {
//     display: block;
//     color: #888;
//     font-size: 24rpx;
//     margin: 6rpx 0;
// }

// .address-section {
//     margin: 40rpx 0;

//     .section-title {
//         font-size: 32rpx;
//         font-weight: bold;
//         color: #333;
//         margin-bottom: 20rpx;
//     }

//     .address-picker {
//         height: 88rpx;
//         background-color: #f8f8f8;
//         border-radius: 12rpx;
//         display: flex;
//         align-items: center;
//         justify-content: space-between;
//         padding: 0 30rpx;

//         text {
//             font-size: 28rpx;
//             color: #333;
//         }
//     }
// }

// .upload-btn {
//     margin-top: 20rpx;
//     padding: 20rpx;
//     border: 2rpx dashed #ccc;
//     text-align: center;
//     border-radius: 10rpx;
//     color: #333;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     position: relative;
// }

// .upload-icon {
//     width: 40rpx;
//     height: 40rpx;
//     margin-right: 10rpx;
// }

// .Submit {
//     width: 100%;
//     /* height: 80rpx; */
//     /* position: fixed; */
//     /* bottom: 30rpx; */
//     /* border-radius: 12rpx; */
//     padding: 40rpx 0rpx;
//     background-color: white;
//     z-index: 100;
// }

// .submit-btn {
//     width: 92%;
//     /* height: 100%; */
//     margin: 0 auto;
//     background-color: #00c4b6;
//     color: white;
//     font-size: 30rpx;
//     /* padding: 20rpx; */


// }

// .verify-options {
//     position: absolute;
//     width: 240rpx;
//     height: 70rpx;
//     display: flex;
//     justify-content: space-around;
//     align-items: center;
//     font-size: 30rpx;
//     /* color: #999; */
//     margin-top: 20rpx;
//     background-color: white;
//     z-index: 200;
//     color: black;
//     border-radius: 20rpx;

//     img {
//         width: 40rpx;
//         height: 40rpx;
//         /* margin-right: 10rpx; */
//     }
// }


/* 新版 */
.auth-container {
    background: #fff;
    min-height: 100vh;
    padding-bottom: 40rpx;
}

.nav-bar {
    background: #fff;
}

.auth-title-row {
    display: flex;
    align-items: center;
    margin: 40rpx 0 10rpx 32rpx;
    justify-content: center;
}

.icon-warn {
    width: 36rpx;
    height: 36rpx;
    margin-right: 12rpx;
}

.auth-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #222;
}

.auth-desc {
    color: #666;
    font-size: 26rpx;
    margin: 0 32rpx 32rpx 32rpx;
    line-height: 1.6;
}

.auth-form {
    background: #fff;
    border-radius: 20rpx;
    margin: 0 24rpx;
    padding: 32rpx 24rpx 24rpx 24rpx;
    box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.04);
}

 .form-item {
  margin-bottom: 40rpx;
}
.label {
  font-size: 28rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
  color: #222;
} 
.required {
    color: #f54b4b;
    margin-right: 6rpx;
}

.input {
    width: 100%;
    border: 1rpx solid #eee;
    border-radius: 10rpx;
    padding: 20rpx;
    font-size: 28rpx;
    margin-top: 10rpx;
    background: #fafbfc;
}

.upload-area {
    width: 100%;
    min-height: 440rpx;
    border: 2rpx dashed #e5e5e5;
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f7f8fa;
    position: relative;
    cursor: pointer;

    .verify-options {
        position: absolute;
        width: 240rpx;
        height: 70rpx;
        display: flex;
        justify-content: space-around;
        align-items: center;
        font-size: 30rpx;
        /* color: #999; */
        margin-top: 20rpx;
        background-color: white;
        z-index: 200;
        color: black;
        border-radius: 20rpx;

        img {
            width: 40rpx;
            height: 40rpx;
            /* margin-right: 10rpx; */
        }
    }
}

.license-img {
    width: 100%;
    height: 400rpx;
    border-radius: 12rpx;
    object-fit: contain;
}

.upload-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.upload-bg {
    width: 100%;
    height: 440rpx;
    object-fit: contain;
    opacity: 0.8;
    margin-bottom: 10rpx;
}

.upload-btn {
    position: absolute;
    left: 50%;
    top: 60%;
    transform: translate(-50%, -50%);
    background: #00c4b6;
    color: #fff;
    border-radius: 40rpx;
    padding: 12rpx 36rpx;
    display: flex;
    align-items: center;
    font-size: 28rpx;
    font-weight: 500;
    box-shadow: 0 2rpx 8rpx rgba(0, 196, 182, 0.08);
}

.upload-btn text {
    margin-left: 12rpx;
}

.notice {
    margin-top: 32rpx;
    background: #f8f8f8;
    border-radius: 12rpx;
    padding: 24rpx;
}

.notice-title {
    color: #f54b4b;
    font-size: 26rpx;
    font-weight: bold;
    margin-bottom: 12rpx;
}

.notice-list view {
    color: #888;
    font-size: 24rpx;
    line-height: 1.8;
}
.Submit {
    width: 90%;
    /* height: 80rpx; 
    /* position: fixed; */
    /* bottom: 30rpx; */
    /* border-radius: 12rpx; */
    // padding: 40rpx 0rpx;
    background-color: white;
    z-index: 100;
    margin: 0 auto;
    background-color: #00c4b6;
    border-radius: 30rpx;
    margin-top: 100rpx;
}
.submit-btn {
    width: 92%;
    /* height: 100%; */
    margin: 0 auto;
    background-color: #00c4b6;
    color: white;
    font-size: 30rpx;
    /* padding: 20rpx; */
    border-radius: 30rpx;
}
::v-deep .u-textarea {
    border: 1px solid #ccc;
    height: 130rpx;
}

::v-deep .uni-section-header {
    padding: inherit !important;
}

::v-deep .u-input--square {
    border: 1px solid #ccc;
}
</style>