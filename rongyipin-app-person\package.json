{"id": "uni-starter", "displayName": "uni-starter", "version": "2.2.3", "description": "云端一体应用快速开发基本项目模版", "keywords": ["login", "登录", "搜索", "uni-id实例", "留言板"], "repository": "https://gitcode.net/dcloud/uni-starter", "engines": {"HBuilderX": "^3.2.6"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "", "type": "unicloud-template-project"}, "uni_modules": {"dependencies": ["uni-id-pages"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "u", "app-harmony": "u", "app-uvue": "u"}, "H5-mobile": {"Safari": "u", "Android Browser": "u", "微信浏览器(Android)": "u", "QQ浏览器(Android)": "u"}, "H5-pc": {"Chrome": "y", "IE": "n", "Edge": "u", "Firefox": "u", "Safari": "u"}, "小程序": {"微信": "y", "阿里": "u", "百度": "u", "字节跳动": "u", "QQ": "u", "京东": "u"}, "快应用": {"华为": "u", "联盟": "u"}, "Vue": {"vue2": "y", "vue3": "y"}}}}, "dependencies": {"@dcloudio/uni-ui": "^1.5.10", "qrcodejs2": "^0.0.2", "socket.io-client": "^4.1.0", "uview-ui": "^2.0.38"}, "devDependencies": {"sass": "^1.87.0", "sass-loader": "^10.1.1"}}