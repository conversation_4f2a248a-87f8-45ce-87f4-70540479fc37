# 全局 WebSocket 监听功能说明

## 功能概述

实现了全局 WebSocket 监听功能，当监听的接口发生变化时，无论用户在哪个页面都会打印出 "111"。

## 实现原理

### 1. 全局监听器机制
- 在 `WebSocketService` 类中添加了 `globalListeners` 数组
- 所有接收到的 WebSocket 消息都会触发全局监听器
- 全局监听器在应用级别注册，不受页面切换影响

### 2. 应用级别初始化
- 在 `App.vue` 中初始化 WebSocket 连接
- 在连接成功后注册全局监听器
- 确保整个应用生命周期内都保持监听

## 核心代码

### WebSocket 服务 (`utils/websocket.js`)

```javascript
// 添加全局监听器
addGlobalListener(callback) {
    this.globalListeners.push(callback);
}

// 触发全局监听器
triggerGlobalListeners(data) {
    this.globalListeners.forEach(callback => {
        try {
            callback(data);
        } catch (error) {
            console.error('WebSocket: 全局监听器执行失败', error);
        }
    });
}

// 在消息处理中触发
handleMessage(data) {
    // ... 解析消息
    
    // 触发全局监听器 - 无论在哪个页面都会执行
    this.triggerGlobalListeners(jsonData);
    
    // ... 其他处理
}
```

### 应用级别注册 (`App.vue`)

```javascript
// 设置全局WebSocket监听器
setupGlobalWebSocketListener() {
    // 添加全局监听器 - 无论在哪个页面都会执行
    webSocketService.addGlobalListener((data) => {
        console.log('111'); // 您要求的打印
        console.log('App: 全局监听到WebSocket消息:', data);
        
        // 如果是聊天相关消息，可以在这里做全局处理
        if (data.event === 'private_msg' || data.event === 'chat_update') {
            console.log('App: 检测到聊天消息变化');
        }
    });
}
```

## 使用效果

### 1. 全局监听
- ✅ 无论在哪个页面，收到 WebSocket 消息时都会打印 "111"
- ✅ 在应用启动时自动注册监听器
- ✅ 不受页面切换影响

### 2. 特定事件监听
- 当收到 `private_msg` 或 `chat_update` 事件时，额外打印 "111"
- 可以根据需要添加更多事件类型的监听

### 3. 调试功能
- 在调试页面可以发送测试消息
- 可以发送聊天测试消息来验证全局监听功能

## 测试方法

### 1. 使用调试页面测试
1. 打开消息页面，点击右上角"调试"按钮
2. 确保 WebSocket 连接成功
3. 点击"发送聊天测试消息"按钮
4. 查看浏览器控制台，应该看到 "111" 的打印

### 2. 模拟真实消息
```javascript
// 在浏览器控制台执行
import webSocketService from '@/utils/websocket.js'

// 模拟接收到聊天消息
webSocketService.handleMessage(JSON.stringify({
    event: 'private_msg',
    data: {
        to: 'user123',
        content: '测试消息',
        type: 'text',
        timestamp: Date.now()
    }
}))
```

## 扩展功能

### 1. 添加更多全局处理
```javascript
webSocketService.addGlobalListener((data) => {
    console.log('111'); // 基本打印
    
    // 根据消息类型做不同处理
    switch(data.event) {
        case 'private_msg':
            // 处理私聊消息
            break;
        case 'chat_update':
            // 处理聊天更新
            break;
        case 'system_notification':
            // 处理系统通知
            break;
    }
});
```

### 2. 页面级别的额外监听
```javascript
// 在具体页面中
export default {
    onLoad() {
        // 页面级别的监听（会在页面卸载时移除）
        webSocketService.on('private_msg', this.handlePrivateMessage)
    },
    
    onUnload() {
        // 清理页面级别的监听器
        webSocketService.off('private_msg', this.handlePrivateMessage)
    }
}
```

## 注意事项

1. **全局监听器不会自动清理**：全局监听器在整个应用生命周期内都存在
2. **避免重复注册**：确保不要多次调用 `addGlobalListener` 注册相同的监听器
3. **错误处理**：全局监听器中的错误不会影响其他监听器的执行
4. **性能考虑**：全局监听器会在每次收到消息时执行，避免在其中进行耗时操作

## 当前状态

- ✅ 全局监听器已实现
- ✅ 应用级别自动注册
- ✅ 调试工具已集成
- ✅ 无论在哪个页面都会打印 "111"

现在当 WebSocket 接收到任何消息时，都会在控制台打印 "111"，无论用户当前在哪个页面！
