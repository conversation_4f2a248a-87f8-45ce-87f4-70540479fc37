<template>
  <view class="container">
    <view class="header-section">
      <u-navbar height="44px" title="" :autoBack="true" :leftIconSize="30" :leftIconColor="'#333'" safeAreaInsetTop
        placeholder fixed>
        <view slot="right">
          <view class="navbar-actions">
            <view class="action-icon" @click="handleCollect(option)">
              <u-icon name="star" :color="isCollected ? '#FFD700' : '#666'" size="40"></u-icon>
            </view>
            <view class="action-icon" @click='clickShare()'>
              <u-icon name="share" color="#666" size="40"></u-icon>
            </view>
          </view>
        </view>
      </u-navbar>
    </view>
    <!-- 头部信息 -->
    <view class="header">
      <view class="title-section">
        <text class="job-title">{{ jobData.name }}</text>
        <text class="salary">{{ jobData.min_salary }}-{{ jobData.max_salary }}</text>
      </view>

      <view class="meta-info">
        <u-tag :text="jobData.address_name" size="mini" type="info" />
        <u-tag :text="jobData.experience" size="mini" type="info" />
        <u-tag :text="jobData.education" size="mini" type="warning" />
      </view>

      <view class="recruiter-info">
        <u-avatar size="40" src="/static/default-avatar.png"></u-avatar>
        <view class="recruiter-detail">
          <text class="name">{{ jobData.username }}</text>
          <text class="position">{{ jobData.job_position_name }}</text>
        </view>
      </view>
    </view>

    <!-- 分割线 -->
    <u-line color="#f5f5f5" margin="20rpx 0"></u-line>

    <!-- 职位详情 -->
    <view class="section">
      <text class="section-title">职位详情</text>
      <view class="job-info-skill">
        <view class="skills-container">
          <view
            v-for="(skill, index) in jobData.skill_name"
            :key="index"
            class="skill-tag"
          >
            <text class="skill-text">{{ skill }}</text>
          </view>
        </view>
      </view>
      <view class="job-desc">
        <text class="sub-title">职位描述：</text>
        <view class="desc-item">
          <text v-html="jobData.content"></text>
        </view>
        <!-- <view class="desc-item">
          <text>2.负责包装材料变更与验证、工艺标准制定与执行，并与其他职能团队密切合作，确保包装方案的顺利实施；</text>
        </view> -->
      </view>

      <view class="job-requirement">
        <text class="sub-title">任职需求：</text>
        <!-- <view class="requirement-item">
          <text>1.本科、硕士学历，包装设计、包装工程、工业设计</text>
        </view> -->
      </view>
    </view>

    <!-- 公司信息 -->
    <view class="section">
      <text class="section-title">公司信息</text>
      <view class="company-info">
        <text class="company-name">{{ jobData.company_name }}</text>
        <text class="company-detail">{{ jobData.size }}-{{ jobData.lthy }}</text>
      </view>

      <view class="company-locations">
        <view class="location-item">
          <u-icon name="map" size="16" color="#666"></u-icon>
          <text>{{ jobData.formatted_address }}</text>
        </view>
        <view class="map-container" >
          <map id="jobLocationMap" class="map" :show-location="false" :scale="scale" :latitude="jobData.lat"
            :longitude="jobData.lon" :markers="markers" :enable-zoom="true"
               enableScroll="false"
            style="width: 100%; height: 400rpx; border-radius: 16rpx;"></map>
          <!-- 中心点标记 -->
          <!-- <cover-view class="center-marker">
                <cover-view class="marker-icon">📍</cover-view>
            </cover-view> -->

            <!-- 定位按钮 - 使用cover-view在真机上显示 -->
            <!-- <cover-view class="location-btn" @tap="getCurrentLocation">
                <cover-image @tap="getCurrentLocation" style="width: 80%;height: 80%;" class="location-icon"
                    src="@/static/app/authentication/positioni.png"></cover-image>
            </cover-view> -->
        </view>
      </view>
    </view>

    <!-- 底部操作栏 -->
    <view class="footer">
      <!-- <u-button type="default" plain size="medium" @click="handleCollect">收藏</u-button> -->
      <u-button type="primary" size="medium" @click="handleApply">立即沟通</u-button>
    </view>

      <!-- uView分享面板 -->
       <u-popup :show="showShare" mode="bottom" :closeable="true" @close="showShare=false">
      <view class="action-sheet">
        <view 
          v-for="(item, index) in shareList" 
          :key="index" 
          class="action-item"
          @click="handleShare(index)"
        >
          <u-icon :name="item.icon" :color="item.color" size="28"></u-icon>
          <text>{{ item.text }}</text>
        </view>
      </view>
    </u-popup>
    <!-- <u-action-sheet :list="shareList" v-model="showShare" @click="shareClick"></u-action-sheet> -->
  </view>
</template>

<script>

import { collect, jobApi } from "@/utils/api"
export default {
  data() {
    return {
      id: '',
      jobData: {},
      showLocation: true,
      scale: 16,
      latitude: '', // 默认纬度（北京）
      longitude: '', // 默认经度（北京）
      markers: [], // 标记点
      isCollected: false,
      // 技能列表 - 可以从API获取或使用默认数据
      skillsList: ["React", "Vue", "HTML5", "JavaScript", "HTML", "CSS"],
      // 可以在这里定义数据
       showShare: false,
      shareList: [
        {
          text: '微信好友',
          icon: 'weixin-fill',
          color: '#09BB07'
        },
        {
          text: '微信朋友圈',
          icon: 'weixin-circle-fill',
          color: '#09BB07'
        },
        {
          text: 'QQ',
          icon: 'qq-fill',
          color: '#1E90FF'
        },
        {
          text: '微博',
          icon: 'weibo',
          color: '#FF0000'
        },
        {
          text: '复制链接',
          icon: 'file-copy-fill',
          color: '#909399'
        }
      ],
      shareInfo: {
        title: '分享标题',
        desc: '分享描述',
        link: 'https://www.yourdomain.com/share', // 分享链接
        imgUrl: 'https://www.yourdomain.com/logo.png' // 分享图标
      },
      mapScrollEnabled:false,
    }
  },
  
  onLoad(options) {
    // 在页面onLoad回调事件中通过this.id获取参数
    console.log(options.id, 'gfdgd');
    this.id = options.id;
    this.data();
    // this.getJobInfo()
  },

  methods: {
    clickShare(){
      this.showShare = true;
      const pages = getCurrentPages();
      if (pages.length === 0) return;
    
      // 当前页面实例
      const currentPage = pages[pages.length - 1];
    
      // 页面路径（补全前缀）
      const path = `/${currentPage.route}`;
    
      // 处理参数（编码特殊字符）
      const queryParams = currentPage.options || {};
      const queryString = Object.keys(queryParams)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(queryParams[key])}`)
        .join('&');
    
        // 完整地址
      const fullUrl = queryString ? `${path}?${queryString}` : path;
      console.log(fullUrl); // 输出：例如 "/pages/index/index?param=value"
    },
    //
    async data() {
      const user = uni.getStorageSync('userInfo');
      console.log(user);
      // 获取数据
      await jobApi.joblist({ job_id: this.id, user_id: user.id }).then(res => {
        uni.hideLoading();
        if (res.code == 200) {
          this.jobData = res.data;
          this.isCollected = res.data.is_job_collect == 1 ? true : false
          // 设置地图经纬度
          if (this.jobData.lat && this.jobData.lon) {
            console.log('经纬度', this.jobData.lat, this.jobData.lon);
            this.latitude = parseFloat(this.jobData.lat);
            this.latitude = parseFloat(this.jobData.lon);

            // 设置标记点
            // this.markers = [{
            //   iconPath: "../../static/shaixuan.png", // 需要准备一个图标文件
            //   id: 1,
            //   latitude: this.latitude,
            //   longitude: this.longitude,
            //   width: 50,
            //   height: 50,
            //   title: this.jobData.formatted_address || "工作地点",
            //   callout: {
            //     content: this.jobData.formatted_address || "工作地点",
            //     color: "#333",
            //     fontSize: 14,
            //     borderRadius: 4,
            //     bgColor: "#fff",
            //     padding: 5,
            //     display: "ALWAYS"
            //   }
            // }];

            // 调整地图视野到标记点
            this.updateMapRegion();
          }
        } else {
          uni.showToast({
            title: res.msg,
            icon: "none",
          });
        }
      })
    },
    updateMapRegion() {
      const that = this
      const mapCtx = uni.createMapContext('jobLocationMap', that);
      this.markers=[{
            id:1,
            latitude: '36.617012',
            longitude: '114.485689',
            iconPath: '/static/app/authentication/positioni.png',
            width: 20,
            height: 20
            
      }]
    //    mapCtx.onTouchStart((event) => {
    //     event.preventDefault(); // 阻止默认事件
    //     event.stopPropagation(); // 阻止事件冒泡
    // });
      // mapCtx.setCenter({
      //   latitude: this.latitude,
      //   longitude: this.longitude,
      // });
      mapCtx.moveToLocation({
        latitude: this.latitude,
        longitude: this.longitude
      });
      console.log('地图',mapCtx)
      // const list=
      mapCtx.addMarkers({
          markers: [{
            id:1,
            latitude: '36.617012',
            longitude: '114.485689',
            iconPath: '/static/app/authentication/positioni.png',
            width: 30,
            height: 30
            
      }],
          clear: false,
          success: function () {
            console.log('log添加成功');
          },
          fail: function () {
            console.log('err添加失败');
          }
      });

    },
    //分享
    share() {

    },
    //返回
    goBack() {
      uni.navigateBack({
        delta: 1 // 返回的页面数，这里设置为1表示返回上一页
      });
    },
    //收藏
    async handleCollect() {
      // 收藏逻辑
      this.isCollected = !this.isCollected;
      if (this.isCollected) {
        const params = {
          company_id: this.jobData.id,
          type: 3
        }
        await collect.collectCompany(params)
      } else {
        const params = {
          company_id: this.jobData.id,
          type: 3
        }
        await collect.collectCompanyDel(params)
      }
      uni.showToast({
        title: this.isCollected ? '已收藏' : '已取消收藏',
        icon: 'none',
        duration: 1500
      });
    },
    handleApply() {
      // 申请逻辑
      uni.showToast({
        title: '已发送沟通请求',
        icon: 'success'
      })
    },
    handleShare(index) {
      switch(index) {
        case 0:
          this.shareToWechat('WXSceneSession'); // 微信好友
          break;
        case 1:
          this.shareToWechat('WXSceneTimeline'); // 微信朋友圈
          break;
        case 2:
          this.shareToQQ(); // QQ
          break;
        case 3:
          this.shareToWeibo(); // 微博
          break;
        case 4:
          this.copyLink(); // 复制链接
          break;
      }
    },
    
    // 分享到微信
    shareToWechat(scene) {
      console.log('shareToWechat', scene);
      // #ifdef APP-PLUS
      console.log('plus',plus);
      plus.share.getServices(services => {
        let weixin = null;
        for (let i = 0; i < services.length; i++) {
          if (services[i].id === 'weixin') {
            weixin = services[i];
            break;
          }
        }
        if (weixin) {
          console.log('weixin', weixin);
          weixin.share({
            type: 0,
            pictures: [this.shareInfo.imgUrl],
            href: this.shareInfo.link,
            title: this.shareInfo.title,
            content: this.shareInfo.desc,
            extra: {
              scene: scene
            }
          }, function() {
            uni.showToast({
              title: '分享成功',
              icon: 'success'
            });
          }, function(err) {
            uni.showToast({
              title: '分享失败: ' + err.message,
              icon: 'none'
            });
          });
        } else {
          uni.showToast({
            title: '未检测到微信客户端',
            icon: 'none'
          });
        }
      }, err => {
        uni.showToast({
          title: '获取分享服务失败: ' + err.message,
          icon: 'none'
        });
      });
      console.log('app');
      // #endif
      
      // #ifdef MP-WEIXIN
      console.log(' MP-WEIXIN');
      uni.share({
        provider: 'weixin',
        scene: scene === 'WXSceneSession' ? 'WXSceneSession' : 'WXSceneTimeline',
        type: 0,
        title: this.shareInfo.title,
        summary: this.shareInfo.desc,
        href: this.shareInfo.link,
        imageUrl: this.shareInfo.imgUrl,
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          uni.showToast({
            title: '分享失败: ' + err.errMsg,
            icon: 'none'
          });
        }
      });
      // #endif
      //#ifdef H5
      console.log('H5');
      if (navigator.share) { // 检查是否支持 Web Share API
      try {
         navigator.share({
          title: this.shareInfo.title,
          text: this.shareInfo.desc,
          url: this.shareInfo.link
        });
        uni.showToast({ title: '分享成功', icon: 'success' });
      } catch (err) {
        uni.showToast({ title: '分享取消', icon: 'none' });
      }
    } else {
       this.copyLink(this.shareInfo.link); // 降级为复制链接
    }
    // #endif

    // #ifdef MP-WEIXIN
    uni.share({
      provider: 'weixin',
      type: 0,
      title: shareInfo.title,
      summary: shareInfo.desc,
      href: shareInfo.link,
      success: () => uni.showToast({ title: '分享成功', icon: 'success' }),
      fail: (err) => uni.showToast({ title: '分享失败: ' + err.errMsg, icon: 'none' })
    });
    // #endif
    },
    
    // 分享到QQ
    shareToQQ() {
      // #ifdef APP-PLUS
      plus.share.getServices(services => {
        let qq = null;
        for (let i = 0; i < services.length; i++) {
          if (services[i].id === 'qq') {
            qq = services[i];
            break;
          }
        }
        if (qq) {
          qq.share({
            type: 0,
            pictures: [this.shareInfo.imgUrl],
            href: this.shareInfo.link,
            title: this.shareInfo.title,
            content: this.shareInfo.desc
          }, function() {
            uni.showToast({
              title: '分享成功',
              icon: 'success'
            });
          }, function(err) {
            uni.showToast({
              title: '分享失败: ' + err.message,
              icon: 'none'
            });
          });
        } else {
          uni.showToast({
            title: '未检测到QQ客户端',
            icon: 'none'
          });
        }
      });
      // #endif
      
      // #ifdef H5 || MP-QQ
      uni.share({
        provider: 'qq',
        type: 0,
        title: this.shareInfo.title,
        summary: this.shareInfo.desc,
        href: this.shareInfo.link,
        imageUrl: this.shareInfo.imgUrl,
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          uni.showToast({
            title: '分享失败: ' + err.errMsg,
            icon: 'none'
          });
        }
      });
      // #endif
    },
    
    // 分享到微博
    shareToWeibo() {
      // #ifdef APP-PLUS
      plus.share.getServices(services => {
        let sina = null;
        for (let i = 0; i < services.length; i++) {
          if (services[i].id === 'sinaweibo') {
            sina = services[i];
            break;
          }
        }
        if (sina) {
          sina.share({
            type: 0,
            pictures: [this.shareInfo.imgUrl],
            href: this.shareInfo.link,
            title: this.shareInfo.title + ' ' + this.shareInfo.desc
          }, function() {
            uni.showToast({
              title: '分享成功',
              icon: 'success'
            });
          }, function(err) {
            uni.showToast({
              title: '分享失败: ' + err.message,
              icon: 'none'
            });
          });
        } else {
          uni.showToast({
            title: '未检测到微博客户端',
            icon: 'none'
          });
        }
      });
      // #endif
      
      // #ifdef H5
      uni.share({
        provider: 'sinaweibo',
        type: 0,
        title: this.shareInfo.title + ' ' + this.shareInfo.desc,
        href: this.shareInfo.link,
        imageUrl: this.shareInfo.imgUrl,
        success: () => {
          uni.showToast({
            title: '分享成功',
            icon: 'success'
          });
        },
        fail: (err) => {
          uni.showToast({
            title: '分享失败: ' + err.errMsg,
            icon: 'none'
          });
        }
      });
      // #endif
    },
    
    // 复制链接
    copyLink() {
      uni.setClipboardData({
        data: this.shareInfo.link,
        success: () => {
          uni.showToast({
            title: '链接已复制',
            icon: 'success'
          });
        },
        fail: () => {
          uni.showToast({
            title: '复制失败',
            icon: 'none'
          });
        }
      });
    }
    
  },
    // 页面生命周期 - 分享配置
  onShareAppMessage() {
    return {
      title: this.shareInfo.title,
      path: '/pages/index/index?id=123', // 小程序分享路径
      imageUrl: this.shareInfo.imgUrl,
      success: () => {
        uni.showToast({
          title: '分享成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        uni.showToast({
          title: '分享失败: ' + err.errMsg,
          icon: 'none'
        });
      }
    };
  },
}
</script>

<style lang="scss" scoped>
.container {
  padding: 20rpx 30rpx 100rpx;
  background-color: #fff;
}

.header-section {
  display: flex;
  justify-content: space-between;

  .navbar-actions {
    display: flex;
    align-items: center;
    gap: 20rpx;
    padding-right: 10rpx;

    .action-icon {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.9);
        background-color: rgba(255, 255, 255, 0.2);
      }
    }
  }


}

.header {
  .title-section {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;

    .job-title {
      font-size: 36rpx;
      font-weight: bold;
      margin-right: 20rpx;
    }

    .salary {
      font-size: 32rpx;
      color: #ff5a5f;
    }
  }

  .meta-info {
    margin-bottom: 30rpx;
    display: flex;

    ::v-deep .u-tag {
      margin-right: 15rpx;
    }
  }

  .recruiter-info {
    display: flex;
    align-items: center;

    .recruiter-detail {
      margin-left: 20rpx;
      display: flex;
      flex-direction: column;

      .name {
        font-size: 28rpx;
        font-weight: 500;
      }

      .position {
        font-size: 24rpx;
        color: #999;
      }
    }
  }
}

.section {
  margin-top: 30rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
  }

  .sub-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin: 20rpx 0 10rpx;
    display: block;
  }

  // 技能标签样式
  .job-info-skill {
    margin-bottom: 30rpx;
  }

  .skills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16rpx;
    margin-top: 20rpx;
  }

  .skill-tag {
    background: #f5f5f5;
    border-radius: 20rpx;
    padding: 12rpx 24rpx;
    border: 1rpx solid #e8e8e8;
  }

  .skill-text {
    font-size: 26rpx;
    color: #666;
    line-height: 1;
  }

  .desc-item,
  .requirement-item {
    font-size: 26rpx;
    color: #666;
    line-height: 1.6;
    margin-bottom: 10rpx;
  }

  .company-info {
    margin-bottom: 20rpx;

    .company-name {
      font-size: 28rpx;
      font-weight: 500;
      display: block;
      margin-bottom: 10rpx;
    }

    .company-detail {
      font-size: 24rpx;
      color: #999;
    }
  }

  .company-locations {
    .location-item {
      display: flex;
      align-items: center;
      padding: 15rpx 0;
      border-bottom: 1rpx solid #f5f5f5;

      &:last-child {
        border-bottom: none;
      }

      text {
        margin-left: 10rpx;
        font-size: 26rpx;
        color: #666;
      }
    }
  }
}

.section {
  margin-top: 30rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
  }
}

// 地图容器
.map-container {
  height: 300rpx;
  position: relative;

  &.with-search {
    height: calc(100vh - 500rpx);
  }

  .map {
    width: 100%;
    height: 100%;
  }

  // 中心点标记
  .center-marker {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 5;

    .marker-icon {
      font-size: 60rpx;
      color: #10d2c3;
      text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
    }
  }

  // 定位按钮
  .location-btn {
    position: absolute;
    right: 30rpx;
    bottom: 200rpx;
    width: 80rpx;
    height: 80rpx;
    background-color: #fff;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
    z-index: 5;
  }
}

.footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  padding: 15rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;

  ::v-deep .u-btn {
    flex: 1;
    margin: 0 10rpx;
  }
}

.action-sheet {
  padding: 40rpx;
  background-color: #fff;
  border-radius: 32rpx 32rpx 0 0;
}
.action-item {
  padding: 32rpx 0;
  display: flex;
  align-items: center;
  border-bottom: 1r solid #f5f5f5;
}
.action-item text {
  margin-left: 20rpx;
}
</style>