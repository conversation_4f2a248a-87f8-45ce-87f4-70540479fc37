<template>
  <view class="homepage-container">
    <u-navbar :autoBack="true" title="" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <!-- 顶部信息区 -->
    <view class="top-info" @click="openUserInfo(userInfo.user_id)">
      <view class="avatar-name">
        <u-avatar class="avatar" size="70" shape="circle" :src="userInfo.avatarUrl"></u-avatar>
        
        <view class="name-ip">
          <view class="name">{{ userInfo.username }}</view>
          <view class="ip">ip属地：{{ userInfo.cityName }}</view>
        </view>
      </view>
      <view class="follow-fans">
        <view style="width: 30%;">
          <view class="item">
            <view class="num">{{ userInfo.gznum }}</view>
            <view class="label">关注</view>
          </view>
          <view class="item">
            <view class="num">{{ userInfo.fsnum }}</view>
            <view class="label">粉丝</view>
          </view>
        </view>
        <view class="edit-btn"> 
          编辑资料
        </view>
        <!-- <u-button style="width:80px" color="#ddd" size="mini" shape="circle" @click="editProfile" >编辑资料</u-button> -->
        <!-- <button class="edit-btn" @click="editProfile" >编辑资料</button> -->
      </view>
      
    </view>

    <!-- 内容区 -->
    <view class="content-section">
      <view class="section-title">内容</view>
      <view class="post-list">
        <scroll-view class="list-container" scroll-y="true" refresher-enabled="true"
				:refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
				:lower-threshold="50" >

		    <view  v-for="(item, index) in postList" 
          :key="index" class="item-list">
          <view  class="cell-title">
            <view style="display: flex;">
              <u-avatar class="avatar" :src="item.avatarUrl"></u-avatar>
              <text class="username"> {{ item.username }} </text>
              
            </view>
            <text class="status"> {{ item.status==0?'待审核':item.status==1?'审核通过':'拒绝' }} </text>
            <!-- <view>
              <u-button color="#059f9f" size="mini" @click="follow(item)" shape="circle" :hairline="true" v-if="item.is_gz==0" >+ 关注</u-button>
              <u-button color="#ddd" size="mini" shape="circle" :hairline="false" v-else >已关注</u-button>
            </view> -->
          </view>
          
          <view @click="seeAll(item)">
                <!-- 内容部分 -->
                <view 
                  class="content"
                  :class="{'content-collapsed': !item.expanded && item.showExpand}"
                  
                >
                  <text>{{ item.content }}</text>
				          <text v-if="item.showExpand">...</text>
                </view>
                <view 
                  class="expand-qw" 
                  v-if="item.showExpand"
                >
                  <text>全文</text> 
                </view>
          </view>
            <view class="post-interaction">
            <u-icon name="thumb-up" size="36" color="#999" ></u-icon>
            <text class="interaction-num">{{ item.like_count }}</text>
            <u-icon name="chat" size="36" color="#999" ></u-icon>
            <text class="interaction-num">{{ item.comment_count }}</text>
            <u-icon name="share" size="36" color="#999" ></u-icon>
            <text class="interaction-num">{{ item.share_count }}</text>
        </view>
		</view>
	  </scroll-view>
       
      </view>
    </view>
    <u-icon name="plus-circle-fill" class="publish" @click="publish" size="80" color="#059f9f"></u-icon>
  </view>
</template>

<script>
import {communityApi } from '@/utils/api'
export default {
  data() {
    return {
      refreshing:false,
      userInfo: {
        
      },
      postList: []
    };
  },
  onLoad() { 
    this.getProfile();
  },
  methods: {
    //发布
    publish(){
      uni.navigateTo({
        url: '/pages/community/publish'
      })
    },
     openUserInfo(id){
        uni.navigateTo({
          url: '/pages/community/myinfo?id='+id
        });
    },
    //个人信息data
    async getProfile() {
      await communityApi.myCommunityArticle().then(res => {
            uni.hideLoading();
            res.data.data&&res.data.data.map(item => {
              item['expanded']=false;
              item['showExpand']= this.checkNeedExpand(item.content) // 是否需要显示展开按钮
              if(item.content.length > 75){
                item['content'] = item.content.substring(0, 67)
              }
              
            })
            this.posttotle=res.data.pageTotal;
            this.postList = [...this.postList,...res.data.data];
            this.userInfo={...res.data.userdata};
            // this.postList=res.data.data;
            // console.log(res.data.data[0],'获取信息成功')
            // this.comments=res.data.data[0].comments;
        //   this.info = res.data
        })
    },
    	// 检查是否需要显示展开按钮
    checkNeedExpand(content) {
      // 这里可以根据实际需求实现更精确的判断
      // 例如根据内容长度或行数

	  console.log('content',content.length);
      return content.length > 50; // 简单根据长度判断
    },
    // 编辑资料
    editProfile() {
      uni.showToast({ title: '编辑资料' });
    },
    // 关注
    follow(post) {
      post.isFollowed = true;
      uni.showToast({ title: '关注成功' });
    },
    seeAll(item){
      uni.navigateTo({
        url: '/pages/community/communityInfo?id=' + item.id
      });
    },
    
    // 显示更多
    toggleReadMore(index) {
      this.$set(this.postList[index], 'showFull', !this.postList[index].showFull);
    },
    	//下拉刷新
    onRefresh(){
      // if(this.currentTab == 1){ 
      //   this.followpage=1;
      // 	this.followListData()
      // }else{
      //   this.recommendpage=1;
      // 	this.getData()
      // }
    },
    //上拉加载
    onLoadMore(){
      // if(this.currentTab == 1){ 
      //   if(this.followtotle>this.followpage){
      //     this.followpage++;
      // 	  this.followListData()
      //   }
      // }else{
      //   if(this.recommendtotle>this.recommendpage){
      //     this.recommendpage++;
      // 	  this.getData()
      //   }
        
      // }
    },
  }
};
</script>

<style lang="scss" scoped>
.publish{
  position: fixed;
  bottom: 60rpx;
  right: 20rpx;
}
.homepage-container {
  padding: 20rpx;
  background-color: #fff;

  .top-info {
    // display: flex;
    // flex-direction: column;
    // align-items: center;
    margin-bottom: 20rpx;

    .avatar-name {
      display: flex;
      align-items: center;
      margin-bottom: 10rpx;

      .avatar {
        width: 120rpx;
        height: 120rpx;
        border-radius: 50%;
        margin-right: 20rpx;
      }

      .name-ip {
        .name {
          font-size: 32rpx;
          font-weight: bold;
        }

        .ip {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .follow-fans {
      display: flex;
      margin-bottom: 20rpx;
          align-items: center;
    justify-content: space-between;

      .item {
        // display: flex;
        // flex-direction: column;
        // align-items: center;
        display: inline-block;
        margin: 0 20rpx;

        .num {
          font-size: 32rpx;
          font-weight: bold;
          text-align: center;
        }

        .label {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .edit-btn {
      
     border-radius: 28rpx;
    border: 1rpx solid #ccc;
    font-size: 24rpx;
    color: #666;

    padding: 0px 12rpx;
    height: 40rpx;
    line-height: 40rpx;
    }
  }

  .content-section {
    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      margin-bottom: 20rpx;
    }

    .post-list {
      .list-container{
        height: calc(100vh - 220rpx);
      }
      
      .item-list{
        border: 1px solid #eee;
        margin-bottom: 20rpx;
        padding: 20rpx;
        border-radius: 20rpx;
        position: relative;
        .cell-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding-left: 20rpx;
          margin-bottom: 20rpx;
          .status{
            font-size: 24rpx;
            color: #666;
          }
        .avatar{
          margin-right: 16rpx;
        }
          .username {
            font-size: 32rpx;
            font-weight: bold;
            margin-right: 10rpx;
          }

          .company {
            font-size: 28rpx;
            color: #666;
          }
        }
        .content {
          font-size: 28rpx;
          color: #333;
          line-height: 1.6;
          word-break: break-word;
          
          &-collapsed {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3; /* 显示的行数 */
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .expand-qw{
          color: #14b19e;
            font-size: 26rpx;
          position: absolute;
          right: 94rpx;
          bottom: 80rpx;
          }
          .expand-btn {
            display: flex;
            align-items: center;
            justify-content: end;
            margin-top: 10rpx;
            
            
            text {
              margin-right: 8rpx;
            }
          }
        .post-header {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;

          .author-avatar {
            width: 40rpx;
            height: 40rpx;
            border-radius: 50%;
            margin-right: 10rpx;
          }

          .author-info {
            .author-name {
              font-size: 28rpx;
              font-weight: bold;
            }

            .author-position {
              font-size: 24rpx;
              color: #999;
            }
          }

          .follow-btn {
            padding: 10rpx 20rpx;
            border-radius: 30rpx;
            border: 1rpx solid #007aff;
            font-size: 24rpx;
            color: #007aff;
            margin-left: 20rpx;

            &.followed {
              border-color: #ccc;
              color: #ccc;
            }
          }
        }

        .post-content {
          font-size: 28rpx;
          line-height: 1.6;
          color: #333;

          .read-more {
            font-size: 24rpx;
            color: #007aff;
            text-align: right;
            margin-top: 10rpx;
          }
        }

        .post-interaction {
          display: flex;
          align-items: center;
          margin-top: 20rpx;

          .interaction-num {
            font-size: 24rpx;
            color: #999;
            margin: 0 10rpx;
          }
        }
      }
    }
  }
}
</style>


