<template>
  <view class="container">
    <u-navbar height="44px" title="选择资格证书" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
      :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed></u-navbar>
    
    <view class="content">
      <!-- 一级分类 -->
      <scroll-view scroll-y class="left-category" :style="{height: scrollHeight + 'px'}">
        <view 
          v-for="(item, index) in categoryData" 
          :key="item.id"
          class="category-item"
          :class="{active: currentFirst === index}"
          @click="selectFirst(index)"
        >
          {{item.name}}
        </view>
      </scroll-view>
      
      <!-- 二级分类 -->
      <scroll-view scroll-y class="middle-category" :style="{height: scrollHeight + 'px'}" 
      v-if="currentFirst !== null && categoryData[currentFirst] && categoryData[currentFirst].children">
        <view 
          v-for="(item, index) in categoryData[currentFirst].children" 
          :key="item.id"
          class="category-item"
          :class="{
            'has-children': hasChildren(item),
            'children-active': currentSecond === index && hasChildren(item),
            'selected-item': !hasChildren(item) && selectedCerts[item.id]
          }"
        >
          <view class="item-content">
            <text 
              class="item-text" 
              :class="{'selected-text': !hasChildren(item) && selectedCerts[item.id]}"
              @click="handleItemClick(item, index)"
            >
              {{item.name}}
            </text>
            <u-icon 
              v-if="hasChildren(item)" 
              name="arrow-right" 
              @click="selectSecond(index)"
            ></u-icon>
          </view>
        </view>
      </scroll-view>
      
      <!-- 三级分类 -->
      <scroll-view scroll-y class="right-category" :style="{height: scrollHeight + 'px'}" 
         v-if="showThirdCategory && categoryData[currentFirst] && 
              categoryData[currentFirst].children[currentSecond] &&
              categoryData[currentFirst].children[currentSecond].children">
        <view 
          v-for="(item, index) in categoryData[currentFirst].children[currentSecond].children" 
          :key="item.id"
          class="category-item"
          :class="{'selected-item': selectedCerts[item.id]}"
        >
          <view class="item-content">
            <text 
              class="item-text"
              :class="{'selected-text': selectedCerts[item.id]}"
              @click="handleThirdItemClick(item, index)"
            >
              {{item.name}}
            </text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 底部按钮 -->
    <view class="footer">
      <view class="selected-count">已选 {{selectedCount}} 项</view>
      <u-button type="primary" @click="confirmSelection">确定选择</u-button>
    </view>
  </view>
</template>

<script>
import { jobApi, userApi } from '@/utils/api.js'
export default {
  data() {
    return {
      scrollHeight: 0,
      currentFirst: 0, // 默认选中第一个一级分类
      currentSecond: null,
      currentThird: null,
      categoryData: [],
      selectedCerts: {}, // 存储选中的证书 {id: true/false}
      selectedCertInfos: {}, // 存储选中的证书详细信息 {id: {certInfo}}
      preSelectedIds: [] // 存储预先选中的证书ID
    }
  },
  computed: {
      showThirdCategory() {
      return this.currentFirst !== null && 
             this.currentSecond !== null && 
             this.categoryData[this.currentFirst] &&
             this.categoryData[this.currentFirst].children &&
             this.categoryData[this.currentFirst].children[this.currentSecond] &&
             this.hasChildren(this.categoryData[this.currentFirst].children[this.currentSecond]);
    },
    selectedCount() {
      return Object.values(this.selectedCerts).filter(v => v).length;
    }
  },

  async onLoad() {
    this.calcScrollHeight();
    await this.loadPreSelectedCerts(); // 先加载已选证书
    await this.loadAllCategoryData(); // 再加载分类数据
  },
  methods: {
    hasChildren(item) {
      // 判断是否有子分类（children存在且是数组且长度大于0）
      return item && Array.isArray(item.children) && item.children.length > 0;
    },
    calcScrollHeight() {
      const systemInfo = uni.getSystemInfoSync();
      this.scrollHeight = systemInfo.windowHeight - 50 - 70; // 减去导航栏和底部按钮高度
    },
    // 加载用户已选证书
    async loadPreSelectedCerts() {
      try {
       const userinfo= uni.getStorageSync('userInfo')
       console.log("用户信息",userinfo.certificate_label)
        // const res = await userApi.getUserInfo(); // 假设这个API返回用户信息，包含已选证书
        // uni.hideLoading();
        if (userinfo && userinfo.certificate_label) {
            this.preSelectedIds=userinfo.certificate_label;
        //   this.preSelectedIds = userinfo.certificate_label.split(',').map(id => id.trim());
          console.log('已选证书:', this.preSelectedIds);
        }
      } catch (error) {
        console.error('加载已选证书失败:', error);
      }
    },
    // 加载所有分类数据
    async loadAllCategoryData() {
      try {
        const res = await jobApi.cert();
        uni.hideLoading();
        this.categoryData = res.data;
        
        // 默认选中第一个一级分类的第一个二级分类（如果有）
        if (this.categoryData.length > 0 && 
            this.categoryData[0].children && 
            this.categoryData[0].children.length > 0) {
          this.currentFirst = 0;
        }
        
        // 回显已选证书
        this.markPreSelectedCerts();
      } catch (error) {
        console.error('加载分类数据失败:', error);
        uni.hideLoading();
      }
    },
    // 标记预先选中的证书
    markPreSelectedCerts() {
      if (!this.preSelectedIds || this.preSelectedIds.length === 0) return;
      
      // 遍历所有分类，标记已选证书
      this.preSelectedIds.forEach(id => {
        const certInfo = this.findCertInfo(id);
        if (certInfo) {
          this.$set(this.selectedCerts, id, true);
          this.$set(this.selectedCertInfos, id, certInfo);
        }
      });
      
      // 如果已选证书中有三级分类，自动展开对应的二级分类
      this.autoExpandForSelectedCerts();
    },
    // 自动展开已选证书所在的二级分类
    autoExpandForSelectedCerts() {
      if (this.preSelectedIds.length === 0 || !this.categoryData.length) return;
      
      // 查找第一个已选证书所在的二级分类
      for (const firstIndex in this.categoryData) {
        const firstItem = this.categoryData[firstIndex];
        if (firstItem&&firstItem.children&&!firstItem.children) continue;
        
        for (const secondIndex in firstItem.children) {
          const secondItem = firstItem.children[secondIndex];
          // 检查是否有已选的三级证书在这个二级分类下
          const hasSelectedChild = secondItem&&secondItem.children && 
            secondItem.children.some(child => this.preSelectedIds.includes(child.id.toString()));
          
          if (hasSelectedChild) {
            this.currentFirst = parseInt(firstIndex);
            this.currentSecond = parseInt(secondIndex);
            return;
          }
          
          // 或者这个二级分类本身被选中
          if (this.preSelectedIds.includes(secondItem.id.toString())) {
            this.currentFirst = parseInt(firstIndex);
            return;
          }
        }
      }
    },
    selectFirst(index) {
      if (this.currentFirst === index) return;
      this.currentFirst = index;
      this.currentSecond = null;
      this.currentThird = null;
    },
    selectSecond(index) {
      if (this.currentSecond === index) return;
      this.currentSecond = index;
      this.currentThird = null;
    },
    handleItemClick(item, index) {
      if (this.hasChildren(item)) {
        // 有子分类，展开子分类
        this.selectSecond(index);
      } else {
        // 没有子分类，切换选中状态
        this.toggleCertSelection(item.id);
      }
    },
    handleThirdItemClick(item, index) {
      // 三级分类点击，切换选中状态
      this.toggleCertSelection(item.id);
    },
    toggleCertSelection(id) {
      // 切换选中状态
      const newValue = !this.selectedCerts[id];
      this.$set(this.selectedCerts, id, newValue);
      
      // 查找证书信息并存储
      const certInfo = this.findCertInfo(id);
      if (certInfo) {
        if (newValue) {
          this.$set(this.selectedCertInfos, id, certInfo);
        } else {
          this.$delete(this.selectedCertInfos, id);
        }
      }
    },
    findCertInfo(id) {
      // 遍历三级结构查找证书信息
      for (const first of this.categoryData) {
        if (first.id == id) return null; // 一级分类不能选
        
        for (const second of first.children || []) {
          if (second.id == id) {
            return {
              id: second.id,
              name: second.name,
              level: 2
            };
          }
          
          for (const third of second.children || []) {
            if (third.id == id) {
              return {
                id: third.id,
                name: third.name,
                level: 3
              };
            }
          }
        }
      }
      return null;
    },
    async confirmSelection() {
      if (this.selectedCount === 0) {
        this.$u.toast('请至少选择一项资格证书');
        return;
      }
      
      // 获取所有选中的证书ID
      const selectedIds = Object.keys(this.selectedCerts).filter(id => this.selectedCerts[id]);
      
      try {
        await userApi.userSave({ certificate_label: selectedIds.join(',') });
        this.$u.toast('保存成功');
        const userinnf = uni.getStorageSync('userInfo') || {};
        userinnf.certificate_label = selectedIds;
        uni.setStorageSync('userInfo', userinnf);
        uni.navigateBack();
      } catch (error) {
        console.error('保存失败:', error);
        this.$u.toast('保存失败，请重试');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.left-category, .middle-category, .right-category {
  width: 33.33%;
  background-color: #f7f7f7;
}

.category-item {
  padding: 15rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #eee;
  position: relative;
  
  &.active {
    background-color: #fff;
    color: #2979ff;
    font-weight: bold;
  }
  
  &.has-children {
    .item-content {
      justify-content: space-between;
    }
  }
  
  &.children-active {
    background-color: #e6f0ff;
    color: #2979ff;
  }
  
  &.selected-item {
    background-color: #e6f0ff;
  }
}

.item-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.item-text {
  margin-left: 10rpx;
  flex: 1;
  padding: 10rpx 0;
  
  &.selected-text {
    color: #2979ff;
    font-weight: bold;
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  
  .selected-count {
    font-size: 26rpx;
    color: #666;
  }
}
</style>