{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__E0574AE", "name": "容翼聘", "version": {"name": "1.1.3", "code": 400}, "description": "云端一体应用快速开发基本项目模版", "launch_path": "__uniappview.html", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Geolocation": {}, "Maps": {"coordType": "gcj02"}, "Payment": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"target": "id:1", "autoclose": true, "waiting": true, "delay": 0}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#FFFFFF"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "distribute": {"google": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>"]}, "apple": {"dSYMs": false, "privacyDescription": {"NSPhotoLibraryUsageDescription": "需要访问相册以选择文件"}}, "plugins": {"push": {"unipush": null}, "geolocation": {"tencent": {"__platform__": ["ios", "android"], "apikey_ios": "FEVBZ-S3ARJ-SKRFR-XDQ7W-BFS52-R6BFA", "apikey_android": "FEVBZ-S3ARJ-SKRFR-XDQ7W-BFS52-R6BFA"}, "system": {"__platform__": ["ios", "android"]}}, "maps": {"amap": {"name": "com.rongyipinapp.standardsdk", "appkey_ios": "80a5cc32c09b83a60cb10a8c12a6d5a3", "appkey_android": "80a5cc32c09b83a60cb10a8c12a6d5a3"}}, "payment": {"alipay": {"__platform__": ["ios", "android"]}}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}}}, "uniStatistics": {"enable": false}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#FFFFFF", "bottom": {"offset": "auto"}}, "uni-app": {"compilerVersion": "4.75", "control": "uni-v3", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "normal"}, "tabBar": {"color": "#969ca1", "selectedColor": "#15CD7B", "borderStyle": "rgba(0,0,0,0.4)", "list": [{"pagePath": "pages/index/index", "text": "首页", "iconPath": "static/tabbar/home.png", "selectedIconPath": "static/tabbar/home-active.png"}, {"pagePath": "pages/community/community", "text": "社区", "iconPath": "static/tabbar/square.png", "selectedIconPath": "static/tabbar/square-active.png"}, {"pagePath": "pages/earnMoney/earnMoney", "text": "赚现金", "iconPath": "static/tabbar/message.png", "selectedIconPath": "static/tabbar/message-active.png"}, {"pagePath": "pages/message/message", "text": "消息", "iconPath": "static/tabbar/message.png", "selectedIconPath": "static/tabbar/message-active.png"}, {"pagePath": "pages/user/user", "text": "我的", "iconPath": "static/tabbar/mine.png", "selectedIconPath": "static/tabbar/mine-active.png"}], "height": "50px"}, "launch_path": "__uniappview.html"}}