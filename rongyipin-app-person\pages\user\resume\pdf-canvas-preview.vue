<template>
    <view class="pdf-preview-page">
        <!-- 顶部导航栏 -->
        <u-navbar height="44px" :title="fileName || 'PDF预览'" :autoBack="true" :leftIconSize="30"
            :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            <template #right>
                <view class="navbar-actions">
                    <u-icon name="download" size="20" color="#333" @click="downloadFile" style="margin-right: 15px;"></u-icon>
                    <u-icon name="more-dot-fill" size="20" color="#333" @click="showMoreActions"></u-icon>
                </view>
            </template>
        </u-navbar>

        <!-- PDF Canvas查看器 -->
        <view class="pdf-container">
            <pdf-canvas-viewer 
                :pdfUrl="pdfUrl"
                :showToolbar="true"
                :initialScale="1"
                :minScale="0.5"
                :maxScale="3"
                @pageChange="onPageChange"
                @scaleChange="onScaleChange"
                @error="onError"
            ></pdf-canvas-viewer>
        </view>

        <!-- 更多操作弹窗 -->
        <u-popup v-model="showMorePopup" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
            <view class="more-actions-popup">
                <view class="popup-header">
                    <text class="popup-title">更多操作</text>
                    <u-icon name="close" size="20" color="#666" @click="showMorePopup = false"></u-icon>
                </view>
                <view class="action-list">
                    <view class="action-item" @click="downloadFile">
                        <u-icon name="download" size="24" color="#333"></u-icon>
                        <text class="action-text">下载文件</text>
                    </view>
                    <view class="action-item" @click="shareFile">
                        <u-icon name="share" size="24" color="#333"></u-icon>
                        <text class="action-text">分享文件</text>
                    </view>
                    <view class="action-item" @click="openWithBrowser">
                        <u-icon name="link" size="24" color="#333"></u-icon>
                        <text class="action-text">浏览器打开</text>
                    </view>
                    <view class="action-item" @click="copyLink">
                        <u-icon name="copy" size="24" color="#333"></u-icon>
                        <text class="action-text">复制链接</text>
                    </view>
                </view>
            </view>
        </u-popup>

        <!-- 加载PDF.js库 (H5端) -->
        <!-- #ifdef H5 -->
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
        <!-- #endif -->
    </view>
</template>

<script>
import PdfCanvasViewer from '@/components/pdf-canvas-viewer/pdf-canvas-viewer.vue'

export default {
    components: {
        PdfCanvasViewer
    },
    data() {
        return {
            pdfUrl: '',
            fileName: '',
            showMorePopup: false,
            currentPage: 1,
            totalPages: 0,
            currentScale: 1
        }
    },
    onLoad(options) {
        console.log('页面参数:', options)
        
        try {
            if (options.url) {
                this.pdfUrl = decodeURIComponent(options.url)
            }
            if (options.name) {
                this.fileName = decodeURIComponent(options.name)
            }
            
            // 如果是从简历列表传递的参数
            if (options.item) {
                const item = JSON.parse(decodeURIComponent(options.item))
                this.pdfUrl = item.storage_path
                this.fileName = item.attachment_name
            }
            
            console.log('PDF信息:', { url: this.pdfUrl, name: this.fileName })
            
        } catch (error) {
            console.error('参数解析失败:', error)
            uni.showToast({
                title: '参数错误',
                icon: 'none'
            })
        }
    },
    onReady() {
        // #ifdef H5
        // 确保PDF.js库已加载
        this.ensurePDFJSLoaded()
        // #endif
    },
    methods: {
        // 确保PDF.js库已加载 (H5端)
        ensurePDFJSLoaded() {
            // #ifdef H5
            if (typeof pdfjsLib === 'undefined') {
                console.log('PDF.js库未加载，正在加载...')
                
                const script = document.createElement('script')
                script.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js'
                script.onload = () => {
                    console.log('PDF.js库加载完成')
                    // 设置PDF.js工作路径
                    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
                }
                script.onerror = () => {
                    console.error('PDF.js库加载失败')
                    uni.showToast({
                        title: 'PDF库加载失败',
                        icon: 'none'
                    })
                }
                document.head.appendChild(script)
            } else {
                // 设置PDF.js工作路径
                pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
            }
            // #endif
        },

        // 页面变化回调
        onPageChange(page, total) {
            this.currentPage = page
            this.totalPages = total
            console.log(`当前页面: ${page}/${total}`)
        },

        // 缩放变化回调
        onScaleChange(scale) {
            this.currentScale = scale
            console.log(`当前缩放: ${Math.round(scale * 100)}%`)
        },

        // 错误回调
        onError(error) {
            console.error('PDF查看器错误:', error)
            uni.showToast({
                title: error.message || 'PDF加载失败',
                icon: 'none'
            })
        },

        // 显示更多操作
        showMoreActions() {
            this.showMorePopup = true
        },

        // 下载文件
        async downloadFile() {
            console.log('开始下载文件')
            
            // #ifdef APP-PLUS
            await this.downloadForApp()
            // #endif

            // #ifdef H5
            this.downloadForH5()
            // #endif

            // #ifdef MP-WEIXIN
            this.downloadForWeixin()
            // #endif
            
            this.showMorePopup = false
        },

        // APP端下载
        async downloadForApp() {
            uni.showLoading({ title: '下载中...' })
            
            try {
                const downloadTask = uni.downloadFile({
                    url: this.pdfUrl,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            // 下载成功，打开文件
                            uni.openDocument({
                                filePath: res.tempFilePath,
                                showMenu: true,
                                success: () => {
                                    console.log('文件打开成功')
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '文件已打开',
                                        icon: 'success'
                                    })
                                },
                                fail: (error) => {
                                    console.error('文件打开失败:', error)
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '无法打开文件',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    },
                    fail: (error) => {
                        console.error('下载失败:', error)
                        uni.hideLoading()
                        uni.showToast({
                            title: '下载失败',
                            icon: 'none'
                        })
                    }
                })

                // 监听下载进度
                downloadTask.onProgressUpdate((res) => {
                    console.log('下载进度:', res.progress)
                    uni.showLoading({ title: `下载中...${res.progress}%` })
                })

            } catch (error) {
                console.error('下载异常:', error)
                uni.hideLoading()
                uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                })
            }
        },

        // H5端下载
        downloadForH5() {
            try {
                const link = document.createElement('a')
                link.href = this.pdfUrl
                link.download = this.fileName || 'document.pdf'
                link.target = '_blank'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
                
                uni.showToast({
                    title: '开始下载',
                    icon: 'success'
                })
            } catch (error) {
                console.error('H5下载失败:', error)
                uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                })
            }
        },

        // 微信小程序下载
        downloadForWeixin() {
            uni.showToast({
                title: '请在浏览器中下载',
                icon: 'none'
            })
        },

        // 分享文件
        shareFile() {
            // #ifdef APP-PLUS
            uni.share({
                provider: 'system',
                type: 0,
                href: this.pdfUrl,
                title: this.fileName || 'PDF文档',
                success: () => {
                    console.log('分享成功')
                },
                fail: (error) => {
                    console.error('分享失败:', error)
                }
            })
            // #endif

            // #ifdef H5
            if (navigator.share) {
                navigator.share({
                    title: this.fileName || 'PDF文档',
                    url: this.pdfUrl
                })
            } else {
                this.copyLink()
            }
            // #endif

            // #ifdef MP-WEIXIN
            uni.showToast({
                title: '暂不支持分享',
                icon: 'none'
            })
            // #endif

            this.showMorePopup = false
        },

        // 浏览器打开
        openWithBrowser() {
            // #ifdef APP-PLUS
            plus.runtime.openURL(this.pdfUrl)
            // #endif

            // #ifdef H5
            window.open(this.pdfUrl, '_blank')
            // #endif

            // #ifdef MP-WEIXIN
            uni.showToast({
                title: '请复制链接到浏览器打开',
                icon: 'none'
            })
            // #endif

            this.showMorePopup = false
        },

        // 复制链接
        copyLink() {
            // #ifdef H5
            if (navigator.clipboard) {
                navigator.clipboard.writeText(this.pdfUrl).then(() => {
                    uni.showToast({
                        title: '链接已复制',
                        icon: 'success'
                    })
                })
            }
            // #endif

            // #ifdef APP-PLUS
            uni.setClipboardData({
                data: this.pdfUrl,
                success: () => {
                    uni.showToast({
                        title: '链接已复制',
                        icon: 'success'
                    })
                }
            })
            // #endif

            // #ifdef MP-WEIXIN
            uni.setClipboardData({
                data: this.pdfUrl,
                success: () => {
                    uni.showToast({
                        title: '链接已复制',
                        icon: 'success'
                    })
                }
            })
            // #endif

            this.showMorePopup = false
        }
    }
}
</script>

<style scoped>
.pdf-preview-page {
    height: 100vh;
    background-color: #f5f5f5;
}

.navbar-actions {
    display: flex;
    align-items: center;
    padding-right: 15px;
}

.pdf-container {
    height: calc(100vh - 88rpx);
    width: 100%;
}

/* 更多操作弹窗 */
.more-actions-popup {
    padding: 20px 0;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.popup-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.action-list {
    padding-top: 10px;
}

.action-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    transition: background-color 0.3s ease;
}

.action-item:active {
    background-color: #f8f9fa;
}

.action-text {
    font-size: 16px;
    color: #333;
    margin-left: 15px;
}
</style>
