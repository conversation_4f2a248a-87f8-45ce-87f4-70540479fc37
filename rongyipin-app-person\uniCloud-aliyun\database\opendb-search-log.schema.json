{"bsonType": "object", "permission": {"create": true, "delete": false, "read": false, "update": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "content": {"bsonType": "string", "description": "搜索内容"}, "create_date": {"bsonType": "timestamp", "description": "统计时间"}, "device_id": {"bsonType": "string", "description": "设备id"}, "user_id": {"bsonType": "string", "description": "收藏者id，参考uni-id-users表"}}, "required": ["content"]}