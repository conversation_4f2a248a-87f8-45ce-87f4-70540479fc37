<template>
	<view class="person-container">
		<view>
			<u-icon name="arrow-left" class="back-icon" color="#2a9938" size="28"></u-icon>
			<div class="login-top">
				<div class="login-top-img">
					<img src="../../static/logo.png" mode=""></img>
				</div>
				<div>
					<view class="top_jl_box">
							<div>立即登录，解锁高薪兼职</div>
					</view>
					<view class="login-top-tip">
							未注册过的手机号将自动创建账号
						</view>
				</div>
			</div>

		</view>
		<view class="login-form">
			<view class="form-container">
				<!-- Account Login -->
				<view v-if="activeTab === 'account'" class="form account-form">
					<uni-forms label-position="top">
						<uni-forms-item label="账号" name="name">
							<uni-easyinput class="input-field" type="text" v-model="accountForm.username"
								placeholder="请输入账号/手机号" />
						</uni-forms-item>
						<uni-forms-item name="password" label="密码">
							<view class="password-field">
								<input class="input-field" placeholder="请输入密码" :password="!showPassword"
									v-model="accountForm.password" />
								<view class="icon-wrapper" @click="changePassword">
									<uni-icons :type="showPassword ? 'eye' : 'eye-slash'" size="20"
										:color="showPassword ? '#2a9938' : '#999'" />
								</view>
								<text class="forgot" @click="handleForgotPassword">忘记密码?</text>
							</view>
						</uni-forms-item>
					</uni-forms>
					<u-checkbox-group v-model="checkvalue">
						<u-checkbox name="agreement" shape="square" active-color="#2a9938" size="30" label-size="25"
							label-color="#666" label="阅读并同意用户协议、隐私协议及处罚规则"></u-checkbox>
					</u-checkbox-group>
					<button class="login-btn" :disabled="!isAccountFormValid"
						:class="{ 'login-btn-disabled': !isAccountFormValid }" @click="handleAccountLogin">立即登录</button>
				</view>
				<!-- Phone Login -->
				<view v-if="activeTab === 'phone'" class="form phone-form">
					<uni-forms label-position="top">
						<!-- <uni-forms-item label="账号" name="name">
							<uni-easyinput 
								class="input-field" 
								type="text" 
								v-model="accountForm.username"
								placeholder="请输入账号/手机号" />
						</uni-forms-item> -->
						<uni-forms-item label="手机号" name="phone">
							<uni-easyinput class="input-field" type="number" v-model="phoneForm.phone"
								placeholder="请输入手机号" @input="handlePhoneInput" :maxlength="11" />
						</uni-forms-item>
					</uni-forms>

					<view class="input-group code-group">
						<input type="text" v-model="phoneForm.code" placeholder="请输入验证码" class="input code-input" />
						<button class="code-btn" :disabled="countdown > 0"
							:class="{ 'code-btn-disabled': countdown > 0 }" @click="sendCode">
							{{ countdown > 0 ? `${countdown}s 后重试` : '获取验证码' }}
						</button>
					</view>
					<u-checkbox-group v-model="checkvalue">
						<u-checkbox name="agreement" shape="square" active-color="#2a9938" size="30" label-size="25"
							label-color="#666" label="阅读并同意用户协议、隐私协议及处罚规则"></u-checkbox>
					</u-checkbox-group>
					<button class="login-btn" :disabled="!isPhoneFormValid"
						:class="{ 'login-btn-disabled': !isPhoneFormValid }" @click="handlePhoneLogin">立即登录</button>
				</view>
			</view>
			<view class="tab-bar">
				<view v-if="activeTab === 'phone'" :class="['tab', { active: activeTab === 'account' }]"
					@click="switchTab('account')">
					账户登录
				</view>
				<view v-else :class="['tab', { active: activeTab === 'phone' }]" @click="switchTab('phone')">
					验证码登录
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { setStorage } from '../../utils/storage'
import { generateToken } from '../../utils/mock'
import { userApi, priseApi } from '../../utils/api'
export default {
	data() {
		return {
			activeTab: 'account',
			showPassword: false,
			accountForm: {
				username: '',
				password: '',
			},
			phoneForm: {
				phone: '',
				code: '',
			},
			countdown: 0,
			images: [
				'https://images.unsplash.com/photo-*************-b723cf961d3e',
				'https://images.unsplash.com/photo-*************-675f0ddb6308',
				'https://images.unsplash.com/photo-*************-d120267933ba'
			],
			currentIndex: 0,
			checkvalue: []
		}
	},
	computed: {
		isAccountFormValid() {
			return this.accountForm.username &&
				this.accountForm.password;
		},

		isPhoneFormValid() {
			return this.phoneForm.phone && this.phoneForm.code;
		}
	},
	methods: {
		switchTab(tab) {
			this.activeTab = tab
			this.accountForm = { username: '', password: '' }
			this.phoneForm = { phone: '', code: '' }
		},
		async sendCode() {
			const phoneRegex = /^1[3-9]\d{9}$/
			const purePhone = this.phoneForm.phone.trim().replace(/\D/g, '');
			if (!phoneRegex.test(purePhone)) {
				uni.showToast({ title: '请输入有效手机号', icon: 'none' })
				return
			}
			this.countdown = 60

			// 模拟发送验证码的 API 调用
			try {
				let res = await userApi.Captcha({ phone: this.phoneForm.phone, event: 'joblogin' })

				if (res.code == 200) {
					uni.showToast({ title: '验证码已发送', icon: 'success' })
					const timer = setInterval(() => {
						this.countdown--
						if (this.countdown <= 0) {
							clearInterval(timer)
						}
					}, 1000)
				} else {
					uni.showToast({ title: res.msg, icon: 'none' })
					this.countdown = 0
				}

			} catch (error) {
				uni.showToast({ title: '验证码发送失败', icon: 'none' })
				return
			}

		},
		handlePhoneInput(value) {
			const digitsOnly = value.replace(/[^0-9]/g, '').slice(0, 11)
			if (value !== digitsOnly) {
				this.phoneForm.phone = digitsOnly
				uni.showToast({ title: '只能输入数字', icon: 'none' })
			}
		},
		async handleAccountLogin() {
			if (!this.accountForm.username || !this.accountForm.password) {
				uni.showToast({ title: '请输入用户名和密码', icon: 'none' })
				return
			}

			if (this.checkvalue.length === 0) {
				uni.showModal({
					title: '温馨提示',
					content: '请阅读并同意用户协议、隐私协议及处罚规则',
					showCancel: true,
					cancelText: '取消',
					confirmText: '确定',
					success: (res) => {
						if (res.confirm) {
							this.checkvalue = ['agreement']
						}
					}
				})
				return
			}

			try {
				uni.showLoading({ title: '登录中...' })
				let res = await userApi.login({
					username: this.accountForm.username,
					password: this.accountForm.password,
					logintype: 2
				})
				if (res.code == 200) {
					const token = res.data.token
					uni.setStorageSync('token', token)
					uni.showToast({ title: '登录成功', icon: 'success' })
					let reslate = await priseApi.getCompanyInfoe()

					if (reslate.code == 200) {
						uni.setStorageSync('is_auth', reslate.data.is_auth)
						if (reslate.data.is_auth == 0) {
							uni.navigateTo({ url: '/pages/audits/index' })
						} else if (reslate.data.is_auth == 1) {
							uni.navigateTo({ url: '/pages/second/second' })
						} else {
							uni.navigateTo({ url: '/pages/authentication/index' })
						}
					} else if (reslate.code == 404) {
						uni.showToast({ title: res.msg })
						uni.navigateTo({ url: '/pages/authentication/index' })
					} else if (reslate.code == 406) {
						uni.showToast({ title: res.msg })
						uni.navigateTo({ url: '/pages/audits/index' })
					}else if(reslate.code == 405){
						uni.showToast({ title: res.msg })
						uni.navigateTo({ url: '/pages/authentication/index' })
					}
				} else {
					uni.showToast({ title: res.msg, icon: 'none' })
					uni.hideLoading()
					return
				}
			} catch (error) {
				uni.hideLoading()
				uni.showToast({ title: error.msg, icon: 'none' })
			}
		},
		async handlePhoneLogin() {
			const phoneRegex = /^1[3-9]\d{9}$/
			if (!phoneRegex.test(this.phoneForm.phone)) {
				uni.showToast({ title: '请输入有效手机号', icon: 'none' })
				return
			}
			if (!this.phoneForm.code) {
				uni.showToast({ title: '请输入验证码', icon: 'none' })
				return
			}

			if (this.checkvalue.length === 0) {
				uni.showModal({
					title: '温馨提示',
					content: '请阅读并同意用户协议、隐私协议及处罚规则',
					showCancel: true,
					cancelText: '取消',
					confirmText: '确定',
					success: (res) => {
						if (res.confirm) {
							this.checkvalue = ['agreement']
						}
					}
				})
				return
			}

			try {
				uni.showLoading({ title: '登录中...' })
				let res = await userApi.login({
					mobile: this.phoneForm.phone,
					code: this.phoneForm.code,
					logintype: 1
				})
				console.log(res, '##')
				if (res.code == 200) {
					const token = res.data.token
					uni.setStorageSync('token', token)
					uni.showToast({ title: '登录成功', icon: 'success' })
					setTimeout(() => {
							uni.reLaunch({
								url: "/pages/user/user"
							})
						}, 100)

				} else {
					uni.showToast({ title: res.msg, icon: 'none' })
					return
				}
			} catch (error) {
				uni.hideLoading()
				uni.showToast({ title: error.msg, icon: 'none' })
			}
		},
		handleSwiperChange(e) {
			this.currentIndex = e.detail.current
		},
		changePassword() {
			this.showPassword = !this.showPassword
		},
		handleForgotPassword() {
			
			uni.navigateTo({ url: './loginres' })
			console.log("dhfjhsfjdshfj")
		}
	}
}
</script>

<style lang="scss">
.person-container{
	height: 100vh;
	
}
.back-icon{
	height: 40px;
	padding-left:20px ;
}
.login-top{
	display:flex;
	height: 100px;
    margin: 20px;
    justify-content: center;
	.login-top-img{
		width: 20%;
		img{
			width: 50px;
			border-radius: 30px;
		}
	}
	.top_jl_box {
		display: flex;
		align-items: center;
		font-weight: 800;
		font-size: 36rpx;
	}

	.top_jl {
		margin-top: 20rpx;
	}
	.login-top-tip{
		margin-top: 5px;
		font-size: 12px;
		color: #8f8f8f;
	}
}
::v-deep .uniui-clear::before{
color: #2a9938 !important;

}
::v-deep .uni-easyinput__content {
	height: 100%;
}

.forgot {
	font-size: 24rpx;
	color: #2a9938;
	text-align: right;
	margin: 20rpx 0;
	cursor: pointer;
	transition: opacity 0.3s;
}

.forgot:hover {
	opacity: 0.8;
}

::v-deep .uni-easyinput__content-input {
	border: 0px solid #F0F0F0 !important;
	background: #f9f9f9 !important;
	font-size: 22rpx !important;
	font-weight: 600 !important;
	font-family: monospace !important;
}

.input-wrapper {
	position: relative;
	//   width: 100%;

}

.password-input {
	border: 2rpx solid #f2f2f2;
	padding: 11rpx;
	border-radius: 10rpx;
	font-size: 24rpx;
	background: #f9f9f9;
	// border: 1px solid #F0F0F0;
}

.icon-wrapper {
	position: absolute;
	right: 20rpx;
	top: 50%;
	transform: translateY(-50%);
	z-index: 2;
}

.home-container {
	/* padding: 40rpx; */
	width: 100%;
	height: 300rpx;
	/* background: orange; */
	/* display: flex;
  flex-direction: column;
  align-items: center; */
}

.carousel-container {
	width: 100%;
	/* max-width: 700rpx; */
	height: 100%;
	position: relative;
	margin-bottom: 40rpx;
}

.swiper {
	width: 100%;
	height: 100%;
	/* border-radius: 20rpx; */
	overflow: hidden;
	box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.2);
}

.swiper-image {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.tab-barlate {
	position: absolute;
	bottom: 20rpx;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	gap: 16rpx;
}

.login-form {
	width: 100%;
	height: 100%;
	border-radius: 10px 10px 0px 0px;
	background-color: white;
	margin-top: -10px;
	z-index: 2;
}

.tab-bar {
	position: absolute;
	bottom: 100rpx;
	left: 50%;
	transform: translateX(-50%);
	display: flex;
	gap: 16rpx;
	margin-top: 30rpx;
}

.tab-dot {
	width: 16rpx;
	height: 16rpx;
	border-radius: 50%;
	background: #ccc;
	transition: background 0.3s;
	align-items: center;
}

.tab-dot.active {
	background: #000;
}

.nav-btn {
	background: linear-gradient(90deg, #007aff, #00c6ff);
	color: #fff;
	font-size: 32rpx;
	padding: 20rpx 40rpx;
	border-radius: 10rpx;
	transition: transform 0.2s;
}

.nav-btn:hover {
	transform: scale(1.05);
}

.login-container {
	/* padding: 60rpx 40rpx; */
	height: 100vh;
	background-color: #2a9938;
	display: flex;
	flex-direction: column;
	align-items: center;
}

.tab-barlate {
	display: flex;
	width: 10%;
	align-items: center;
	/* max-width: 600rpx; */
	/* background: #fff;
	border-radius: 20rpx 20rpx 0 0; */
	overflow: hidden;
}

.tab {
	flex: 1;
	padding: 20rpx;
	text-align: center;
	font-size: 32rpx;
	color: #666;
	transition: all 0.3s;
}

.tab.active {
	color: #007aff;
	background: #e6f0ff;
}

.form-container {
	// width: 100%;
	// max-width: 100%;
	background: #fff;
	padding: 48rpx 40rpx;
	border-radius: 32rpx 32rpx 0 0;
	box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
	margin-top: -40rpx;
	position: relative;
	z-index: 2;
}

.form {
	opacity: 0;
	animation: fadeIn 0.5s forwards;
}

@keyframes fadeIn {
	to {
		opacity: 1;
	}
}

.input-group {
	margin-bottom: 30rpx;
}

.input {
	width: 70%;
	height: 32px;
	padding: 11rpx 11rpx 11rpx 24rpx;
	border-radius: 10rpx;
	font-size: 24rpx;
	background: #f9f9f9;
	border: 1px solid #F0F0F0;
}

.code-group {
	display: flex;
	align-items: center;
}

.code-input {
	flex: 1;
	margin-right: 20rpx;
}

.code-btn {
	background: #2a9938;
	color: #fff;
	font-size: 28rpx;
	padding: 0rpx 14rpx;
	border-radius: 10rpx;
	height: 41px;
	line-height: 41px;
}

.code-btn:disabled {
	background: #ccc;
}

.login-btn {
	width: 100%;
	height: 88rpx;
	background: linear-gradient(135deg, #2a9938 0%, #2a9938 100%);
	border-radius: 44rpx;
	font-size: 32rpx;
	color: #fff;
	border: none;
	margin-top: 60rpx;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 16rpx rgba(16, 210, 58, 0.2);

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 8rpx rgba(16, 210, 58, 0.2);
	}

	&.login-btn-disabled {
		background: #ccc;
		box-shadow: none;
		cursor: not-allowed;
	}
}

.input-field {
	// width: 100%;
	height: 88rpx;
	background: #f8f9fa;
	border: 2rpx solid #eee;
	border-radius: 16rpx;
	// padding: 0 32rpx;
	font-size: 28rpx;
	color: #333;
	transition: all 0.3s ease;
	padding-left: 10rpx;

	&:focus {
		border-color: #2a9938;
		background: #fff;
		box-shadow: 0 0 0 2rpx rgba(16, 210, 195, 0.1);
	}

	&::placeholder {
		color: #999;
	}
}

.password-field {
	position: relative;
	width: 100%;

	.input-field {
		padding-right: 80rpx !important;
		padding-left: 20rpx;
	}

	.icon-wrapper {
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		padding: 20rpx;
		z-index: 1;
	}

	.forgot {
		position: absolute;
		right: 32rpx;
		top: -85%;
		transform: translateY(-50%);
		font-size: 24rpx;
		// color: #2a9938;
		z-index: 1;
	}
}

::v-deep .uni-forms-item__label {
	font-size: 28rpx;
	color: #333;
	margin-bottom: 16rpx;
	font-weight: 500;
}

::v-deep .uni-easyinput__content {
	background: transparent !important;
	border: none !important;
}

::v-deep .u-checkbox {
	margin-top: 32rpx;
	margin-bottom: 32rpx;
}
</style>