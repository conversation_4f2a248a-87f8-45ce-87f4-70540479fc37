<template>
	<view class="industry-select-page">
		<!-- 顶部导航 -->
		<view class="header">
			<u-navbar
				title="选择行业"
				:border-bottom="true"
				left-icon="arrow-left"
				@left-click="goBack"
			></u-navbar>
		</view>

		<!-- 选择提示 -->
		<view class="selection-tip">
			<view class="tip-content">
				<u-icon name="info-circle" size="32" color="#409eff"></u-icon>
				<text class="tip-text">最多可选择 3 个行业，已选择 {{ selectedIndustries.length }}/3</text>
			</view>
		</view>

		<!-- 主体内容区域 -->
		<view class="main-content">
			<!-- 左侧一级行业列表 -->
			<view class="left-sidebar">
				<scroll-view scroll-y class="sidebar-scroll">
					<view
						v-for="(primaryIndustry, primaryIndex) in industries"
						:key="primaryIndex"
						class="primary-industry-item"
						:class="{ 'active': currentPrimaryIndex === primaryIndex }"
						@click="selectPrimaryIndustry(primaryIndex)"
					>
						<view class="primary-industry-content">
							<u-icon
								:name="primaryIndustry.icon"
								size="36"
								:color="currentPrimaryIndex === primaryIndex ? '#409eff' : primaryIndustry.color"
							></u-icon>
							<text class="primary-industry-name">{{ primaryIndustry.name }}</text>
							<view class="industry-count">
								<text class="count-text">{{ primaryIndustry.subIndustries.length }}</text>
							</view>
						</view>
						<view v-if="currentPrimaryIndex === primaryIndex" class="active-indicator"></view>
					</view>
				</scroll-view>
			</view>

			<!-- 右侧二级行业列表 -->
			<view class="right-content">
				<view v-if="currentPrimaryIndustry" class="sub-industries-container">
					<!-- 当前一级行业标题 -->
					<view class="current-primary-header">
						<u-icon
							:name="currentPrimaryIndustry.icon"
							size="40"
							:color="currentPrimaryIndustry.color"
						></u-icon>
						<text class="current-primary-name">{{ currentPrimaryIndustry.name }}</text>
					</view>

					<!-- 二级行业网格 -->
					<scroll-view scroll-y class="sub-industries-scroll">
						<view class="sub-industries-grid">
							<view
								v-for="(subIndustry, subIndex) in currentPrimaryIndustry.subIndustries"
								:key="subIndex"
								class="sub-industry-item"
								:class="{
									'selected': isIndustrySelected(subIndustry.id),
									'disabled': !isIndustrySelected(subIndustry.id) && selectedIndustries.length >= 3
								}"
								@click="selectIndustry(currentPrimaryIndustry, subIndustry)"
							>
								<text class="sub-industry-name">{{ subIndustry.name }}</text>
								<view v-if="isIndustrySelected(subIndustry.id)" class="selected-icon">
									<u-icon name="checkmark" size="20" color="#fff"></u-icon>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>

				<!-- 空状态 -->
				<view v-else class="empty-state">
					<u-icon name="list" size="80" color="#c0c4cc"></u-icon>
					<text class="empty-text">请选择左侧行业分类</text>
				</view>
			</view>
		</view>

		<!-- 已选择的行业 -->
		<view v-if="selectedIndustries.length > 0" class="selected-industries">
			<view class="selected-header">
				<text class="selected-title">已选择行业 ({{ selectedIndustries.length }}/3)</text>
				<text class="clear-btn" @click="clearAllIndustries">清空</text>
			</view>
			<scroll-view scroll-x class="selected-scroll">
				<view class="selected-industries-list">
					<view
						v-for="(industry, index) in selectedIndustries"
						:key="index"
						class="selected-industry-item"
					>
						<text class="selected-industry-name">{{ industry.name }}</text>
						<text class="selected-industry-category">{{ industry.primaryName }}</text>
						<u-icon
							name="close"
							size="16"
							color="#909399"
							@click="removeIndustry(industry)"
						></u-icon>
					</view>
				</view>
			</scroll-view>
		</view>

		<!-- 底部操作 -->
		<view class="bottom-actions">
			<view class="action-buttons">
				<u-button
					type="default"
					@click="resetSelection"
					custom-style="width: 30%; height: 80rpx; margin-right: 20rpx;"
				>
					重置
				</u-button>
				<u-button
					type="primary"
					:disabled="selectedIndustries.length === 0"
					@click="confirmSelection"
					custom-style="width: 65%; height: 80rpx;"
				>
					确认选择 ({{ selectedIndustries.length }})
				</u-button>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			selectedIndustries: [], // 已选择的行业
			maxSelection: 3, // 最大选择数量
			currentPrimaryIndex: 0, // 当前选中的一级行业索引

			// 行业数据
			industries: [
				{
					name: '互联网/电商',
					icon: 'wifi',
					color: '#409eff',
					expanded: true,
					subIndustries: [
						{ id: 'internet_ecommerce', name: '电子商务' },
						{ id: 'internet_social', name: '社交网络' },
						{ id: 'internet_search', name: '搜索引擎' },
						{ id: 'internet_game', name: '网络游戏' },
						{ id: 'internet_education', name: '在线教育' },
						{ id: 'internet_finance', name: '互联网金融' },
						{ id: 'internet_travel', name: '在线旅游' },
						{ id: 'internet_o2o', name: 'O2O服务' }
					]
				},
				{
					name: '金融/投资',
					icon: 'rmb-circle',
					color: '#19be6b',
					expanded: false,
					subIndustries: [
						{ id: 'finance_bank', name: '银行' },
						{ id: 'finance_insurance', name: '保险' },
						{ id: 'finance_securities', name: '证券' },
						{ id: 'finance_fund', name: '基金' },
						{ id: 'finance_trust', name: '信托' },
						{ id: 'finance_pe', name: '私募/风投' },
						{ id: 'finance_fintech', name: '金融科技' },
						{ id: 'finance_payment', name: '第三方支付' }
					]
				},
				{
					name: '房产/建筑',
					icon: 'home',
					color: '#ff9500',
					expanded: false,
					subIndustries: [
						{ id: 'realestate_development', name: '房地产开发' },
						{ id: 'realestate_agency', name: '房地产中介' },
						{ id: 'realestate_property', name: '物业管理' },
						{ id: 'construction_engineering', name: '建筑工程' },
						{ id: 'construction_design', name: '建筑设计' },
						{ id: 'construction_decoration', name: '装修装饰' },
						{ id: 'construction_materials', name: '建筑材料' }
					]
				},
				{
					name: '汽车/交通',
					icon: 'car',
					color: '#fa3534',
					expanded: false,
					subIndustries: [
						{ id: 'auto_manufacturing', name: '汽车制造' },
						{ id: 'auto_sales', name: '汽车销售' },
						{ id: 'auto_service', name: '汽车服务' },
						{ id: 'auto_parts', name: '汽车配件' },
						{ id: 'transport_logistics', name: '物流运输' },
						{ id: 'transport_aviation', name: '航空航天' },
						{ id: 'transport_railway', name: '铁路运输' },
						{ id: 'transport_shipping', name: '航运海运' }
					]
				},
				{
					name: '医疗/健康',
					icon: 'heart',
					color: '#e4007f',
					expanded: false,
					subIndustries: [
						{ id: 'medical_hospital', name: '医院/诊所' },
						{ id: 'medical_pharma', name: '制药/生物技术' },
						{ id: 'medical_device', name: '医疗器械' },
						{ id: 'medical_health', name: '健康管理' },
						{ id: 'medical_beauty', name: '医疗美容' },
						{ id: 'medical_elderly', name: '养老服务' },
						{ id: 'medical_insurance', name: '医疗保险' }
					]
				},
				{
					name: '教育/培训',
					icon: 'bookmark',
					color: '#8f4fff',
					expanded: false,
					subIndustries: [
						{ id: 'education_k12', name: 'K12教育' },
						{ id: 'education_higher', name: '高等教育' },
						{ id: 'education_vocational', name: '职业培训' },
						{ id: 'education_language', name: '语言培训' },
						{ id: 'education_art', name: '艺术培训' },
						{ id: 'education_early', name: '早教/幼教' },
						{ id: 'education_exam', name: '考试培训' }
					]
				}
			]
		}
	},
	computed: {
		// 当前选中的一级行业
		currentPrimaryIndustry() {
			return this.industries[this.currentPrimaryIndex] || null;
		}
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 选择一级行业
		selectPrimaryIndustry(index) {
			this.currentPrimaryIndex = index;
		},

		// 选择/取消选择行业
		selectIndustry(primaryIndustry, subIndustry) {
			const isSelected = this.isIndustrySelected(subIndustry.id);

			if (isSelected) {
				// 取消选择
				this.removeIndustryById(subIndustry.id);
			} else {
				// 检查是否已达到最大选择数量
				if (this.selectedIndustries.length >= this.maxSelection) {
					uni.showToast({
						title: `最多只能选择${this.maxSelection}个行业`,
						icon: 'none',
						duration: 2000
					});
					return;
				}

				// 添加选择
				const industryWithCategory = {
					id: subIndustry.id,
					name: subIndustry.name,
					primaryId: primaryIndustry.name,
					primaryName: primaryIndustry.name,
					primaryColor: primaryIndustry.color
				};
				this.selectedIndustries.push(industryWithCategory);
			}
		},

		// 检查行业是否已选择
		isIndustrySelected(industryId) {
			return this.selectedIndustries.some(industry => industry.id === industryId);
		},

		// 移除指定行业
		removeIndustry(industry) {
			this.removeIndustryById(industry.id);
		},

		// 根据ID移除行业
		removeIndustryById(industryId) {
			const index = this.selectedIndustries.findIndex(industry => industry.id === industryId);
			if (index > -1) {
				this.selectedIndustries.splice(index, 1);
			}
		},

		// 清空所有选择
		clearAllIndustries() {
			uni.showModal({
				title: '确认清空',
				content: '确定要清空所有已选择的行业吗？',
				success: (res) => {
					if (res.confirm) {
						this.selectedIndustries = [];
					}
				}
			});
		},

		// 重置选择
		resetSelection() {
			uni.showModal({
				title: '确认重置',
				content: '确定要重置所有选择吗？',
				success: (res) => {
					if (res.confirm) {
						this.selectedIndustries = [];
						// 重置到第一个一级行业
						this.currentPrimaryIndex = 0;
					}
				}
			});
		},

		// 确认选择
		confirmSelection() {
			if (this.selectedIndustries.length === 0) {
				uni.showToast({
					title: '请至少选择一个行业',
					icon: 'none'
				});
				return;
			}

			// 按一级行业分组
			const industriesByPrimary = {};
			this.selectedIndustries.forEach(industry => {
				if (!industriesByPrimary[industry.primaryId]) {
					industriesByPrimary[industry.primaryId] = {
						primaryName: industry.primaryName,
						primaryColor: industry.primaryColor,
						subIndustries: []
					};
				}
				industriesByPrimary[industry.primaryId].subIndustries.push({
					id: industry.id,
					name: industry.name
				});
			});

			console.log('选择的行业:', {
				total: this.selectedIndustries.length,
				industries: industriesByPrimary,
				list: this.selectedIndustries
			});

			uni.showModal({
				title: '确认选择',
				content: `您选择了 ${this.selectedIndustries.length} 个行业`,
				success: (res) => {
					if (res.confirm) {
						// 可以通过事件或者页面参数传递选择的行业数据
						uni.navigateBack({
							delta: 1
						});
					}
				}
			});
		}
	}

}
</script>

<style lang="scss" scoped>
.industry-select-page {
	background-color: #f8f9fa;
	min-height: 100vh;
	padding-bottom: 120rpx;
}

.header {
	background-color: #fff;
	border-bottom: 1rpx solid #ebeef5;
}

.selection-tip {
	background-color: #fff;
	padding: 30rpx;
	margin-bottom: 20rpx;
}

.tip-content {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 20rpx 30rpx;
	background-color: #f0f9ff;
	border-radius: 16rpx;
	border: 1rpx solid #e3f2fd;
}

.tip-text {
	font-size: 28rpx;
	color: #409eff;
	font-weight: 500;
}

/* 主体内容区域 */
.main-content {
	display: flex;
	height: calc(100vh - 200rpx);
	background-color: #fff;
}

/* 左侧一级行业列表 */
.left-sidebar {
	width: 240rpx;
	background-color: #f5f6fa;
	border-right: 1rpx solid #ebeef5;
	flex-shrink: 0;
}

.sidebar-scroll {
	height: 100%;
}

.primary-industry-item {
	position: relative;
	padding: 30rpx 20rpx;
	border-bottom: 1rpx solid #ebeef5;
	transition: all 0.3s ease;

	&.active {
		background-color: #fff;

		.primary-industry-name {
			color: #409eff;
			font-weight: 600;
		}

		.count-text {
			color: #409eff;
		}
	}

	&:active {
		background-color: #e3f2fd;
	}
}

.primary-industry-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 12rpx;
}

.primary-industry-name {
	font-size: 26rpx;
	color: #606266;
	text-align: center;
	line-height: 1.4;
}

.industry-count {
	background-color: #f0f0f0;
	border-radius: 20rpx;
	padding: 4rpx 12rpx;
	min-width: 40rpx;
	text-align: center;
}

.count-text {
	font-size: 22rpx;
	color: #909399;
}

.active-indicator {
	position: absolute;
	right: 0;
	top: 50%;
	transform: translateY(-50%);
	width: 6rpx;
	height: 40rpx;
	background-color: #409eff;
	border-radius: 3rpx 0 0 3rpx;
}

/* 右侧内容区域 */
.right-content {
	flex: 1;
	background-color: #fff;
}

.sub-industries-container {
	height: 100%;
	display: flex;
	flex-direction: column;
}

.current-primary-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;
	background-color: #fafbfc;
}

.current-primary-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #303133;
}

.sub-industries-scroll {
	flex: 1;
	padding: 30rpx;
}

.sub-industries-grid {
	display: flex;
	flex-wrap: wrap;
	gap: 20rpx;
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	height: 100%;
	gap: 30rpx;
}

.empty-text {
	font-size: 28rpx;
	color: #909399;
}

.sub-industry-item {
	position: relative;
	padding: 24rpx 30rpx;
	background-color: #fff;
	border-radius: 40rpx;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	min-width: 160rpx;
	text-align: center;

	&.selected {
		background-color: #e3f2fd;
		border-color: #409eff;

		.sub-industry-name {
			color: #409eff;
			font-weight: 600;
		}
	}

	&.disabled {
		opacity: 0.5;
		background-color: #f5f5f5;

		.sub-industry-name {
			color: #c0c4cc;
		}
	}

	&:not(.disabled):active {
		transform: scale(0.95);
	}
}

.sub-industry-name {
	font-size: 28rpx;
	color: #606266;
}

.selected-icon {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 32rpx;
	height: 32rpx;
	background-color: #409eff;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

.selected-industries {
	background-color: #fff;
	border-top: 1rpx solid #ebeef5;
	position: fixed;
	bottom: 120rpx;
	left: 0;
	right: 0;
	max-height: 300rpx;
	z-index: 100;
}

.selected-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx 10rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.selected-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #303133;
}

.clear-btn {
	font-size: 26rpx;
	color: #409eff;
}

.selected-scroll {
	height: 120rpx;
}

.selected-industries-list {
	display: flex;
	padding: 0 30rpx 20rpx;
	gap: 20rpx;
	white-space: nowrap;
}

.selected-industry-item {
	display: flex;
	align-items: center;
	padding: 12rpx 20rpx;
	background-color: #e3f2fd;
	border-radius: 30rpx;
	gap: 10rpx;
	flex-shrink: 0;
}

.selected-industry-name {
	font-size: 26rpx;
	color: #409eff;
	font-weight: 500;
	white-space: nowrap;
}

.selected-industry-category {
	font-size: 22rpx;
	color: #909399;
	background-color: rgba(255, 255, 255, 0.8);
	padding: 4rpx 8rpx;
	border-radius: 12rpx;
	white-space: nowrap;
}

.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	padding: 30rpx;
	border-top: 1rpx solid #ebeef5;
	z-index: 999;
}

.action-buttons {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
</style>