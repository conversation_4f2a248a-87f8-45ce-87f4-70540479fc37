<template>
    <view class="test-pdf-viewer">
        <!-- 加载状态 -->
        <view v-if="loading" class="loading-container">
            <u-loading-icon mode="spinner" size="40" color="#14B19E"></u-loading-icon>
            <text class="loading-text">{{ loadingText }}</text>
        </view>

        <!-- 错误状态 -->
        <view v-else-if="error" class="error-container">
            <u-icon name="file-pdf" size="80" color="#ff4757"></u-icon>
            <text class="error-title">{{ fileName || 'PDF文档' }}</text>
            <text class="error-text">{{ errorMessage }}</text>
            <view class="error-actions">
                <button class="action-btn primary" @click="downloadAndOpen">
                    <u-icon name="download" size="18" color="#fff" style="margin-right: 8px;"></u-icon>
                    下载查看
                </button>
                <button class="action-btn secondary" @click="retryLoad">
                    <u-icon name="refresh" size="18" color="#14B19E" style="margin-right: 8px;"></u-icon>
                    重新加载
                </button>
            </view>
        </view>

        <!-- PDF显示区域 -->
        <view v-else class="pdf-display">
            <!-- 简单工具栏 -->
            <view class="simple-toolbar">
                <text class="pdf-title">{{ fileName || 'PDF文档' }}</text>
                <button class="download-btn" @click="downloadAndOpen">
                    <u-icon name="download" size="16" color="#14B19E"></u-icon>
                </button>
            </view>

            <!-- Canvas容器 -->
            <scroll-view class="canvas-scroll" scroll-x="true" scroll-y="true">
                <view class="canvas-wrapper" :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }">
                    <canvas 
                        class="pdf-canvas"
                        canvas-id="testPdfCanvas"
                        :style="{ width: canvasWidth + 'px', height: canvasHeight + 'px' }">
                    </canvas>
                </view>
            </scroll-view>

            <!-- 提示信息 -->
            <view class="info-bar">
                <text class="info-text">{{ infoText }}</text>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'TestPdfViewer',
    props: {
        pdfUrl: {
            type: String,
            required: true
        },
        fileName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            loading: false,
            error: false,
            loadingText: '正在加载...',
            errorMessage: '',
            infoText: '',
            
            // Canvas相关
            canvasContext: null,
            canvasWidth: 400,
            canvasHeight: 600,
            
            // 系统信息
            systemInfo: {},
            platform: ''
        }
    },
    mounted() {
        this.init()
    },
    methods: {
        // 初始化
        async init() {
            try {
                console.log('测试PDF查看器初始化开始')
                
                // 获取系统信息
                this.systemInfo = uni.getSystemInfoSync()
                console.log('系统信息:', this.systemInfo)
                
                // 检测平台
                this.detectPlatform()
                
                // 设置Canvas尺寸
                this.canvasWidth = this.systemInfo.windowWidth - 40
                this.canvasHeight = this.systemInfo.windowHeight - 200
                
                console.log('Canvas尺寸:', { width: this.canvasWidth, height: this.canvasHeight })
                
                // 等待DOM渲染
                await this.$nextTick()
                
                // 初始化Canvas
                this.initCanvas()
                
                // 开始加载
                if (this.pdfUrl) {
                    await this.loadPDF()
                }
                
            } catch (error) {
                console.error('初始化失败:', error)
                this.showError('初始化失败: ' + error.message)
            }
        },

        // 检测平台
        detectPlatform() {
            // #ifdef H5
            this.platform = 'h5'
            // #endif
            
            // #ifdef APP-PLUS
            this.platform = 'app'
            // #endif
            
            // #ifdef MP-WEIXIN
            this.platform = 'weixin'
            // #endif
            
            console.log('当前平台:', this.platform)
        },

        // 初始化Canvas
        initCanvas() {
            try {
                if (this.platform === 'h5') {
                    // H5端暂时不处理Canvas，直接显示提示
                    this.infoText = 'H5端PDF预览功能开发中...'
                } else {
                    // uni-app平台
                    this.canvasContext = uni.createCanvasContext('testPdfCanvas', this)
                    console.log('Canvas上下文创建成功')
                }
            } catch (error) {
                console.error('Canvas初始化失败:', error)
            }
        },

        // 加载PDF
        async loadPDF() {
            console.log('开始加载PDF:', this.pdfUrl)
            
            this.loading = true
            this.error = false
            this.loadingText = '正在处理PDF文档...'

            try {
                // 根据平台处理
                if (this.platform === 'h5') {
                    await this.handleH5PDF()
                } else {
                    await this.handleAppPDF()
                }
                
                this.loading = false
                
                // 触发加载完成事件
                this.$emit('loaded', {
                    platform: this.platform,
                    success: true
                })
                
            } catch (error) {
                console.error('PDF加载失败:', error)
                this.showError(error.message || 'PDF加载失败')
            }
        },

        // 处理H5端PDF
        async handleH5PDF() {
            console.log('H5端PDF处理')
            this.infoText = 'H5端：建议点击下载按钮在新窗口查看PDF'
        },

        // 处理APP端PDF
        async handleAppPDF() {
            console.log('APP端PDF处理')
            
            // 在Canvas上绘制提示信息
            if (this.canvasContext) {
                this.drawPDFInfo()
                this.infoText = 'APP端：PDF文档已准备就绪，点击下载查看完整内容'
            } else {
                this.infoText = 'Canvas未初始化，请点击下载查看PDF'
            }
        },

        // 在Canvas上绘制PDF信息
        drawPDFInfo() {
            if (!this.canvasContext) return

            try {
                // 设置背景
                this.canvasContext.setFillStyle('#ffffff')
                this.canvasContext.fillRect(0, 0, this.canvasWidth, this.canvasHeight)

                // 绘制边框
                this.canvasContext.setStrokeStyle('#e5e5e5')
                this.canvasContext.setLineWidth(2)
                this.canvasContext.strokeRect(10, 10, this.canvasWidth - 20, this.canvasHeight - 20)

                // 绘制PDF图标区域
                this.canvasContext.setFillStyle('#ff4757')
                this.canvasContext.fillRect(this.canvasWidth / 2 - 30, this.canvasHeight / 2 - 80, 60, 40)

                // 绘制文字
                this.canvasContext.setFillStyle('#333333')
                this.canvasContext.setFontSize(18)
                this.canvasContext.setTextAlign('center')

                const centerX = this.canvasWidth / 2
                const centerY = this.canvasHeight / 2

                this.canvasContext.fillText('PDF 文档', centerX, centerY - 20)
                
                this.canvasContext.setFontSize(14)
                this.canvasContext.fillText(this.fileName || '简历文件', centerX, centerY + 10)
                this.canvasContext.fillText('点击下载按钮查看完整内容', centerX, centerY + 40)
                
                this.canvasContext.setFontSize(12)
                this.canvasContext.setFillStyle('#999999')
                this.canvasContext.fillText(`平台: ${this.platform}`, centerX, centerY + 70)

                // 绘制到Canvas
                this.canvasContext.draw()
                
                console.log('Canvas绘制完成')
                
            } catch (error) {
                console.error('Canvas绘制失败:', error)
            }
        },

        // 显示错误
        showError(message) {
            this.error = true
            this.errorMessage = message
            this.loading = false
            
            this.$emit('error', { message })
        },

        // 重新加载
        retryLoad() {
            this.loadPDF()
        },

        // 下载并打开
        async downloadAndOpen() {
            try {
                console.log('开始下载PDF:', this.pdfUrl)
                uni.showLoading({ title: '准备下载...' })
                
                // #ifdef APP-PLUS
                const downloadTask = uni.downloadFile({
                    url: this.pdfUrl,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            uni.hideLoading()
                            uni.openDocument({
                                filePath: res.tempFilePath,
                                showMenu: true,
                                success: () => {
                                    uni.showToast({
                                        title: '文件已打开',
                                        icon: 'success'
                                    })
                                },
                                fail: () => {
                                    uni.showToast({
                                        title: '无法打开文件',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    },
                    fail: () => {
                        uni.hideLoading()
                        uni.showToast({
                            title: '下载失败',
                            icon: 'none'
                        })
                    }
                })
                
                downloadTask.onProgressUpdate((res) => {
                    uni.showLoading({ title: `下载中...${res.progress}%` })
                })
                // #endif
                
                // #ifdef H5
                uni.hideLoading()
                window.open(this.pdfUrl, '_blank')
                // #endif
                
                // #ifdef MP-WEIXIN
                uni.hideLoading()
                uni.showToast({
                    title: '请在浏览器中下载',
                    icon: 'none'
                })
                // #endif
                
            } catch (error) {
                uni.hideLoading()
                console.error('下载失败:', error)
                uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                })
            }
        }
    }
}
</script>

<style scoped>
.test-pdf-viewer {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 15px;
    padding: 40px 20px;
}

.loading-text {
    font-size: 14px;
    color: #666;
}

.error-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-top: 15px;
}

.error-text {
    font-size: 14px;
    color: #999;
    text-align: center;
    line-height: 1.5;
}

.error-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    border-radius: 20px;
    font-size: 14px;
    border: none;
}

.action-btn.primary {
    background-color: #14B19E;
    color: #fff;
}

.action-btn.secondary {
    background-color: #fff;
    color: #14B19E;
    border: 1px solid #14B19E;
}

/* PDF显示区域 */
.pdf-display {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.simple-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #fff;
    border-bottom: 1px solid #e5e5e5;
}

.pdf-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    flex: 1;
}

.download-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border: 1px solid #e5e5e5;
    border-radius: 20px;
}

.canvas-scroll {
    flex: 1;
    background-color: #fff;
}

.canvas-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100%;
    padding: 20px;
}

.pdf-canvas {
    display: block;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-radius: 4px;
}

.info-bar {
    padding: 10px 15px;
    background-color: #fff;
    border-top: 1px solid #e5e5e5;
}

.info-text {
    font-size: 12px;
    color: #666;
    text-align: center;
}
</style>
