# WebSocket 连接问题排查指南

## 问题现象
```
WebSocket connection to 'ws://*************/' failed
WebSocket: 连接错误 {}
WebSocket: 重连次数已达上限，停止重连
```

## 排查步骤

### 1. 检查服务器地址
- ✅ 已修复：URL 从 `ws://*************/ws` 改为 `ws://*************/`
- 确认服务器是否在 `*************` 上运行
- 确认服务器是否监听 WebSocket 连接

### 2. 检查网络连接
```javascript
// 在浏览器控制台测试网络连接
ping *************
telnet ************* 80
```

### 3. 检查 Token
- 确认 `uni.getStorageSync('token')` 返回有效 token
- 确认 token 格式正确（通常以 Bearer 开头）

### 4. 使用调试工具
1. 在消息页面点击右上角"调试"按钮
2. 查看连接状态和配置信息
3. 尝试手动连接和重置

## 常见解决方案

### 方案1: 检查服务器状态
```bash
# 检查服务器是否运行
curl -I http://*************/
# 或者
ping *************
```

### 方案2: 更新服务器地址
如果服务器地址不正确，可以通过以下方式更新：

```javascript
// 在调试页面或控制台执行
import webSocketService from '@/utils/websocket.js'
webSocketService.updateServerUrl('ws://新的服务器地址/')
```

### 方案3: 检查 Token 格式
```javascript
// 检查 token
const token = uni.getStorageSync('token')
console.log('Token:', token)

// 如果 token 需要 Bearer 前缀
if (token && !token.startsWith('Bearer ')) {
    const newToken = 'Bearer ' + token
    uni.setStorageSync('token', newToken)
}
```

### 方案4: 手动测试连接
```javascript
// 在浏览器控制台手动测试
const ws = new WebSocket('ws://*************/')
ws.onopen = () => console.log('连接成功')
ws.onerror = (error) => console.log('连接失败', error)
ws.onclose = (event) => console.log('连接关闭', event)
```

## 调试工具使用

### 访问调试页面
1. 打开消息页面
2. 点击右上角"调试"按钮
3. 查看连接状态和日志

### 调试功能
- **连接状态**: 实时显示连接状态
- **手动连接**: 测试连接功能
- **重置连接**: 清除状态重新连接
- **更新地址**: 修改服务器地址
- **发送测试**: 发送测试消息
- **消息日志**: 查看详细日志

## 配置选项

### 修改重连参数
```javascript
// 在 utils/websocket.js 中修改
this.maxReconnectAttempts = 10;  // 增加重连次数
this.reconnectInterval = 5000;   // 增加重连间隔
```

### 修改服务器地址
```javascript
// 方法1: 直接修改代码
this.serverUrl = 'ws://你的服务器地址/';

// 方法2: 动态更新
webSocketService.updateServerUrl('ws://新地址/')
```

## 常见错误码

### 1006 - 连接异常关闭
- 服务器未运行
- 网络连接问题
- 防火墙阻止连接

### 1000 - 正常关闭
- 服务器主动关闭连接
- 客户端主动关闭连接

### 1002 - 协议错误
- WebSocket 协议不匹配
- 服务器不支持 WebSocket

## 下一步操作

1. **立即测试**: 使用调试页面测试连接
2. **检查服务器**: 确认服务器运行状态
3. **验证 Token**: 检查 token 是否有效
4. **网络测试**: 确认网络连接正常

## 联系支持

如果问题仍然存在，请提供以下信息：
- 调试页面的连接状态截图
- 浏览器控制台的错误日志
- 服务器运行状态
- 网络环境信息
