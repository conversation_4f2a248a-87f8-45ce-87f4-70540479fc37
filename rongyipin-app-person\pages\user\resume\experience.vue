<template>
  <view class="container">
    <u-navbar title="添加工作经历" :autoBack="true" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    
    <view class="content">
      <view class="section">
        <view class="section-title">公司名称</view>
        <u-input v-model="companyName" placeholder="请输入" border="bottom" clearable></u-input>
        <!-- <view class="section-subtitle">所在行业</view>
        <view @click="industryfunc" >
            <u-input v-model="industry" placeholder="请选择" border="bottom" 
                 :disabled="true">
          <u-icon slot="right" name="arrow-right"></u-icon>
        </u-input>
        </view> -->
        
      </view>
      
      <view class="section">
        <view class="section-title">在职时间</view>
        <view class="time-range">
            <view @click="showStartTimePicker = true" >
                <u-input v-model="startTime" placeholder="入职时间" border="bottom" 
                        :disabled="true">
                    <u-icon slot="right" name="arrow-right"></u-icon>
                </u-input>
            </view>
            <text class="time-separator">至</text>
            <view @click="showEndTimePicker = true" >
                <u-input v-model="endTime" placeholder="离职时间" border="bottom" 
                        :disabled="true">
                    <u-icon slot="right" name="arrow-right"></u-icon>
                </u-input>
            </view>
        </view>
        <view class="section-subtitle">职位名称</view>
        <view>
             <u-input v-model="position" placeholder="请输入" border="bottom" clearable></u-input>
            <!-- <u-input v-model="position.name" placeholder="请选择" border="bottom" 
                    :disabled="true">
            <u-icon slot="right" name="arrow-right"></u-icon>
            </u-input> -->
        </view>
      </view>
      
      <view class="section">
        <view class="section-title">工作内容</view>
        <u-input v-model="jobContent" placeholder="请输入" border="bottom" clearable type="textarea" autoHeight></u-input>
        <!-- <view class="optional-item">
          <text class="optional-label">以下为选项项</text>
          <u-input v-model="jobPerformance" placeholder="工作业绩" border="bottom" clearable></u-input>
        </view> -->
      </view>
      
      <!-- <view class="section">
        <view class="section-title">所属部门</view>
        <u-input v-model="department" placeholder="请选择" border="bottom" 
                 @click="showDepartmentPicker = true" :disabled="true">
          <u-icon slot="right" name="arrow-right"></u-icon>
        </u-input>
      </view> -->
      
      <view class="divider"></view>
      
      <!-- <view class="checkbox-section">
        <u-checkbox-group v-model="isInternship">
          <u-checkbox :name="true" shape="circle">本段经历是实习经历</u-checkbox>
        </u-checkbox-group>
      </view> -->
      
      <view class="button-group">
        <view class="button-del" v-if="id"> <u-button text="删除"  shape="circle" @click="delExperience"></u-button> </view>
        <view class="button-save"><u-button type="primary" text="保存" @click="saveExperience"></u-button></view>
      </view>
    </view>
    
    <!-- 行业选择器 -->
     <u-popup :show="showIndustryPicker" mode="right" @close="showIndustryPicker = false">
                    <view>
                        <div class="city-title">
                            <u-icon name="arrow-left" size="30" @click="showIndustryPicker = false"></u-icon>
                        </div>
                        <Industry ref="searchindustry" @industrydatafunc="industrydatafunc"></Industry>
                    
                    </view>
                </u-popup>
    <!-- <u-picker 
      :show="showIndustryPicker" 
      :columns="industryColumns" 
      keyName="label" 
      @confirm="confirmIndustry"
      @cancel="showIndustryPicker = false"
    ></u-picker> -->
    
    <!-- 时间选择器 -->
    <u-datetime-picker
      :show="showStartTimePicker"
      v-model="startTimeValue"
      mode="year-month"
      @confirm="confirmStartTime"
      @cancel="showStartTimePicker = false"
    ></u-datetime-picker>
    
    <u-datetime-picker
      :show="showEndTimePicker"
      v-model="endTimeValue"
      mode="year-month"
      @confirm="confirmEndTime"
      @cancel="showEndTimePicker = false"
    ></u-datetime-picker>
    
    <!-- 职位选择器 -->
     <u-popup :show="showPositionPicker" mode="right" @close="showPositionPicker = false">
                    <view>
                        <div class="city-title">
                            <u-icon name="arrow-left" size="30" @click="showPositionPicker = false"></u-icon>
                        </div>
                        <Positionhope ref="searchsPosition" @positiondatafunc="positiondatafunc"></Positionhope>
                    
                    </view>
                </u-popup>
    <!-- <u-picker 
      :show="showPositionPicker" 
      :columns="positionColumns" 
      keyName="label" 
      @confirm="confirmPosition"
      @cancel="showPositionPicker = false"
    ></u-picker> -->
    
    <!-- 部门选择器 -->
    <u-picker 
      :show="showDepartmentPicker" 
      :columns="departmentColumns" 
      keyName="label" 
      @confirm="confirmDepartment"
      @cancel="showDepartmentPicker = false"
    ></u-picker>
  </view>
</template>

<script>
import  { jobApi,onlineResume } from '@/utils/api.js'
import Industry from '../../index/industry.vue'
import Positionhope from '../../index/positionhope.vue'
export default {
    components: { Industry,Positionhope },
  data() {
    return {
        id: '',
      companyName: '',
      industry: '',
      startTime: '',
      endTime: '至今',
      position: '',
      jobContent: '',
      jobPerformance: '',
      department: '',
      isInternship: false,
      
      // 选择器相关
      showIndustryPicker: false,
      showStartTimePicker: false,
      showEndTimePicker: false,
      showPositionPicker: false,
      showDepartmentPicker: false,
      startTimeValue: Number(new Date()),
      endTimeValue: Number(new Date()),
      
      // 行业选项
      industryColumns: [],
      
      // 职位选项
      positionColumns: [],
      
      // 部门选项
      departmentColumns: []
    }
  },
  onLoad(options) {
        this.id=options.id
		this.loadResumeData()
	},
  methods: {
    // 加载简历数据
		async loadResumeData() {
			await onlineResume.resume().then(res => {
				uni.hideLoading();
				// this.resumeData = res.data;
                res.data.resume_work.map(item=>{
                    if(item.id==this.id){
                        this.companyName=item.name;
                        this.startTime=item.sdate;
                        this.endTime=item.edate;
                        this.position=item.title;
                        this.jobContent=item.content;
                    }
                })

			}).catch(err => {
				console.error("获取简历数据失败:", err)
				uni.showToast({
					title: '加载简历数据失败',
					icon: 'none'
				})
			})
		},
     // 职位data
        positiondatafunc(data){
            this.showPositionPicker=false;
            this.position=data;
            // //职位改变技能清空
            // this.skills=[];
        },
     // 职位
        hopeposition(){
            this.showPositionPicker=true;
            this.$nextTick(() => {
                 this.$refs.searchsPosition.getJobCategories();
            })
        },
     // 行业选择
        industryfunc(){
            this.showIndustryPicker=true;
            this.$nextTick(() => {
                const searchindustryRef = this.$refs.searchindustry;
                if (searchindustryRef && searchindustryRef.initDefaultCategory) {
                    this.$refs.searchindustry.initDefaultCategory();
                } else {
                    console.error('Industry 组件未正确加载');
                }
            })
        },
    //获取行业data
        industrydatafunc(data){ 
            console.log('获取行业data',data);
            // this.industrydata = data;
            this.showIndustryPicker=false;
             const hy_ids=[];
                data.map(item=>{
                    hy_ids.push(item.name)
                })
            this.industry=hy_ids.join(',');
        },
    confirmIndustry(e) {
      this.industry = e.value[0].label
      this.showIndustryPicker = false
    },
    confirmStartTime(e) {
        console.log(e.value,Number(new Date()))
      this.startTime = this.$u.timeFormat(e.value, 'yyyy-mm')
      this.showStartTimePicker = false
    },
    confirmEndTime(e) {
      this.endTime = this.$u.timeFormat(e.value, 'yyyy-mm')
      this.showEndTimePicker = false
    },
    confirmPosition(e) {
      this.position = e.value[0].label
      this.showPositionPicker = false
    },
    confirmDepartment(e) {
      this.department = e.value[0].label
      this.showDepartmentPicker = false
    },
    async saveExperience() {
      // 验证表单
      if (!this.companyName) {
        this.$u.toast('请输入公司名称')
        return
      }
    //   if (!this.industry) {
    //     this.$u.toast('请选择所在行业')
    //     return
    //   }
      if (!this.startTime) {
        this.$u.toast('请选择入职时间')
        return
      }
      if (!this.position) {
        this.$u.toast('请选择职位名称')
        return
      }
      if (!this.jobContent) {
        this.$u.toast('请输入工作内容')
        return
      }
      const endtime=this.endTime=='至今'?this.$u.timeFormat(Number(new Date()), 'yyyy-mm'):this.endTime
      // 构造数据
      const experience = {
        resume_type:2,
        name: this.companyName,
        sdate: this.startTime,
        edate: endtime,
        title: this.position,
        content: this.jobContent,
      }
      console.log("我提交的数据",this.id, experience)
      if(this.id){
        await jobApi.update({...experience,id:this.id}).then(res=>{
          console.log('保存成功:', res)
        })
      }else{
        await jobApi.add({...experience}).then(res=>{
            console.log('保存成功:', res)
        })
      }
      
      // 保存成功后返回上一页
      setTimeout(() => {
        uni.navigateBack()
      }, 500)
    },
    delExperience(){
        uni.showModal({
            content: '确定要删除吗？',
            success: function (res) {
                if (res.confirm) {
                    jobApi.del({resume_type:2,id:this.id}).then(res=>{
                        uni.hideLoading();
                        console.log('保存成功:', res)
                         setTimeout(() => {
                            uni.navigateBack()
                        }, 500)
                    })
                } else if (res.cancel) {
                            console.log('用户点击取消')
                }
            }
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  background-color: #f7f7f7;
  min-height: 100vh;
}

.content {
  padding: 20rpx 30rpx;
}

.section {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
    font-weight: bold;
  }
  
  .section-subtitle {
    font-size: 26rpx;
    color: #666;
    margin: 20rpx 0 10rpx;
  }
}

.time-range {
  display: flex;
  align-items: center;
  
  .u-input {
    flex: 1;
  }
  
  .time-separator {
    margin: 0 20rpx;
    color: #999;
  }
}

.optional-item {
  margin-top: 20rpx;
  
  .optional-label {
    font-size: 24rpx;
    color: #999;
    display: block;
    margin-bottom: 10rpx;
  }
}

.divider {
  height: 20rpx;
  background-color: #f7f7f7;
}

.checkbox-section {
  background-color: #fff;
  padding: 20rpx 30rpx;
  margin-bottom: 40rpx;
  
  ::v-deep .u-checkbox-group {
    display: flex;
  }
}

.button-group {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
  .button-del,.button-save{
    width: 40%;
    margin:20rpx;
  }
  .u-button {
    border-radius: 50rpx;
    height: 90rpx;
    font-size: 32rpx;
  }
}
</style>