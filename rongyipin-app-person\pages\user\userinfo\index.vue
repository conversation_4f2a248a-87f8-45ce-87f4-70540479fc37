<template>
	<view class="profile-page">
		<view class="navbar">
			<u-navbar height="44px" title="个人信息" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
				:leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
			</u-navbar>
		</view>
		<!-- 基本信息卡片 -->
		<view class="info-card">
			<!-- 头像 -->
			<view class="list-item item-avatar" @click="changeAvatar">
				<text class="label">头像</text>
				<view class="item-right">
					<image class="avatar" :src="userInfo.avatarUrl" mode="aspectFill"></image>
					<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
				</view>
			</view>

			<!-- 姓名 -->
			<view class="list-item" @click="navigateToEdit('name')">
				<text class="label">姓名</text>
				<view class="item-right">
					<text class="value">{{ userInfo.username }}</text>
					<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
				</view>
			</view>

			<!-- 性别 -->
			<view class="list-item" @click="openGenderPopup">
				<text class="label">性别</text>
				<view class="item-right">
					<text class="value">{{ userInfo.sex == 1 ? '男' : '女' }}</text>
					<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
				</view>
			</view>

			<!-- 出生年月 -->
			<view class="list-item" @click="show = true">
				<text class="label">出生年月</text>
				<view class="item-right">
					<text class="value">{{ toYearMonth(userInfo.birthday) }}</text>
					<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 工作信息卡片 -->
		<view class="info-card">
			<!-- 参加工作时间 -->
			<view class="list-item" @click="first_word_show = true">
				<text class="label">参加工作时间</text>
				<view class="item-right">
					<text class="value">{{ userInfo.first_word_time }}</text>
					<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 联系方式卡片 -->
		<view class="info-card">
			<!-- 手机号 -->
			<view class="list-item list-item-column" @click="navigateToEdit('phone')">
				<view class="item-main">
					<text class="label">手机号</text>
					<view class="item-right">
						<text class="value">{{ userInfo.telephone }}</text>
						<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
					</view>
				</view>
				<view class="item-tip">
					<uni-icons type="info-filled" size="14" color="#C0C4CC"></uni-icons>
					<text class="tip-text">手机号只有在您主动与招聘者交换时, 才会告知对方</text>
				</view>
			</view>

			<!-- 微信号 -->
			<view class="list-item list-item-column" @click="navigateToEdit('wechat')">
				<view class="item-main">
					<text class="label">微信号</text>
					<view class="item-right">
						<text class="value">{{ maskedWechat || '待认证' }}</text>
						<uni-icons type="right" size="16" color="#C0C4CC"></uni-icons>
					</view>
				</view>
				<view class="item-tip">
					<uni-icons type="info-filled" size="14" color="#C0C4CC"></uni-icons>
					<text class="tip-text">微信号只有在您主动与招聘者交换时, 才会告知对方</text>
				</view>
			</view>
		</view>
		<view class="footer-actions">
			<button class="save-button" @click="saveProfile">保 存</button>
		</view>

		<view v-if="showGenderPopup" class="popup-mask" @click="closeGenderPopup">
			<!-- 弹窗内容区，使用 @click.stop 防止点击内容关闭弹窗 -->
			<view class="popup-content" @click.stop>
				<!-- 标题 -->
				<view class="popup-title">性别</view>

				<!-- 选项：男 -->
				<view class="popup-item" :class="{ 'is-selected': userInfo.sex === '1' }" @click="onGenderSelect('1')">
					男
				</view>

				<!-- 选项：女 -->
				<view class="popup-item" :class="{ 'is-selected': userInfo.sex === '2' }" @click="onGenderSelect('2')">
					女
				</view>

				<!-- 分隔区域 -->
				<view class="popup-separator"></view>

				<!-- 取消按钮 -->
				<view class="popup-item" @click="closeGenderPopup">
					取消
				</view>
			</view>
		</view>

		<u-datetime-picker style="height: 1000rpx;" :show="show" v-model="userInfo.birthday" mode="date"
			:min-date="minDate" :max-date="maxDate" @confirm="onBirthdayConfirm" @cancel="show = false"
			@close="show = false" title="选择出生日期" closeOnClickOverlay></u-datetime-picker>

		<u-datetime-picker class="datetime-picker" :show="first_word_show" mode="year-month"
			:value="userInfo.first_word_time" :minDate="minDate" :maxDate="maxDate" @confirm="firstWordConfirm"
			@cancel="first_word_show = false" itemHeight="70" @close="first_word_show = false"></u-datetime-picker>
	</view>
</template>

<script>
import { userApi } from "@/utils/api"
// Script部分与之前相同，核心在于数据和方法，无需改动
export default {
	data() {
		return {
			// 模拟从API获取的用户信息
			userInfo: {},
			showGenderPopup: false,
			show: false,
			minDate: 0,
			maxDate: 0,
			first_word_show: false,
		}
	},
	computed: {
		workExperienceText() {
			if (!this.userInfo.workStartDate) return '待补充';
			const start = new Date(this.userInfo.workStartDate);
			const now = new Date();
			let experience = now.getFullYear() - start.getFullYear();
			if (now.getMonth() < start.getMonth() || (now.getMonth() === start.getMonth() && now.getDate() < start.getDate())) {
				experience--;
			}
			return experience <= 0 ? '少于1年经验' : `${experience}年经验`;
		},
		maskedPhone() {
			if (this.userInfo.phone && this.userInfo.phone.length === 11) {
				return this.userInfo.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
			}
			return '待补充';
		},
		maskedWechat() {
			if (!this.userInfo.wechat) return '待补充';
			return this.userInfo.wechat.length > 2 ? this.userInfo.wechat.substring(0, 2) + '****' : this.userInfo.wechat;
		},
	},
	created() {
		// 1. 获取当前时间对象
		const today = new Date();

		// 2. 将当前时间设为可选的最大日期
		// .getTime() 获取毫秒时间戳
		this.maxDate = today.getTime();

		// 3. 计算100年前的日期
		// getFullYear() 获取年份, setFullYear() 设置年份
		const hundredYearsAgo = new Date();
		hundredYearsAgo.setFullYear(today.getFullYear() - 100);

		// 4. 将100年前的日期设为可选的最小日期
		this.minDate = hundredYearsAgo.getTime();
	},

	onShow() {
		this.userInfo = uni.getStorageSync('userInfo')
	},
	methods: {
		changeAvatar() {
			uni.chooseImage({
				count: 1,
				sizeType: ['compressed'],
				success: (res) => {
					// console.log(res);
					this.imageUrl = res.tempFilePaths[0];
					// console.log(this.imageUrl);
					this.uploadImage(this.imageUrl);
					// this.userInfo.avatarUrl = tempFilePath;
					uni.showToast({ title: '头像更新成功', icon: 'none' });
				}
			})
		},
		 uploadImage(filePath) { 
            console.log('选择的图片路径:', filePath)
            uni.showLoading({ title: '上传中' })
            // #ifdef APP-PLUS
            uni.uploadFile({
                url: 'http://8.130.152.121:82/jobapi/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 4
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.userInfo.avatarUrl = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif

            // #ifdef H5
            uni.uploadFile({
                url: '/jobapi/common/upload', // 替换为你自己的上传地址
                filePath: filePath,
                name: 'file', // 根据后端字段设定
                formData: {
                    type: 4
                },
                success: (uploadFileRes) => {
                    console.log(uploadFileRes)
                    uni.hideLoading()
                    uni.showToast({ title: '上传成功', icon: 'success' })
                    this.userInfo.avatarUrl = JSON.parse(uploadFileRes.data).data.url
                },
                fail: (err) => {
                    uni.hideLoading()
                    uni.showToast({ title: '上传失败', icon: 'none' })
                    console.error('上传失败:', err)
                }
            })
            // #endif
        },
		navigateToEdit(field) {
			switch (field) {
				case 'phone':
					uni.navigateTo({
						url: '/pages/user/userinfo/emitPhone'
					});
					break;
				case 'wechat':
					uni.navigateTo({
						url: '/pages/user/userinfo/emitWatch'
					});
					break;
				case 'name':
					uni.navigateTo({
						url: '/pages/user/userinfo/name'
					});
					break;
				default:
					break;
			}
		},
		openGenderPopup() {
			this.showGenderPopup = true;
		},
		// 关闭弹窗
		closeGenderPopup() {
			this.showGenderPopup = false;
		},
		// 选择性别
		onGenderSelect(selectedGender) {
			// 1. 更新数据
			this.userInfo.sex = selectedGender;
			// 2. 关闭弹窗
			this.closeGenderPopup();
		},
		onBirthdayConfirm(e) {
			this.show = false;
		},
		firstWordConfirm(e) {
			this.first_word_show = false;

			const date = this.toYearMonths(e.value);
			this.userInfo.first_word_time = date;
		},
		toYearMonth(timestamp) {
			const date = new Date(timestamp);

			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以要加1
			const day = date.getDate().toString().padStart(2, '0');
			// const hours = date.getHours().toString().padStart(2, '0');
			// const minutes = date.getMinutes().toString().padStart(2, '0');
			// const seconds = date.getSeconds().toString().padStart(2, '0');

			const formattedDate = `${year}-${month}-${day}`;
			return formattedDate;
		},
		toYearMonths(timestamp) {
			const date = new Date(timestamp);

			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以要加1
			// const day = date.getDate().toString().padStart(2, '0');
			// const hours = date.getHours().toString().padStart(2, '0');
			// const minutes = date.getMinutes().toString().padStart(2, '0');
			// const seconds = date.getSeconds().toString().padStart(2, '0');

			const formattedDate = `${year}-${month}`;
			return formattedDate;
		},
		async saveProfile() {

			try {
				uni.showLoading({
					title: '保存中...'
				});
				console.log(this.userInfo)
				const params = {
					avatarUrl:this.userInfo.avatarUrl,
					username: this.userInfo.username,
					sex: this.userInfo.sex,
					telephone: this.userInfo.telephone,
					wechat: this.userInfo.wechat,
					first_word_time: this.userInfo.first_word_time,
					birthday: this.toYearMonth(this.userInfo.birthday),
					// mobile: this.userInfo.mobile,
				}
				const res = await userApi.userSave(params)
				if (res.code == 200) {
					const userinnf = uni.getStorageSync('userInfo') || {};
					userinnf.username = this.userInfo.username.trim();
					userinnf.sex = this.userInfo.sex
					userinnf.birthday = this.toYearMonth(this.userInfo.birthday);
					userinnf.first_word_time = this.userInfo.first_word_time;
					userinnf.avatarUrl =this.userInfo.avatarUrl
					if(this.userInfo.telephone){
						userinnf.telephone = this.userInfo.telephone.trim();
					}
					if(this.userInfo.wechat){
						userinnf.wechat = this.userInfo.wechat.trim();
					}
					uni.setStorageSync('userInfo', userinnf);
					uni.showToast({
						title: '保存成功',
						icon: 'success'
					});
					uni.showToast({
						title: '保存成功',
						icon: 'success',
						duration: 2000
					});
					this.userInfo
					uni.hideLoading()
				} else {
					uni.showToast({
						title: '保存失败',
						icon: 'none',
						duration: 2000
					});
					console.log(res, '@@@@@@@@@@@')
					// uni.hideLoading()
				}

			} catch (err) {
				console.log(err)
			}
		}
	}
}
</script>

<style scoped>
/* 页面整体背景 */
.profile-page {
	background-color: #F5F6FA;
	min-height: 100vh;
	padding: 24rpx 0;
}

/* 信息卡片 */
.info-card {
	background-color: #FFFFFF;
	border-radius: 20rpx;
	margin: 0 24rpx 24rpx;
	padding: 0 32rpx;
	/* 左右内边距，让内容与卡片边缘有距离 */
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
	/* 添加细微阴影提升质感 */
}

/* 列表项通用样式 */
.list-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 32rpx 0;
	/* 上下内边距 */
	transition: background-color 0.2s ease;
}

/* 使用 :not 选择器为非最后一个元素添加下边框，线条更柔和 */
.list-item:not(:last-child) {
	border-bottom: 1rpx solid #F0F2F5;
}

.list-item:active {
	background-color: #FAFAFA;
}

/* 头像行特殊样式 */
.item-avatar {
	padding: 24rpx 0;
}

.avatar {
	width: 110rpx;
	height: 110rpx;
	border-radius: 50%;
	border: 2rpx solid #eee;
}

/* 标签文本 */
.label {
	font-size: 30rpx;
	color: #303133;
}

/* 右侧容器 */
.item-right {
	display: flex;
	align-items: center;
	gap: 16rpx;
	/* 控制右侧内容和箭头的间距 */
}

/* 值文本 */
.value {
	font-size: 28rpx;
	color: #606266;
	/* 文本过长时显示省略号 */
	max-width: 400rpx;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	text-align: right;
}

/* 针对手机号和微信号的特殊纵向布局 */
.list-item-column {
	flex-direction: column;
	align-items: stretch;
	/* 占满整行 */
	gap: 12rpx;
}

.item-main {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

/* 提示信息 */
.item-tip {
	display: flex;
	align-items: center;
	padding-left: 2rpx;
}

.tip-text {
	font-size: 24rpx;
	color: #C0C4CC;
	margin-left: 8rpx;
}

.footer-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #ffffff;
	padding: 20rpx 30rpx;
	/* 适配iPhone等设备底部安全区域 */
	padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
	padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
	box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.06);
	z-index: 100;
}

.save-button {
	width: 100%;
	height: 88rpx;
	line-height: 88rpx;
	font-size: 32rpx;
	font-weight: 500;
	color: #ffffff;
	background: linear-gradient(90deg, #409EFF, #67C23A);
	/* 使用一个漂亮的渐变色 */
	border-radius: 44rpx;
	/* 胶囊形状 */
	border: none;
	box-shadow: 0 4rpx 12rpx rgba(0, 123, 255, 0.3);
}

.save-button:active {
	opacity: 0.9;
	transform: scale(0.98);
	/* 点击时轻微缩小，增加反馈 */
}

/* 去除uni-app按钮默认边框 */
.save-button::after {
	border: none;
}


.popup-mask {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background-color: rgba(0, 0, 0, 0.4);
	z-index: 998;
	animation: fade-in 0.3s ease;
}

/* 弹窗内容 */
.popup-content {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #ffffff;
	border-top-left-radius: 12px;
	border-top-right-radius: 12px;
	z-index: 999;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
	animation: slide-up 0.3s ease-out;
}

/* 弹窗中的项目 */
.popup-item {
	height: 55px;
	line-height: 55px;
	text-align: center;
	font-size: 17px;
	color: #333;
	border-bottom: 1px solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f5f5f5;
	}
}

/* 选中项的样式 */
.popup-item.is-selected {
	color: #07c160;
}

/* 弹窗标题 */
.popup-title {
	height: 50px;
	line-height: 50px;
	text-align: center;
	font-size: 15px;
	color: #888888;
	border-bottom: 1px solid #f0f0f0;
}

/* 分隔条 */
.popup-separator {
	height: 8px;
	background-color: #f7f7f7;
}

/* 动画定义 */
@keyframes slide-up {
	from {
		transform: translateY(100%);
	}

	to {
		transform: translateY(0);
	}
}

@keyframes fade-in {
	from {
		opacity: 0;
	}

	to {
		opacity: 1;
	}
}

::v-deep .u-picker__view {
	height: 400rpx !important;
}
</style>