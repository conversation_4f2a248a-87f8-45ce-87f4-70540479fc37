# WebSocket 监听接口打印功能

## 功能概述

当 WebSocket 连接成功后，自动打印当前监听的接口信息，包括：
- 服务器连接信息
- 监听器状态
- 监听的事件类型
- 相关的 API 接口

## 实现功能

### 1. 自动打印（连接成功时）
当 WebSocket 连接成功时，会自动在控制台打印详细的监听接口信息。

### 2. 手动打印（调试页面）
在调试页面提供"打印监听接口"按钮，可以随时查看当前状态。

## 打印内容示例

```
=== WebSocket 监听接口信息 ===
服务器地址: ws://192.168.1.162/
连接状态: 已连接
Token状态: 已设置

页面级事件监听器:
  - chatListUpdate: 1 个监听器
  - private_msg: 1 个监听器
  - connected: 2 个监听器

全局监听器数量: 1

监听的事件类型:
  - private_msg: 私聊消息
  - chat_update: 聊天更新
  - chatListUpdate: 聊天列表更新
  - connected: 连接成功

相关的API接口:
  - chat.chatList(): 聊天列表接口
  - applyApi.getJobApply(): 求职申请接口
==============================
```

## 核心代码

### WebSocket 服务 (`utils/websocket.js`)

```javascript
/**
 * 打印当前监听的接口信息
 */
printListeningInterfaces() {
    console.log('=== WebSocket 监听接口信息 ===');
    console.log('服务器地址:', this.serverUrl);
    console.log('连接状态:', this.isConnected ? '已连接' : '未连接');
    console.log('Token状态:', this.token ? '已设置' : '未设置');
    
    // 打印事件监听器
    console.log('页面级事件监听器:');
    if (this.listeners.size === 0) {
        console.log('  - 无页面级监听器');
    } else {
        this.listeners.forEach((callbacks, event) => {
            console.log(`  - ${event}: ${callbacks.length} 个监听器`);
        });
    }
    
    // 打印全局监听器
    console.log('全局监听器数量:', this.globalListeners.length);
    
    // 打印监听的具体接口/事件类型
    console.log('监听的事件类型:');
    console.log('  - private_msg: 私聊消息');
    console.log('  - chat_update: 聊天更新');
    console.log('  - chatListUpdate: 聊天列表更新');
    console.log('  - connected: 连接成功');
    
    // 打印相关的API接口
    console.log('相关的API接口:');
    console.log('  - chat.chatList(): 聊天列表接口');
    console.log('  - applyApi.getJobApply(): 求职申请接口');
    
    console.log('==============================');
}

// 在连接成功时自动调用
this.socket.onOpen(() => {
    console.log('WebSocket: 连接已建立');
    this.isConnected = true;
    this.reconnectAttempts = 0;
    
    // 打印当前监听的接口信息
    this.printListeningInterfaces();
    
    // 触发连接成功事件
    this.emit('connected');
});
```

## 使用方法

### 1. 自动打印
- 当应用启动并成功连接 WebSocket 时，会自动打印
- 当手动重连成功时，也会自动打印

### 2. 手动打印
```javascript
// 在任何地方调用
import webSocketService from '@/utils/websocket.js'
webSocketService.printListeningInterfaces()
```

### 3. 调试页面
1. 打开消息页面
2. 点击右上角"调试"按钮
3. 点击"打印监听接口"按钮
4. 查看浏览器控制台

## 监听的接口说明

### WebSocket 事件
- **private_msg**: 私聊消息事件，触发聊天列表更新
- **chat_update**: 聊天更新事件，触发聊天列表更新
- **connected**: 连接成功事件

### API 接口
- **chat.chatList()**: 获取聊天列表的 HTTP API
- **applyApi.getJobApply()**: 获取求职申请列表的 HTTP API

### 监听器类型
- **页面级监听器**: 在特定页面注册，页面卸载时自动移除
- **全局监听器**: 在应用级别注册，整个应用生命周期内有效

## 实际应用场景

### 1. 开发调试
- 快速了解当前 WebSocket 连接状态
- 检查监听器是否正确注册
- 排查事件监听问题

### 2. 生产环境监控
- 监控 WebSocket 连接状态
- 检查监听器数量是否正常
- 排查实时更新问题

### 3. 性能优化
- 检查是否有重复的监听器
- 监控监听器数量增长
- 优化事件处理逻辑

## 注意事项

1. **控制台输出**: 信息会打印到浏览器控制台，生产环境可考虑关闭
2. **监听器计数**: 显示的监听器数量包括所有注册的回调函数
3. **实时更新**: 信息反映调用时的实时状态
4. **调试工具**: 主要用于开发和调试，不影响正常功能

## 当前状态

- ✅ 连接成功时自动打印监听接口信息
- ✅ 调试页面手动打印功能
- ✅ 详细的接口和监听器信息展示
- ✅ 实时状态反映

现在当 WebSocket 连接成功后，您可以在控制台看到完整的监听接口信息！
