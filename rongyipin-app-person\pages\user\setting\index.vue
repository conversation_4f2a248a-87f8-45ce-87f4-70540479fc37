<template>
    <view class="setting-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="设置" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- 设置列表 -->
        <view class="setting-list">
            <view class="setting-item" @click="handleItemClick('account')">
                <text class="item-text">账号与安全</text>
                <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
            </view>
            <view class="setting-item" @click="handleItemClick('notification')">
                <text class="item-text">通知与提醒</text>
                <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
            </view>
            <view class="setting-item" @click="handleItemClick('version')">
                <text class="item-text">版本更新</text>
                <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
            </view>
            <view class="setting-item" @click="handleItemClick('cache')">
                <text class="item-text">清除缓存</text>
                <u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
            </view>
        </view>

        <!-- 退出登录按钮 -->
        <view class="logout-btn">
            <text @click="show = true">退出登录</text>

            <u-modal :show="show" :content="content" cancelText="取消" confirmText="确认" @confirm="confirm"
                @cancel="cancel" showCancelButton></u-modal>
        </view>
    </view>
</template>

<script>
export default {
    data() {
        return {
            show: false,
            title: '标题',
            content: '确定退出登录吗'

        }
    },
    methods: {
        // 返回上一页
        goBack() {
            uni.navigateBack()
        },
        // 处理设置项点击
        handleItemClick(type) {
            switch (type) {
                case 'account':
                    uni.navigateTo({
                        url: '/pages/user/setting/security'
                    })
                    break
                case 'notification':
                    console.log('通知与提醒')
                    break
                case 'version':
                    console.log('版本更新')
                    break
                case 'cache':
                    console.log('清除缓存')
                    break
            }
        },
        confirm() {
            uni.removeStorageSync('token');
            // 其他需要清除的数据
            uni.removeStorageSync('userInfo');
            uni.reLaunch({
                url: '/pages/login/login'
            });
        },
        cancel() {
            this.show = false;
        }
    }
}
</script>

<style lang="scss" scoped>
.setting-page {
    min-height: 100vh;
    background-color: #f5f5f5;
}

/* 顶部导航栏 */
.navbar {
    // display: flex;
    // align-items: center;
    // justify-content: space-between;
    // height: 88rpx;
    // background-color: #fff;
    // padding: 0 30rpx;
    // position: fixed;
    // top: 0;
    // left: 0;
    // right: 0;
    // z-index: 999;

    // .nav-left,
    // .nav-right {
    //     width: 60rpx;
    //     height: 60rpx;
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    // }

    // .nav-title {
    //     flex: 1;
    //     text-align: center;

    //     .title-text {
    //         font-size: 32rpx;
    //         font-weight: 500;
    //         color: #333;
    //     }
    // }
}

/* 设置列表 */
.setting-list {
    background-color: #fff;
    margin-top: 28rpx;
    /* 为固定导航栏留出空间 */
    border-radius: 0;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40rpx 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    transition: background-color 0.2s;

    &:active {
        background-color: #f8f8f8;
    }

    &:last-child {
        border-bottom: none;
    }

    .item-text {
        font-size: 30rpx;
        color: #333;
        font-weight: 400;
    }
}

/* 退出登录按钮样式 */
.logout-btn {
    margin: 40rpx 0rpx;
    background-color: #fff;
    color: #576b95;
    text-align: center;
    padding: 24rpx;
    border-radius: 8rpx;
    font-size: 32rpx;
}


/* 全局页面样式 */
page {
    background-color: #f5f5f5;
}
</style>