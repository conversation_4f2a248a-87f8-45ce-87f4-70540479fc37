<template>
	<view class="earn-money-page">
		<!-- 头部导航栏 -->
    
		<!-- 顶部资产区域 -->
		<view class="header-section">
			<!-- 我的等级和现金 -->
			<view class="top-info">
				<view class="level-info">
					<view class="level-section">
						<view class="level-avatar">
							<image class="avatar-img" src="/static/avatar-default.png"></image>
						</view>
						<view class="">
							<view class="level-label">我的等级</view>
							<view class="level-value">{{grade.level_id}}</view>

						</view>
						
					</view>
					<text class="level-desc">距下一等级还需{{grade.currentEmp}}经验</text>
					<u-line-progress :percentage="grade.percentage" height="10" :showText="false"></u-line-progress>
				</view>
				<view class="cash-section">
					<view class="cash-info">
						<text class="cash-label">我的现金</text>
						<text class="cash-value">{{grade.cash}}</text>
					</view>
					<view class="withdraw-btn" @click="withdraw(grade.cash)">
						提现
					</view>
				</view>
			</view>

			<!-- 点击签到领xxx元 -->
			<view class="sign-banner" @click="signIn">
				<view class="sign-text">点击签到最高领{{grade.max_reward}}元</view>
				<!-- <view class="reminder-toggle">
					<text class="toggle-text">开启提醒</text>
					<switch class="toggle-switch" color="#00D084" :checked="reminderEnabled" @change="toggleReminder" />
				</view> -->
			</view>
		</view>

		<!-- 经验任务 -->
		<view class="tasks-section">
			<text class="section-title">经验任务</text>
			<view class="task-list">
				<view v-for="(task, index) in taskList" :key="task.id" class="task-item" @click="handleTask(task)">
					<view class="task-left">
						<view class="task-icon" :style="{ backgroundColor: task.iconBg }">
							<u-icon :name="task.icon" size="20" color="#fff"></u-icon>
						</view>
						<view class="task-info">
							<text class="task-name">{{ task.name }}</text>
							<text class="task-progress">已完成{{task.count }}/{{ task.day  }}</text>
						</view>
						<view class="reward-icon">
							<image class="money-img" src="/static/jinbi.png"></image>
							<text class="reward-text">{{ task.experience }}</text>
						</view>

					</view>
					<view class="task-right">
						<view class="task-reward">
							
						</view>
						<view class="task-btn" :class="{ 'completed': task.count }">
							<text class="btn-text">{{ task.count?task.success_button_text:task.button_text }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 提现晒单 -->
		<view class="history-section">
			<text class="section-title">提现晒单</text>
			<view class="history-list">
				<view v-for="(item, index) in historyList" :key="item.id" class="history-item">
					<view class="user-avatar">
						<image class="avatar-img" src="/static/avatar-default.png"></image>
					</view>
					<view class="history-content">
						<view class="user-info">
							<text class="username">{{ item.username }}</text>
							<text class="date">{{ item.processed_time }}</text>
						</view>
						<text class="description">获得了{{ item.amount }}元现金，{{item.payment_method_text}}打款</text>
					</view>
					<text class="amount">+{{ item.amount }}</text>
				</view>
			</view>
		</view>
		<u-popup :show="signInShow" mode="center" @close="signInShow=false" >
			<view class="sign-in-success-modal">
				<iew class="sign-in-bg"></iew>
				<view class="modal-content">
					<text class="modal-title">恭喜获得</text>
					<view class="modal-title">{{ content }}</view>
					
				</view>
			</view>
		</u-popup>
		<!-- <u-popup :show="signInShow" mode="center" @close="close" @open="open">
            <view class="modal-content">
                <text>{{content}}</text>
            </view>
		</u-popup> -->
		<!-- <u-modal :show="signInShow" class="sign-in-modal" title="" width="300" :content='content'>
			<view class="modal-content">
				
			</view>
		</u-modal> -->
	</view>
</template>

<script>
import { rider } from "@/utils/api"
import { onLoad } from "uview-ui/libs/mixin/mixin"
export default {
	data() {
		return {
			grade: {},
			reminderEnabled: true,
			currentLevel: 2,
			currentCash: 100,
			taskList: [],
			signInShow: false,
			content: '10元现金',
			historyList: [
				{
					id: 1,
					username: '用户22222',
					date: '2025.06.01',
					description: '获得了23元现金，微信打款已到账！',
					amount: '23'
				},
				{
					id: 2,
					username: '用户22222',
					date: '2025.06.01',
					description: '获得了23元现金，微信打款已到账！',
					amount: '23'
				}
			],
			shareInfo: {
				title: '分享标题',
				desc: '分享描述',
				link: 'https://www.yourdomain.com/share', // 分享链接
				imgUrl: 'https://www.yourdomain.com/logo.png' // 分享图标
			}
		}
	},
	onLoad() {
		//我的等级
		this.getGradeInfo();
		//经验任务
		this.getTaskList();
		//提现
		this.Withdrawal();
	},

	methods: {
		//提现记录
		Withdrawal(){
			rider.withdrawalLog().then(res => {
				uni.hideLoading();
				this.historyList=res.data.data;
				console.log(res);
				
			})
		},
		//经验任务
		getTaskList(){
			rider.expTask().then(res => {
				uni.hideLoading();
				// res.data[1].count=1
				this.taskList = res.data;
				console.log(res)
				
			})
		},
		//我的等级
		getGradeInfo(){
			rider.MySingIn().then(res => {
				uni.hideLoading();
				res.data["currentEmp"]=res.data.required_points-res.data.empiric_value;
				res.data["percentage"]=res.data.empiric_value/res.data.required_points*100;
				this.grade = res.data;
				console.log(res)
				// this.currentLevel = res.data.level
				// this.currentCash = res.data.cash
			})
		},
		// 切换提醒
		toggleReminder(e) {
			this.reminderEnabled = e.detail.value
		},

		// 签到
		signIn() {
			rider.checkIn().then(res => {
				uni.hideLoading();
				if(res.msg=='今日已签到'){
					uni.showToast({
						title: res.msg,
						icon: 'none'
					})
				}else{
					this.signInShow=true;
					this.content = res.data.points+'元现金';
				}
				
				console.log(res)
			})
			// uni.showToast({
			// 	title: '签到成功',
			// 	icon: 'success'
			// })
		},

		// 提现
		withdraw(val) {
			if (val <= 0) {
				uni.showToast({
					title: '余额不足',
					icon: 'none'
				})
				return
			}
			uni.navigateTo({
				url: '/pages/earnMoney/withdraw?val='+val
			})

			// uni.showModal({
			// 	title: '确认提现',
			// 	content: `确定提现${this.currentCash}元吗？`,
			// 	success: (res) => {
			// 		if (res.confirm) {
			// 			uni.showToast({
			// 				title: '提现申请已提交',
			// 				icon: 'success'
			// 			})
			// 		}
			// 	}
			// })
		},

		// 处理任务
		handleTask(task) {
			if (task.completed) {
				uni.showToast({
					title: '任务已完成',
					icon: 'none'
				})
				return
			}

			console.log('执行任务:', task)

			switch (task.id) {
				case 1:
					this.watchVideo(task)
					break
				case 2:
					this.signIn(task);//签到
					break
				case 3:
					//这个页面暂时没有呢
					break
				case 4:
					this.recommend(task)
					break
				case 5:
					this.shareToFriend(task)
					break
				case 6:
					this.shareToMoments(task)
					break
			}
		},
		//去推荐
		recommend(task) {
			uni.navigateTo({
				url: '/pages/user/rider/recommend'
			})
		},

		// 看视频
		watchVideo(task) {
			uni.showToast({
				title: '跳转到视频页面',
				icon: 'none'
			})
		},

		// 打卡
		checkIn(task) {
			task.completed = true
			task.btnText = '已完成'
			uni.showToast({
				title: '打卡成功',
				icon: 'success'
			})
		},

		// 推荐
		recommend(task) {
			uni.showToast({
				title: '跳转到推荐页面',
				icon: 'none'
			})
		},

		// 分享好友
		shareToFriend(task) {
			this.shareToWechat('WXSceneSession'); // 微信好友
			// uni.showToast({
			// 	title: '分享到微信好友',
			// 	icon: 'none'
			// })
		},
		 // 分享到微信
		shareToWechat(scene) {
			console.log('shareToWechat', scene);
			// #ifdef APP-PLUS
			console.log('plus',plus);
			plus.share.getServices(services => {
				let weixin = null;
				for (let i = 0; i < services.length; i++) {
				if (services[i].id === 'weixin') {
					weixin = services[i];
					break;
				}
				}
				if (weixin) {
				console.log('weixin', weixin);
				weixin.share({
					type: 0,
					pictures: [this.shareInfo.imgUrl],
					href: this.shareInfo.link,
					title: this.shareInfo.title,
					content: this.shareInfo.desc,
					extra: {
					scene: scene
					}
				}, function() {
					uni.showToast({
					title: '分享成功',
					icon: 'success'
					});
				}, function(err) {
					uni.showToast({
					title: '分享失败: ' + err.message,
					icon: 'none'
					});
				});
				} else {
				uni.showToast({
					title: '未检测到微信客户端',
					icon: 'none'
				});
				}
			}, err => {
				uni.showToast({
				title: '获取分享服务失败: ' + err.message,
				icon: 'none'
				});
			});
			console.log('app');
			// #endif
			
			// #ifdef MP-WEIXIN
			console.log(' MP-WEIXIN');
			uni.share({
				provider: 'weixin',
				scene: scene === 'WXSceneSession' ? 'WXSceneSession' : 'WXSceneTimeline',
				type: 0,
				title: this.shareInfo.title,
				summary: this.shareInfo.desc,
				href: this.shareInfo.link,
				imageUrl: this.shareInfo.imgUrl,
				success: () => {
				uni.showToast({
					title: '分享成功',
					icon: 'success'
				});
				},
				fail: (err) => {
				uni.showToast({
					title: '分享失败: ' + err.errMsg,
					icon: 'none'
				});
				}
			});
			// #endif
			//#ifdef H5
			console.log('H5');
			if (navigator.share) { // 检查是否支持 Web Share API
			try {
				navigator.share({
				title: this.shareInfo.title,
				text: this.shareInfo.desc,
				url: this.shareInfo.link
				});
				uni.showToast({ title: '分享成功', icon: 'success' });
			} catch (err) {
				uni.showToast({ title: '分享取消', icon: 'none' });
			}
			} else {
			this.copyLink(this.shareInfo.link); // 降级为复制链接
			}
			// #endif

			// #ifdef MP-WEIXIN
			uni.share({
			provider: 'weixin',
			type: 0,
			title: shareInfo.title,
			summary: shareInfo.desc,
			href: shareInfo.link,
			success: () => uni.showToast({ title: '分享成功', icon: 'success' }),
			fail: (err) => uni.showToast({ title: '分享失败: ' + err.errMsg, icon: 'none' })
			});
			// #endif
    	},

		// 分享朋友圈
		shareToMoments(task) {
			this.shareToWechat('WXSceneTimeline'); // 微信朋友圈
			// uni.showToast({
			// 	title: '分享到朋友圈',
			// 	icon: 'none'
			// })
		}
	}
}
</script>

<style lang="scss" scoped>
.earn-money-page {
	min-height: 100vh;
	background: linear-gradient(180deg, #E8F8F5 0%, #F5F5F5 100%);
	padding: 20rpx 30rpx;
}

/* 顶部区域 */
.header-section {
	margin-top: 60rpx;
	margin-bottom: 30rpx;
}

.top-info {
	display: flex;
	justify-content: left;
	align-items: flex-start;
	margin-bottom: 40rpx;
	
	.level-info{
		width: 40%;
		padding-right: 10%;
		border-right:1rpx solid #c4c4c4 ;
	}
}

/* 我的等级 */
.level-section {
	display: flex;
	align-items: center;
	gap: 20rpx;
}

.level-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	overflow: hidden;
	background-color: #f0f0f0;
}
.money-img{
	width: 30rpx;
	height: 30rpx;
}
.avatar-img {
	width: 100%;
	height: 100%;
	object-fit: cover;
}

.level-info {
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.level-label {
	font-size: 24rpx;
	color: #666;
}

.level-value {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	line-height: 1;
}

.level-desc {
	font-size: 20rpx;
	color: #999;
}

/* 我的现金 */
.cash-section {
	display: flex;
	// align-items: center;
	gap: 20rpx;
	align-items:center;
	padding-left: 20rpx;
	justify-content: space-between;
    width: 45%;
}

.cash-info {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 4rpx;
}

.cash-label {
	font-size: 24rpx;
	color: #666;
}

.cash-value {
	font-size: 48rpx;
	font-weight: bold;
	color: #333;
	line-height: 1;
}

.withdraw-btn {
	background-color: #FFD700;
	border-radius: 30rpx;
	padding: 10rpx 30rpx;
	font-size:24rpx;
	text-align: center;
}

.btn-text {
	font-size: 24rpx;
	color: #333;
	font-weight: 500;
}
// .modal-content{
// 	background: url(@/static/sign-in-banner.png);
// 	background-size: 100% 100%;
// }
// ::v-deep .u-modal__content{
// 	background: url(@/static/sign-in-banner.png);
// 	background-size: 100% 100%;
// }

/* 签到横幅 */
.sign-banner {
	
	//display: flex;
	//justify-content: space-between;
	//align-items: center;
	
}

.sign-text {

	background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
	box-shadow: 0 4rpx 12rpx rgba(255, 165, 0, 0.3);
	border-radius: 16rpx;
	padding: 20rpx 30rpx;
	font-size: 28rpx;
	color: #333;
	font-weight: 600;
	text-align: center;
}

.reminder-toggle {
	display: flex;
	align-items: center;
	gap: 12rpx;
}

.toggle-text {
	font-size: 22rpx;
	color: #666;
}

.toggle-switch {
	transform: scale(0.8);
}

/* 经验任务 */
.tasks-section {
	margin-bottom: 30rpx;
}

.section-title {
	font-size: 32rpx;
	color: #333;
	font-weight: 600;
	margin-bottom: 20rpx;
	display: block;
}

.task-list {
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.task-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.task-left {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex: 1;
}

.task-icon {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
}

//.task-info {
//	flex: 1;
//}

.task-name {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	margin-bottom: 8rpx;
	display: block;
}

.task-progress {
	font-size: 22rpx;
	color: #999;
	display: block;
}

.task-right {
	display: flex;
	align-items: center;
	gap: 20rpx;
	flex-shrink: 0;
}

.task-reward {
	display: flex;
	align-items: center;
}

.reward-icon {
	
	border-radius: 20rpx;
	padding: 4rpx 12rpx;
	display: flex;
    align-items: center
}

.reward-text {
	font-size: 22rpx;
	color: #FF9500;
	font-weight: 500;
	padding-left: 10rpx;
}

.task-btn {
	background-color: #14B19E;
	border-radius: 30rpx;
	padding: 16rpx 24rpx;

	&.completed {
		background-color: #f0f0f0;

		.btn-text {
			color: #999;
		}
	}
}

/* 提现晒单 */
.history-section {
	margin-bottom: 30rpx;
}

.history-list {
	background-color: #fff;
	border-radius: 16rpx;
	overflow: hidden;
}

.history-item {
	display: flex;
	align-items: center;
	padding: 30rpx;
	border-bottom: 1rpx solid #f0f0f0;

	&:last-child {
		border-bottom: none;
	}
}

.user-avatar {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	overflow: hidden;
	background-color: #f0f0f0;
	margin-right: 20rpx;
	flex-shrink: 0;
}

.history-content {
	flex: 1;
	min-width: 0;
}

.user-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 8rpx;
}

.username {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.date {
	font-size: 22rpx;
	color: #999;
	background: #F5F5F5;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.description {
	font-size: 24rpx;
	color: #666;
	line-height: 1.4;
}

.amount {
	font-size: 32rpx;
	color: #FF4757;
	font-weight: bold;
	flex-shrink: 0;
}
//签到成功弹窗
::v-deep .u-popup__content{
	border:8rpx solid #fa7e88;
	 border-radius: 20rpx;
}

.sign-in-success-modal {
    width: 400rpx;
    height: 400rpx;
    border-radius: 20rpx;
    display: flex;
    justify-content: center;
    align-items: center;
	position: relative;
}
.sign-in-bg{
	position: absolute;
	background: url(@/static/sign-in-banner.png) no-repeat center center;
    background-size: contain;
	 width: 500rpx;
    height: 200rpx;
	top:-140rpx;
}

.modal-content {
    text-align: center;
    color: #333;
}

.modal-title {
    font-size: 48rpx;
    margin-bottom: 20rpx;
}

.modal-amount {
    font-size: 60rpx;
    color: #FF5722;
    font-weight: bold;
    margin-bottom: 40rpx;
}

.modal-button {
    width: 80%;
    padding: 20rpx 0;
    background-color: #FF5722;
    color: #fff;
    border: none;
    border-radius: 10rpx;
    font-size: 32rpx;
}

/* 全局页面样式 */
page {
	background-color: #f8f8f8;
}
</style>