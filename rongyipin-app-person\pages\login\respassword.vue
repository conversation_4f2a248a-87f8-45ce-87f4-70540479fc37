
<template>
	<view style="height: 100vh; position: relative;">
		<u-navbar class="pub_pading" left-arrow @click-left="onClickLeft" title="" />
		<div class="top_jl ">
			<u-cell-group inset>
				<u-cell center :border="false">
					<template #title>
						<view class="top_jl_box">
							<div>请输入新密码</div>
						</view>
					</template>
					<template #label>
						<view class="">
							请输入6-16位的数字和字母组合密码
						</view>
					</template>
				</u-cell>
			</u-cell-group>
		</div>
		<u-cell-group inset style="margin-top: 50rpx;" :border="false">
			<u-cell>
				<template #title>
					<input type="text" v-model="newPassword" placeholder="请输入新密码" />
				</template>
			</u-cell>
			<u-cell>
				<template #title>
					<input type="text" v-model="confirmPassword" placeholder="确认新密码" />
				</template>
			</u-cell>
			<u-cell :border="false">
				<template #title>
					<view class="">
						<u-button @click="handleLogin" color="#00E698" type="large"
							style="border: none; border-radius: 20rpx;">下一步</u-button>
					</view>
				</template>
			</u-cell>
		</u-cell-group>
	</view>
</template>

<script>
	import {
		resetpwd
	} from '../../utils/api.js'
    import { userApi } from "@/utils/api"
	export default {
		data() {
			return {
				phone: '',
				newPassword: '',
				confirmPassword: ''
			}
		},
		onLoad(e) {
			this.phone = e.phone
			console.log(this.phone)
		},
		methods: {
			onClickLeft() {
				uni.navigateBack();
			},
			handleLogin() {
				if (this.newPassword !== this.confirmPassword) {
					uni.showToast({
						title: '两次密码输入不一致',
						icon: 'none',
						duration: 2000,
					});
					return;
				}

				// 如果密码长度不符合要求
				if (this.newPassword.length < 6 || this.newPassword.length > 16) {
					uni.showToast({
						title: '密码长度应在6-16位之间',
						icon: 'none',
						duration: 2000,
					});
					return;
				}
				let data = {
					mobile: this.phone,
					newpassword: this.newPassword,
					newpassword2: this.confirmPassword
				}
				userApi.resetPwd(data).then(res => {
					if (res.code == 200) {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000,
						});
						// uni.setStorageSync("token", res.data.token)
						uni.switchTab({
							url: "/pages/login/login"
						})
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000,
						});
					}
					console.log(res);
				})
			}
		}
	}
</script>
<style scoped>
	.top_jl_box {
		display: flex;
		align-items: center;
		font-weight: 800;
		font-size: 36rpx;
	}

	.top_jl {
		margin-top: 20rpx;
	}

	.top_jl :deep(.u-cell__title) {
		flex: 1;
	}

	.zp_img {
		width: 80rpx;
		height: 80rpx;
	}

	.zp_img image {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.mg_lt :deep(.u-cell__title) {
		margin-left: 20px;
	}

	text {
		color: #11C289;
	}

	.icon_phone {
		width: 50rpx;
		height: 50rpx;
	}

	.icon_phone image {
		width: 100%;
		height: 100%;
	}

	.login-options {
		display: flex;
		align-items: center;
		/* 垂直居中 */
		/* 靠下对齐 */
		flex-direction: column;
		/* 垂直排列 */
		/* 添加这个来让内容推到下方 */
	}

	.title {
		font-size: 28rpx;
		font-weight: 600;
		margin-bottom: 10rpx;
		/* 调整标题和图标之间的间距 */
	}
</style>