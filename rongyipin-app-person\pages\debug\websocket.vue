<template>
    <view class="debug-container">
        <view class="debug-header">
            <text class="debug-title">WebSocket 调试工具</text>
        </view>

        <!-- 连接状态 -->
        <view class="status-section">
            <view class="section-title">连接状态</view>
            <view class="status-item">
                <text class="status-label">连接状态:</text>
                <text :class="['status-value', status.isConnected ? 'connected' : 'disconnected']">
                    {{ status.isConnected ? '已连接' : '未连接' }}
                </text>
            </view>
            <view class="status-item">
                <text class="status-label">重连次数:</text>
                <text class="status-value">{{ status.reconnectAttempts }}</text>
            </view>
            <view class="status-item">
                <text class="status-label">服务器地址:</text>
                <text class="status-value">{{ status.serverUrl }}</text>
            </view>
            <view class="status-item">
                <text class="status-label">Token状态:</text>
                <text :class="['status-value', status.hasToken ? 'connected' : 'disconnected']">
                    {{ status.hasToken ? '已设置' : '未设置' }}
                </text>
            </view>
        </view>

        <!-- 服务器配置 -->
        <view class="config-section">
            <view class="section-title">服务器配置</view>
            <view class="input-group">
                <text class="input-label">服务器地址:</text>
                <input class="input-field" v-model="serverUrl" placeholder="ws://*************/" />
            </view>
            <button class="action-btn" @click="updateServerUrl">更新服务器地址</button>
        </view>

        <!-- 操作按钮 -->
        <view class="action-section">
            <view class="section-title">操作</view>
            <view class="button-group">
                <button class="action-btn primary" @click="connect">连接</button>
                <button class="action-btn" @click="disconnect">断开</button>
                <button class="action-btn" @click="reset">重置</button>
                <button class="action-btn" @click="refreshStatus">刷新状态</button>
                <button class="action-btn" @click="printInterfaces">打印监听接口</button>
            </view>
        </view>

        <!-- 测试消息 -->
        <view class="test-section">
            <view class="section-title">测试消息</view>
            <view class="input-group">
                <text class="input-label">测试消息:</text>
                <textarea class="textarea-field" v-model="testMessage" placeholder='{"event": "test", "data": {}}'></textarea>
            </view>
            <view class="button-group">
                <button class="action-btn" @click="sendTestMessage">发送测试消息</button>
                <button class="action-btn" @click="sendChatTestMessage">发送聊天测试消息</button>
            </view>
        </view>

        <!-- 消息日志 -->
        <view class="log-section">
            <view class="section-title">
                消息日志
                <button class="clear-btn" @click="clearLogs">清空</button>
            </view>
            <scroll-view class="log-container" scroll-y="true">
                <view class="log-item" v-for="(log, index) in logs" :key="index" :class="log.type">
                    <text class="log-time">{{ log.time }}</text>
                    <text class="log-content">{{ log.message }}</text>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script>
import webSocketService from '@/utils/websocket.js'

export default {
    data() {
        return {
            status: {
                isConnected: false,
                reconnectAttempts: 0,
                serverUrl: '',
                hasToken: false
            },
            serverUrl: 'ws://*************/',
            testMessage: '{"event": "test", "data": {}}',
            logs: []
        }
    },
    
    onLoad() {
        this.refreshStatus()
        this.setupWebSocketListeners()
        this.startStatusTimer()
    },
    
    onUnload() {
        this.removeWebSocketListeners()
        if (this.statusTimer) {
            clearInterval(this.statusTimer)
        }
    },
    
    methods: {
        // 刷新状态
        refreshStatus() {
            this.status = webSocketService.getStatus()
            this.serverUrl = this.status.serverUrl
        },

        // 打印监听接口信息
        printInterfaces() {
            this.addLog('info', '打印监听接口信息到控制台...')
            webSocketService.printListeningInterfaces()
            this.addLog('success', '监听接口信息已打印到控制台，请查看浏览器控制台')
        },
        
        // 连接
        connect() {
            this.addLog('info', '尝试连接WebSocket...')
            const success = webSocketService.init()
            if (success) {
                this.addLog('success', 'WebSocket初始化成功')
            } else {
                this.addLog('error', 'WebSocket初始化失败')
            }
            this.refreshStatus()
        },
        
        // 断开连接
        disconnect() {
            this.addLog('info', '断开WebSocket连接...')
            webSocketService.close()
            this.refreshStatus()
        },
        
        // 重置连接
        reset() {
            this.addLog('info', '重置WebSocket连接...')
            const success = webSocketService.reset()
            if (success) {
                this.addLog('success', 'WebSocket重置成功')
            } else {
                this.addLog('error', 'WebSocket重置失败')
            }
            this.refreshStatus()
        },
        
        // 更新服务器地址
        updateServerUrl() {
            if (!this.serverUrl.trim()) {
                uni.showToast({
                    title: '请输入服务器地址',
                    icon: 'none'
                })
                return
            }
            
            this.addLog('info', `更新服务器地址: ${this.serverUrl}`)
            webSocketService.updateServerUrl(this.serverUrl)
            this.refreshStatus()
        },
        
        // 发送测试消息
        sendTestMessage() {
            if (!this.testMessage.trim()) {
                uni.showToast({
                    title: '请输入测试消息',
                    icon: 'none'
                })
                return
            }

            try {
                const message = JSON.parse(this.testMessage)
                const success = webSocketService.send(message)
                if (success) {
                    this.addLog('success', `发送消息: ${this.testMessage}`)
                } else {
                    this.addLog('error', '发送消息失败')
                }
            } catch (error) {
                this.addLog('error', `消息格式错误: ${error.message}`)
            }
        },

        // 发送聊天测试消息 - 用于测试全局监听
        sendChatTestMessage() {
            const chatMessage = {
                event: 'private_msg',
                data: {
                    to: 'test_user_id',
                    content: '这是一条测试消息，用于触发全局监听',
                    type: 'text',
                    timestamp: Date.now()
                }
            }

            const success = webSocketService.send(chatMessage)
            if (success) {
                this.addLog('success', `发送聊天测试消息: ${JSON.stringify(chatMessage)}`)
                this.addLog('info', '如果全局监听正常，应该会在控制台看到 "111"')
            } else {
                this.addLog('error', '发送聊天测试消息失败')
            }
        },
        
        // 设置WebSocket监听器
        setupWebSocketListeners() {
            webSocketService.on('connected', this.onConnected)
            webSocketService.on('chatListUpdate', this.onChatListUpdate)
            webSocketService.on('private_msg', this.onPrivateMessage)
        },
        
        // 移除WebSocket监听器
        removeWebSocketListeners() {
            webSocketService.off('connected', this.onConnected)
            webSocketService.off('chatListUpdate', this.onChatListUpdate)
            webSocketService.off('private_msg', this.onPrivateMessage)
        },
        
        // 连接成功回调
        onConnected() {
            this.addLog('success', 'WebSocket连接成功')
            this.refreshStatus()
        },
        
        // 聊天列表更新回调
        onChatListUpdate(data) {
            this.addLog('info', `收到聊天列表更新: ${JSON.stringify(data)}`)
        },
        
        // 私聊消息回调
        onPrivateMessage(data) {
            this.addLog('info', `收到私聊消息: ${JSON.stringify(data)}`)
        },
        
        // 添加日志
        addLog(type, message) {
            const now = new Date()
            const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`
            
            this.logs.unshift({
                type,
                time,
                message
            })
            
            // 限制日志数量
            if (this.logs.length > 100) {
                this.logs = this.logs.slice(0, 100)
            }
        },
        
        // 清空日志
        clearLogs() {
            this.logs = []
        },
        
        // 启动状态定时器
        startStatusTimer() {
            this.statusTimer = setInterval(() => {
                this.refreshStatus()
            }, 2000)
        }
    }
}
</script>

<style lang="scss" scoped>
.debug-container {
    padding: 20rpx;
    background-color: #f5f5f5;
    min-height: 100vh;
}

.debug-header {
    text-align: center;
    margin-bottom: 30rpx;
    
    .debug-title {
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
    }
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-section, .config-section, .action-section, .test-section, .log-section {
    background-color: white;
    border-radius: 12rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15rpx;
    
    .status-label {
        font-size: 28rpx;
        color: #666;
    }
    
    .status-value {
        font-size: 28rpx;
        font-weight: bold;
        
        &.connected {
            color: #4CAF50;
        }
        
        &.disconnected {
            color: #F44336;
        }
    }
}

.input-group {
    margin-bottom: 20rpx;
    
    .input-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 10rpx;
    }
    
    .input-field, .textarea-field {
        width: 100%;
        padding: 20rpx;
        border: 1rpx solid #ddd;
        border-radius: 8rpx;
        font-size: 28rpx;
        box-sizing: border-box;
    }
    
    .textarea-field {
        height: 120rpx;
        resize: none;
    }
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 20rpx;
}

.action-btn {
    padding: 20rpx 30rpx;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
    background-color: #f0f0f0;
    color: #333;
    
    &.primary {
        background-color: #1CBBB4;
        color: white;
    }
}

.clear-btn {
    padding: 10rpx 20rpx;
    border: none;
    border-radius: 6rpx;
    font-size: 24rpx;
    background-color: #f44336;
    color: white;
}

.log-container {
    height: 400rpx;
    border: 1rpx solid #ddd;
    border-radius: 8rpx;
    padding: 20rpx;
}

.log-item {
    margin-bottom: 15rpx;
    padding: 15rpx;
    border-radius: 6rpx;
    
    &.info {
        background-color: #e3f2fd;
        border-left: 4rpx solid #2196F3;
    }
    
    &.success {
        background-color: #e8f5e8;
        border-left: 4rpx solid #4CAF50;
    }
    
    &.error {
        background-color: #ffebee;
        border-left: 4rpx solid #F44336;
    }
    
    .log-time {
        font-size: 24rpx;
        color: #999;
        margin-right: 20rpx;
    }
    
    .log-content {
        font-size: 26rpx;
        color: #333;
    }
}
</style>
