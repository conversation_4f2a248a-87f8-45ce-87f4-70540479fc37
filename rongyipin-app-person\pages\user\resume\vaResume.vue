<template>
    <view class="attachment-resume-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" :title=resumeList.attachment_name :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
                <template #right>
                    <u-icon name="more-dot-fill" size="20" color="#333"></u-icon>
                </template>
            </u-navbar>
        </view>

        <!-- 内容区域 -->
        <view class="content-container">
            <!-- 使用Canvas PDF查看器 -->
            <simple-pdf-viewer
                :pdfUrl="resumeList.storage_path"
                :fileName="resumeList.attachment_name"
                :showControls="true"
                :autoLoad="true"
                @loaded="onPdfLoaded"
                @pageChange="onPageChange"
                @scaleChange="onScaleChange"
                @error="onPdfError"
            ></simple-pdf-viewer>
        </view>


    </view>
</template>

<script>
import SimplePdfViewer from '@/components/simple-pdf-viewer/simple-pdf-viewer.vue'

export default {
    components: {
        SimplePdfViewer
    },
    data() {
        return {
            resumeList: {}, // 简历列表
            currentPage: 1,
            totalPages: 0,
            currentScale: 1
        }
    },
    onLoad(options) {
        console.log('页面参数:', options)
        try {
            // 兼容不同的参数传递方式
            if (options.path) {
                this.resumeList = JSON.parse(decodeURIComponent(options.path))
            } else if (options.item) {
                this.resumeList = JSON.parse(decodeURIComponent(options.item))
            }
            console.log('简历信息:', this.resumeList)
        } catch (e) {
            console.error('参数解析失败:', e)
            uni.showToast({
                title: '参数错误',
                icon: 'none'
            })
        }
    },

    methods: {
        // PDF加载完成回调
        onPdfLoaded(data) {
            console.log('PDF加载完成:', data)
            this.totalPages = data.totalPages
            this.currentPage = data.currentPage

            uni.showToast({
                title: 'PDF加载成功',
                icon: 'success'
            })
        },

        // 页面变化回调
        onPageChange(data) {
            console.log('页面变化:', data)
            this.currentPage = data.currentPage
            this.totalPages = data.totalPages
        },

        // 缩放变化回调
        onScaleChange(data) {
            console.log('缩放变化:', data)
            this.currentScale = data.scale
        },

        // PDF错误回调
        onPdfError(data) {
            console.error('PDF查看器错误:', data)
            uni.showModal({
                title: '预览失败',
                content: data.message || 'PDF文档无法预览，是否下载查看？',
                confirmText: '下载',
                cancelText: '取消',
                success: (res) => {
                    if (res.confirm) {
                        this.downloadFile()
                    }
                }
            })
        },

        // 下载文件
        async downloadFile() {
            console.log('开始下载文件')

            // #ifdef APP-PLUS
            await this.downloadForApp()
            // #endif

            // #ifdef H5
            this.downloadForH5()
            // #endif

            // #ifdef MP-WEIXIN
            this.downloadForWeixin()
            // #endif
        },
        // APP端下载
        async downloadForApp() {
            uni.showLoading({ title: '下载中...' })

            try {
                const downloadTask = uni.downloadFile({
                    url: this.resumeList.storage_path,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            // 下载成功，打开文件
                            uni.openDocument({
                                filePath: res.tempFilePath,
                                showMenu: true,
                                success: () => {
                                    console.log('文件打开成功')
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '文件已打开',
                                        icon: 'success'
                                    })
                                },
                                fail: (error) => {
                                    console.error('文件打开失败:', error)
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '无法打开文件',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    },
                    fail: (error) => {
                        console.error('下载失败:', error)
                        uni.hideLoading()
                        uni.showToast({
                            title: '下载失败',
                            icon: 'none'
                        })
                    }
                })

                // 监听下载进度
                downloadTask.onProgressUpdate((res) => {
                    console.log('下载进度:', res.progress)
                    uni.showLoading({ title: `下载中...${res.progress}%` })
                })

            } catch (error) {
                console.error('下载异常:', error)
                uni.hideLoading()
                uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                })
            }
        },

        // H5端下载
        downloadForH5() {
            try {
                const link = document.createElement('a')
                link.href = this.resumeList.storage_path
                link.download = this.resumeList.attachment_name || 'resume.pdf'
                link.target = '_blank'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)

                uni.showToast({
                    title: '开始下载',
                    icon: 'success'
                })
            } catch (error) {
                console.error('H5下载失败:', error)
                uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                })
            }
        },

        // 微信小程序下载
        downloadForWeixin() {
            uni.showToast({
                title: '请在浏览器中下载',
                icon: 'none'
            })
        }
        onNavigationBarButtonTap() {
            var url = this.pdfUrl
            var title = ''
            let index = url.lastIndexOf(".")
            let titleTwo = url.lastIndexOf("/")
            title = this.title ? (this.title + url.substring(index, url.length)) : titleTwo
            console.log(title, '////');

            let dtask = plus.downloader.createDownload(url, {
                //本地路径开头使用file://，跟上手机文件本地目录storage/emulated/0，就是用户文件管理器能看到的了，之后我创建微垠作为文件夹，后缀是用于文件命名和格式修改，大家可以使用变量。
                filename: "file://storage/emulated/0/mdej/" + title //利用保存路径，实现下载文件的重命名
            },
                function (d, status) {
                    //d为下载的文件对象
                    if (status == 200) {
                        //下载成功,d.filename是文件在保存在本地的相对路径，使用下面的API可转为平台绝对路径
                        let fileSaveUrl = plus.io.convertLocalFileSystemURL(d.filename);
                        plus.runtime.openFile(d.filename); //选择软件打开文件
                        uni.showToast({
                            title: '文件保存路径' + d.filename,
                            icon: 'none',
                            duration: 6000,
                        })
                    } else {
                        //下载失败
                        plus.downloader.clear(); //清除下载任务
                    }
                })
            dtask.start();
        }
    }
}
</script>

<style lang="scss" scoped>
.attachment-resume-page {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background-color: #fff;
}

/* 内容容器 */
.content-container {
    flex: 1;
    margin-top: 88rpx;
    padding: 30rpx;
}

/* 简历列表 */
.resume-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.resume-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.pdf-icon {
    margin-right: 24rpx;
}

.file-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.file-name {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.name-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.default-tag {
    background-color: #14B19E;
    color: #fff;
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 8rpx;
}

.update-time {
    font-size: 24rpx;
    color: #999;
}

.action-buttons {
    display: flex;
    gap: 24rpx;
}

.action-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 12rpx;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 60rpx;
    text-align: center;
}

.empty-icon {
    margin-bottom: 40rpx;
}

.empty-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.empty-desc {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60rpx;
}

.upload-btn {
    background-color: #14B19E;
    border-radius: 50rpx;
    padding: 24rpx 48rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.upload-text {
    font-size: 28rpx;
    color: #fff;
    font-weight: 500;
}

/* 底部上传按钮 */
.bottom-upload {
    position: fixed;
    bottom: 60rpx;
    left: 30rpx;
    right: 30rpx;
    z-index: 100;
}

.bottom-upload .upload-btn {
    // width: 100%;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(20, 177, 158, 0.3);
}
</style>
