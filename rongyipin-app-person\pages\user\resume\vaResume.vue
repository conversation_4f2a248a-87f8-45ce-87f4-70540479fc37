<template>
    <view class="attachment-resume-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" :title=resumeList.attachment_name :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
                <template #right>
                    <u-icon name="more-dot-fill" size="20" color="#333"></u-icon>
                </template>
            </u-navbar>
        </view>

        <!-- 内容区域 -->
        <view class="image-container">
            <!-- 可移动区域 -->
            <movable-area class="movable-area">
                <movable-view
                    class="movable-view"
                    direction="all"
                    :scale="true"
                    :scale-min="0.5"
                    :scale-max="4"
                    :scale-value="currentScale"
                    @scale="onScale"
                    @change="onChange"
                    :x="moveX"
                    :y="moveY">
                    <image
                        :src="pageUrl"
                        mode="aspectFit"
                        class="resume-image"
                        @load="onImageLoad"
                        @error="onImageError"
                        @tap="onImageTap">
                    </image>
                </movable-view>
            </movable-area>

            <!-- 控制工具栏 -->
            <view class="control-toolbar">
                <view class="toolbar-left">
                    <button class="control-btn" @click="zoomOut">
                        <u-icon name="minus" size="20" color="#fff"></u-icon>
                    </button>
                    <text class="scale-text">{{ Math.round(currentScale * 100) }}%</text>
                    <button class="control-btn" @click="zoomIn">
                        <u-icon name="plus" size="20" color="#fff"></u-icon>
                    </button>
                </view>
                <view class="toolbar-right">
                    <button class="control-btn" @click="resetPosition">
                        <u-icon name="reload" size="20" color="#fff"></u-icon>
                    </button>
                    <button class="control-btn" @click="downloadImage">
                        <u-icon name="download" size="20" color="#fff"></u-icon>
                    </button>
                </view>
            </view>

            <!-- 加载状态 -->
            <view v-if="imageLoading" class="loading-overlay">
                <u-loading-icon mode="spinner" size="40" color="#14B19E"></u-loading-icon>
                <text class="loading-text">图片加载中...</text>
            </view>
        </view>


    </view>
</template>

<script>
import { filelate } from "@/utils/api.js"
export default {
    data() {
        return {
            resumeList: {},
            pageUrl: 'http://superluosen.top/backend/20250805/9b0abfc0a3e58818a297182faa8af12b.png', // 简历图片URL

            // 图片缩放和移动相关
            currentScale: 1, // 当前缩放比例
            moveX: 0, // X轴移动距离
            moveY: 0, // Y轴移动距离
            imageLoading: true, // 图片加载状态

            // 图片信息
            imageWidth: 0,
            imageHeight: 0,

            // 系统信息
            systemInfo: {},

            // 缩放限制
            minScale: 0.5,
            maxScale: 4
        }
    },
    onLoad(options) {
        console.log('页面参数:', options)

        // 获取系统信息
        this.systemInfo = uni.getSystemInfoSync()
        console.log('系统信息:', this.systemInfo)

        try {
            // 解析传入的简历信息
            if (options.path) {
                this.resumeList = JSON.parse(decodeURIComponent(options.path))
                console.log('简历信息:', this.resumeList)

                // 如果有图片URL，使用传入的URL
                if (this.resumeList.image_url) {
                    this.pageUrl = this.resumeList.image_url
                }
            }
        } catch (e) {
            console.error('参数解析失败:', e)
        }
    },

    methods: {
        // 图片加载完成
        onImageLoad(e) {
            console.log('图片加载完成:', e)
            this.imageLoading = false

            // 获取图片原始尺寸
            this.imageWidth = e.detail.width
            this.imageHeight = e.detail.height

            console.log('图片尺寸:', { width: this.imageWidth, height: this.imageHeight })
        },

        // 图片加载失败
        onImageError(e) {
            console.error('图片加载失败:', e)
            this.imageLoading = false
            uni.showToast({
                title: '图片加载失败',
                icon: 'none'
            })
        },

        // 图片点击事件
        onImageTap() {
            console.log('图片被点击')
            // 可以在这里添加双击放大等功能
        },

        // 缩放事件
        onScale(e) {
            console.log('缩放事件:', e.detail)
            this.currentScale = e.detail.scale
        },

        // 移动事件
        onChange(e) {
            console.log('移动事件:', e.detail)
            this.moveX = e.detail.x
            this.moveY = e.detail.y
        },

        // 放大
        zoomIn() {
            if (this.currentScale < this.maxScale) {
                this.currentScale = Math.min(this.currentScale + 0.25, this.maxScale)
                console.log('放大到:', this.currentScale)
            }
        },

        // 缩小
        zoomOut() {
            if (this.currentScale > this.minScale) {
                this.currentScale = Math.max(this.currentScale - 0.25, this.minScale)
                console.log('缩小到:', this.currentScale)
            }
        },

        // 重置位置和缩放
        resetPosition() {
            this.currentScale = 1
            this.moveX = 0
            this.moveY = 0
            console.log('重置位置和缩放')
        },

        // 下载图片
        async downloadImage() {
            try {
                uni.showLoading({ title: '准备下载...' })

                // #ifdef APP-PLUS
                // APP端下载图片到相册
                uni.downloadFile({
                    url: this.pageUrl,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success: () => {
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '保存成功',
                                        icon: 'success'
                                    })
                                },
                                fail: () => {
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '保存失败',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    },
                    fail: () => {
                        uni.hideLoading()
                        uni.showToast({
                            title: '下载失败',
                            icon: 'none'
                        })
                    }
                })
                // #endif

                // #ifdef H5
                // H5端打开新窗口
                uni.hideLoading()
                window.open(this.pageUrl, '_blank')
                // #endif

                // #ifdef MP-WEIXIN
                // 微信小程序保存到相册
                uni.downloadFile({
                    url: this.pageUrl,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            uni.saveImageToPhotosAlbum({
                                filePath: res.tempFilePath,
                                success: () => {
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '保存成功',
                                        icon: 'success'
                                    })
                                },
                                fail: () => {
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '保存失败',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    },
                    fail: () => {
                        uni.hideLoading()
                        uni.showToast({
                            title: '下载失败',
                            icon: 'none'
                        })
                    }
                })
                // #endif

            } catch (error) {
                uni.hideLoading()
                console.error('下载失败:', error)
                uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                })
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.attachment-resume-page {
    min-height: 100vh;
    background-color: #000;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* 图片容器 */
.image-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    margin-top: 88rpx; /* 导航栏高度 */
}

/* 可移动区域 */
.movable-area {
    width: 100%;
    height: 100%;
    background-color: #000;
}

.movable-view {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 简历图片 */
.resume-image {
    max-width: 100%;
    max-height: 100%;
    display: block;
}

/* 控制工具栏 */
.control-toolbar {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 25px;
    padding: 10px 20px;
    gap: 20px;
    z-index: 100;
}

.toolbar-left,
.toolbar-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.control-btn {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.control-btn:active {
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0.95);
}

.scale-text {
    font-size: 14px;
    color: #fff;
    font-weight: 500;
    min-width: 50px;
    text-align: center;
}

/* 加载状态 */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 200;
    gap: 15px;
}

.loading-text {
    font-size: 14px;
    color: #fff;
}

/* 导航栏样式调整 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background-color: rgba(0, 0, 0, 0.8);
}

/* 响应式调整 */
@media screen and (max-width: 750rpx) {
    .control-toolbar {
        bottom: 20px;
        padding: 8px 15px;
        gap: 15px;
    }

    .control-btn {
        width: 36px;
        height: 36px;
    }

    .scale-text {
        font-size: 12px;
        min-width: 45px;
    }
}

/* 适配安全区域 */
.control-toolbar {
    bottom: calc(30px + env(safe-area-inset-bottom));
}
</style>
