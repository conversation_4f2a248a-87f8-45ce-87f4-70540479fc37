<template>
    <view class="attachment-resume-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" :title=resumeList.attachment_name :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
                <template #right>
                    <u-icon name="more-dot-fill" size="20" color="#333"></u-icon>
                </template>
            </u-navbar>
        </view>

        <!-- 内容区域 -->
        <view class="content-container">
            <!-- 有数据时显示列表 -->
            <web-view v-if="allUrl" :src="allUrl" :update-title='false'></web-view>
        </view>


    </view>
</template>

<script>
import { filelate } from "@/utils/api.js"
export default {
    data() {
        return {
            resumeList: {}, // 简历列表
            allUrl: '',
            pdfUrl: '',
            title: ''
        }
    },
    onLoad(options) {
        console.log(options)
        try {
            this.resumeList = JSON.parse(decodeURIComponent(options.path))
            console.log(this.resumeList)
            this.allUrl = this.resumeList.storage_path
            this.pdfUrl = this.resumeList.attachment_name
        } catch (e) {
            console.error('参数解析失败:', e)
        }

    },

    methods: {
        loadPdf(pdfUrl, title) {
            /* #ifdef APP-PLUS */
            switch (uni.getSystemInfoSync().platform) {
                case 'android':
                    console.log("loadPdf", '运行Android上');
                    this.loadPdfOnAndroid(pdfUrl)
                    break;
                case 'ios':
                    console.log("loadPdf", '运行iOS上');
                    this.loadPdfOnIos(pdfUrl)
                    break;
                default:
                    console.log("loadPdf", '运行在开发者工具上');
                    break;
            }
            /* #endif */
            /* #ifdef H5 */
            this.allUrl = pdfUrl
            uni.setNavigationBarTitle({
                title
            })
            /* #endif */
        },
        async loadPdfOnAndroid(pdfUrl) {
            let tempFilePath = await this.downloadPdf(pdfUrl)
            let localFilePath = plus.io.convertLocalFileSystemURL(tempFilePath)
            console.log("downloadFile=======localFilePath", localFilePath)
            this.allUrl = this.viewerUrl + '?file=' + encodeURIComponent(localFilePath)
            console.log("downloadFile=======this.allUrl", this.allUrl)
        },
        async loadPdfOnIos(pdfUrl) {
            let tempFilePath = await this.downloadPdf(pdfUrl)
            uni.openDocument({
                filePath: tempFilePath,
                showMenu: true,
                success: function (res) {
                    console.log('打开文档成功');
                }
            });
        },
        downloadPdf(pdfUrl) {
            return new Promise(resolve => {
                uni.downloadFile({
                    url: pdfUrl,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            let tempFilePath = res.tempFilePath
                            uni.getFileInfo({
                                filePath: tempFilePath,
                                success: async imgInfo => {
                                    console.log("downloadPdf=======getFileInfo", imgInfo.size);
                                    if (imgInfo.size) return resolve(tempFilePath)
                                    else return resolve(await this.downloadPdf(pdfUrl))
                                }
                            })
                        }
                    },
                    async fail(err) {
                        return resolve(await this.downloadPdf(pdfUrl))
                    }
                });
            })
        },
        onNavigationBarButtonTap() {
            var url = this.pdfUrl
            var title = ''
            let index = url.lastIndexOf(".")
            let titleTwo = url.lastIndexOf("/")
            title = this.title ? (this.title + url.substring(index, url.length)) : titleTwo
            console.log(title, '////');

            let dtask = plus.downloader.createDownload(url, {
                //本地路径开头使用file://，跟上手机文件本地目录storage/emulated/0，就是用户文件管理器能看到的了，之后我创建微垠作为文件夹，后缀是用于文件命名和格式修改，大家可以使用变量。
                filename: "file://storage/emulated/0/mdej/" + title //利用保存路径，实现下载文件的重命名
            },
                function (d, status) {
                    //d为下载的文件对象
                    if (status == 200) {
                        //下载成功,d.filename是文件在保存在本地的相对路径，使用下面的API可转为平台绝对路径
                        let fileSaveUrl = plus.io.convertLocalFileSystemURL(d.filename);
                        plus.runtime.openFile(d.filename); //选择软件打开文件
                        uni.showToast({
                            title: '文件保存路径' + d.filename,
                            icon: 'none',
                            duration: 6000,
                        })
                    } else {
                        //下载失败
                        plus.downloader.clear(); //清除下载任务
                    }
                })
            dtask.start();
        }
    }
}
</script>

<style lang="scss" scoped>
.attachment-resume-page {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background-color: #fff;
}

/* 内容容器 */
.content-container {
    flex: 1;
    margin-top: 88rpx;
    padding: 30rpx;
}

/* 简历列表 */
.resume-list {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
}

.resume-item {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.pdf-icon {
    margin-right: 24rpx;
}

.file-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;
}

.file-name {
    display: flex;
    align-items: center;
    gap: 16rpx;
}

.name-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
}

.default-tag {
    background-color: #14B19E;
    color: #fff;
    font-size: 20rpx;
    padding: 4rpx 12rpx;
    border-radius: 8rpx;
}

.update-time {
    font-size: 24rpx;
    color: #999;
}

.action-buttons {
    display: flex;
    gap: 24rpx;
}

.action-btn {
    width: 60rpx;
    height: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 12rpx;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 120rpx 60rpx;
    text-align: center;
}

.empty-icon {
    margin-bottom: 40rpx;
}

.empty-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
}

.empty-desc {
    font-size: 28rpx;
    color: #999;
    margin-bottom: 60rpx;
}

.upload-btn {
    background-color: #14B19E;
    border-radius: 50rpx;
    padding: 24rpx 48rpx;
    display: flex;
    align-items: center;
    gap: 12rpx;
}

.upload-text {
    font-size: 28rpx;
    color: #fff;
    font-weight: 500;
}

/* 底部上传按钮 */
.bottom-upload {
    position: fixed;
    bottom: 60rpx;
    left: 30rpx;
    right: 30rpx;
    z-index: 100;
}

.bottom-upload .upload-btn {
    // width: 100%;
    justify-content: center;
    box-shadow: 0 8rpx 24rpx rgba(20, 177, 158, 0.3);
}
</style>
