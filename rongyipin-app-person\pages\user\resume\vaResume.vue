<template>
    <view class="attachment-resume-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" :title=resumePages.attachment_name :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
                <template #right>
                    <u-icon name="more-dot-fill" size="20" color="#333"></u-icon>
                </template>
            </u-navbar>
        </view>
        <vo>
        <!-- 内容区域 -->
       <scroll-view scroll-y="true" class="scroll-content">
            <!-- 渲染简历的每一页图片 -->
            <image
                v-for="(pageUrl, index) in resumePages"
                :key="index"
                :src="pageUrl"
                mode="widthFix"
                class="resume-page-image"
            ></image>
        </scroll-view>


    </view>
</template>

<script>
import { resume } from "@/utils/api.js"
export default {
    data() {
        return {
            resumePages:[] , // 简历列表 
            allUrl: '',
            pdfUrl: '',
            title: '',
            loding: true
        }
    },
    async onLoad(options) {
        console.log(options)
        try {
           console.log(options)
            const res = await resume.ReumeAttachmentInfo({
                id: options.id
            })
            console.log('res:', res)
            if(res.code ==200){
                this.resumePages = res.data.image_path.split(',')
                setTimeout(() => {
                    this.loding =false
                }, 3000)
                console.log(this.resumePages)
            }
            else{
                uni.showToast({
                    title: res.msg,
                    icon: 'none'
                })

            }
            uni.hideLoading();
        } catch (e) {
            console.error('参数解析失败:', e)
        }

    },

    methods: {
        
    }
}
</script>

<style lang="scss" scoped>
.attachment-resume-page {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.navbar {
}
.scroll-content{
    display: flex;
    justify-content: center;
}
.scroll-content {
    flex: 1;
    background-color: #f5f5f5;
}
.resume-page-image {
    width: 100%;
    display: block; /* 消除图片底部间隙 */
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}
.bottom-actions {
    /* 底部操作栏样式 */
    display: flex;
    height: 60px;
    padding: 10px;
    background-color: #fff;
    border-top: 1px solid #eee;
}
.action-btn {
    /* 按钮样式 */
}
</style>
