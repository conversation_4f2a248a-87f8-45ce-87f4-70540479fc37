<template>
    <view class="attachment-resume-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" :title="resumeList.attachment_name || '简历预览'" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
                <template #right>
                    <view class="navbar-actions">
                        <u-icon name="download" size="20" color="#333" @click="downloadFile" style="margin-right: 15px;"></u-icon>
                        <u-icon name="more-dot-fill" size="20" color="#333" @click="showMoreActions"></u-icon>
                    </view>
                </template>
            </u-navbar>
        </view>

        <!-- 内容区域 -->
        <view class="content-container">
            <!-- 加载状态 -->
            <view v-if="isLoading" class="loading-container">
                <u-loading-icon mode="spinner" size="40" color="#14B19E"></u-loading-icon>
                <text class="loading-text">{{ loadingText }}</text>
            </view>

            <!-- PDF预览区域 -->
            <view v-else-if="previewUrl" class="pdf-preview-container">
                <!-- H5端使用iframe -->
                <!-- #ifdef H5 -->
                <iframe
                    :src="previewUrl"
                    class="pdf-iframe"
                    frameborder="0"
                    @load="onIframeLoad"
                    @error="onPreviewError"
                ></iframe>
                <!-- #endif -->

                <!-- APP端使用web-view -->
                <!-- #ifdef APP-PLUS -->
                <web-view
                    :src="previewUrl"
                    :update-title="false"
                    @message="onWebViewMessage"
                    @error="onPreviewError"
                    class="pdf-webview"
                ></web-view>
                <!-- #endif -->

                <!-- 微信小程序使用web-view -->
                <!-- #ifdef MP-WEIXIN -->
                <web-view
                    :src="previewUrl"
                    @message="onWebViewMessage"
                    @error="onPreviewError"
                    class="pdf-webview"
                ></web-view>
                <!-- #endif -->
            </view>

            <!-- 预览失败时的备用方案 -->
            <view v-else class="preview-fallback">
                <view class="fallback-content">
                    <u-icon name="file-pdf" size="80" color="#ff4757"></u-icon>
                    <text class="fallback-title">{{ resumeList.attachment_name || '简历文件' }}</text>
                    <text class="fallback-desc">无法在线预览此文件</text>

                    <view class="fallback-actions">
                        <button class="action-btn primary" @click="downloadAndOpen">
                            <u-icon name="download" size="18" color="#fff" style="margin-right: 8px;"></u-icon>
                            下载并打开
                        </button>
                        <button class="action-btn secondary" @click="openWithBrowser">
                            <u-icon name="link" size="18" color="#14B19E" style="margin-right: 8px;"></u-icon>
                            浏览器打开
                        </button>
                    </view>
                </view>
            </view>
        </view>

        <!-- 更多操作弹窗 -->
        <u-popup v-model="showMorePopup" mode="bottom" border-radius="20" :safe-area-inset-bottom="true">
            <view class="more-actions-popup">
                <view class="popup-header">
                    <text class="popup-title">更多操作</text>
                    <u-icon name="close" size="20" color="#666" @click="showMorePopup = false"></u-icon>
                </view>
                <view class="action-list">
                    <view class="action-item" @click="downloadFile">
                        <u-icon name="download" size="24" color="#333"></u-icon>
                        <text class="action-text">下载文件</text>
                    </view>
                    <view class="action-item" @click="shareFile">
                        <u-icon name="share" size="24" color="#333"></u-icon>
                        <text class="action-text">分享文件</text>
                    </view>
                    <view class="action-item" @click="openWithBrowser">
                        <u-icon name="link" size="24" color="#333"></u-icon>
                        <text class="action-text">浏览器打开</text>
                    </view>
                </view>
            </view>
        </u-popup>
    </view>
</template>

<script>
import { filelate } from "@/utils/api.js"
export default {
    data() {
        return {
            resumeList: {},
            previewUrl: '',
            isLoading: true,
            loadingText: '正在加载...',
            showMorePopup: false,
            previewError: false
        }
    },
    onLoad(options) {
        console.log('页面参数:', options)
        try {
            // 兼容不同的参数传递方式
            if (options.path) {
                this.resumeList = JSON.parse(decodeURIComponent(options.path))
            } else if (options.item) {
                this.resumeList = JSON.parse(decodeURIComponent(options.item))
            }
            console.log('简历信息:', this.resumeList)
            this.initPreview()
        } catch (e) {
            console.error('参数解析失败:', e)
            this.isLoading = false
            this.previewError = true
        }
    },

    methods: {
        // 初始化预览
        async initPreview() {
            this.isLoading = true
            this.loadingText = '正在准备预览...'

            try {
                const fileUrl = this.resumeList.storage_path
                if (!fileUrl) {
                    throw new Error('文件地址不存在')
                }

                await this.setupPreview(fileUrl)

            } catch (error) {
                console.error('预览初始化失败:', error)
                this.isLoading = false
                this.previewError = true
            }
        },

        // 设置预览方式
        async setupPreview(fileUrl) {
            const fileName = this.resumeList.attachment_name || ''
            const fileExt = fileName.split('.').pop()?.toLowerCase()

            console.log('文件信息:', { fileName, fileExt, fileUrl })

            // #ifdef H5
            // H5端使用PDF.js预览
            if (fileExt === 'pdf') {
                this.previewUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(fileUrl)}`
            } else {
                // 其他文件类型使用Google Docs预览
                this.previewUrl = `https://docs.google.com/viewer?url=${encodeURIComponent(fileUrl)}&embedded=true`
            }
            this.isLoading = false
            // #endif

            // #ifdef APP-PLUS
            // APP端优先尝试在线预览
            if (fileExt === 'pdf') {
                // 尝试使用PDF.js在线预览
                this.previewUrl = `https://mozilla.github.io/pdf.js/web/viewer.html?file=${encodeURIComponent(fileUrl)}`
                this.isLoading = false
            } else {
                // 其他文件类型直接显示
                this.previewUrl = fileUrl
                this.isLoading = false
            }
            // #endif

            // #ifdef MP-WEIXIN
            // 微信小程序直接使用web-view
            this.previewUrl = fileUrl
            this.isLoading = false
            // #endif
        },

        // iframe加载完成
        onIframeLoad() {
            console.log('PDF预览加载完成')
            this.isLoading = false
        },

        // 预览出错
        onPreviewError(error) {
            console.error('预览失败:', error)
            this.isLoading = false
            this.previewError = true
        },

        // web-view消息
        onWebViewMessage(event) {
            console.log('WebView消息:', event)
        },

        // 显示更多操作
        showMoreActions() {
            this.showMorePopup = true
        },

        // 下载并打开
        async downloadAndOpen() {
            console.log('下载并打开文件')

            // #ifdef APP-PLUS
            await this.downloadForApp()
            // #endif

            // #ifdef H5
            this.downloadForH5()
            // #endif

            // #ifdef MP-WEIXIN
            this.downloadForWeixin()
            // #endif
        },

        // 浏览器打开
        openWithBrowser() {
            // #ifdef APP-PLUS
            plus.runtime.openURL(this.resumeList.storage_path)
            // #endif

            // #ifdef H5
            window.open(this.resumeList.storage_path, '_blank')
            // #endif

            // #ifdef MP-WEIXIN
            uni.showToast({
                title: '请复制链接到浏览器打开',
                icon: 'none'
            })
            // #endif
        },

        // 分享文件
        shareFile() {
            // #ifdef APP-PLUS
            uni.share({
                provider: 'system',
                type: 0,
                href: this.resumeList.storage_path,
                title: this.resumeList.attachment_name || '简历文件',
                success: () => {
                    console.log('分享成功')
                },
                fail: (error) => {
                    console.error('分享失败:', error)
                }
            })
            // #endif

            // #ifdef H5
            if (navigator.share) {
                navigator.share({
                    title: this.resumeList.attachment_name || '简历文件',
                    url: this.resumeList.storage_path
                })
            } else {
                // 复制链接到剪贴板
                navigator.clipboard.writeText(this.resumeList.storage_path).then(() => {
                    uni.showToast({
                        title: '链接已复制',
                        icon: 'success'
                    })
                })
            }
            // #endif

            // #ifdef MP-WEIXIN
            uni.showToast({
                title: '暂不支持分享',
                icon: 'none'
            })
            // #endif

            this.showMorePopup = false
        },

        // 下载文件
        async downloadFile() {
            await this.downloadAndOpen()
            this.showMorePopup = false
        },
        // APP端下载
        async downloadForApp() {
            uni.showLoading({ title: '下载中...' })

            try {
                const downloadTask = uni.downloadFile({
                    url: this.resumeList.storage_path,
                    success: (res) => {
                        if (res.statusCode === 200) {
                            // 下载成功，打开文件
                            uni.openDocument({
                                filePath: res.tempFilePath,
                                showMenu: true,
                                success: () => {
                                    console.log('文件打开成功')
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '文件已打开',
                                        icon: 'success'
                                    })
                                },
                                fail: (error) => {
                                    console.error('文件打开失败:', error)
                                    uni.hideLoading()
                                    uni.showToast({
                                        title: '无法打开文件',
                                        icon: 'none'
                                    })
                                }
                            })
                        }
                    },
                    fail: (error) => {
                        console.error('下载失败:', error)
                        uni.hideLoading()
                        uni.showToast({
                            title: '下载失败',
                            icon: 'none'
                        })
                    }
                })

                // 监听下载进度
                downloadTask.onProgressUpdate((res) => {
                    console.log('下载进度:', res.progress)
                    uni.showLoading({ title: `下载中...${res.progress}%` })
                })

            } catch (error) {
                console.error('下载异常:', error)
                uni.hideLoading()
                uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                })
            }
        },

        // H5端下载
        downloadForH5() {
            try {
                const link = document.createElement('a')
                link.href = this.resumeList.storage_path
                link.download = this.resumeList.attachment_name || 'resume.pdf'
                link.target = '_blank'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)

                uni.showToast({
                    title: '开始下载',
                    icon: 'success'
                })
            } catch (error) {
                console.error('H5下载失败:', error)
                uni.showToast({
                    title: '下载失败',
                    icon: 'none'
                })
            }
        },

        // 微信小程序下载
        downloadForWeixin() {
            uni.showToast({
                title: '请在浏览器中下载',
                icon: 'none'
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.attachment-resume-page {
    min-height: 100vh;
    background-color: #f8f8f8;
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;
    background-color: #fff;
}

.navbar-actions {
    display: flex;
    align-items: center;
    padding-right: 15px;
}

/* 内容容器 */
.content-container {
    flex: 1;
    margin-top: 88rpx;
    position: relative;
}

/* 加载状态 */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 60vh;
    gap: 20px;
}

.loading-text {
    font-size: 14px;
    color: #666;
}

/* PDF预览容器 */
.pdf-preview-container {
    height: calc(100vh - 88rpx);
    width: 100%;
    position: relative;
}

.pdf-iframe,
.pdf-webview {
    width: 100%;
    height: 100%;
    border: none;
}

/* 预览失败备用方案 */
.preview-fallback {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60vh;
    padding: 40px 20px;
}

.fallback-content {
    text-align: center;
    max-width: 300px;
}

.fallback-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin: 20px 0 10px;
    display: block;
    word-break: break-all;
}

.fallback-desc {
    font-size: 14px;
    color: #999;
    margin-bottom: 30px;
    display: block;
}

.fallback-actions {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 500;
    border: none;
    transition: all 0.3s ease;
}

.action-btn.primary {
    background-color: #14B19E;
    color: #fff;
}

.action-btn.secondary {
    background-color: #fff;
    color: #14B19E;
    border: 1px solid #14B19E;
}

/* 更多操作弹窗 */
.more-actions-popup {
    padding: 20px 0;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.popup-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.action-list {
    padding-top: 10px;
}

.action-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    transition: background-color 0.3s ease;
}

.action-item:active {
    background-color: #f8f9fa;
}

.action-text {
    font-size: 16px;
    color: #333;
    margin-left: 15px;
}
</style>
