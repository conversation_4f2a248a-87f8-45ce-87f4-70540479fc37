{
	"easycom": {
		"autoscan": true,
		"custom": {
			"^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue",
			"^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
		}
	},
	"pages": [
		{
			"path": "pages/login/login",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/loginres",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/login/respassword",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/choseCity",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/mapnear",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/industry",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/skill",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/positionhope",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/addzhiwei",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/addinfo",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/jobinfo",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/index/search",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/user/user",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/user/userinfo",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/community/community",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/community/communityInfo",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/community/searchCommunity",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/community/publish",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/community/publishsucceed",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/community/mypage",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/community/myinfo",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/community/myname",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/community/homepage",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/earnMoney/earnMoney",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/earnMoney/withdraw",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/earnMoney/balanceDetail",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/earnMoney/addalipay",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/earnMoney/withdrawMoney",
			"style": {
				"navigationStyle": "custom"
			}
		},{
			"path": "pages/earnMoney/addbank",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/message/message",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/chat/chat",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/authentication/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/authentication/positioning",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/authentication/adress",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/audits/index",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/setting/index",
			"style": {
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/user/usercontont/InterviewList",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/usercontont/InterviewDetail",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/realName",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/rider/authentication",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/rider/screenshots",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/rider/workcity",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/workInfo",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/rider/recommend",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/rider/recommendList",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/other/invoicing",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/other/feedback",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/other/collection",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/other/integral",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/other/application",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/setting/security",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/userinfo/index", 
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/userinfo/emitPhone",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/userinfo/emitWatch",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/userinfo/name",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/usercontont/jobApplyCount",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/usercontont/collectCount",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/usercontont/anAuditionCount",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/usercontont/TraceList",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/setting/mobilePhone",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/setting/emitPassword",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/rider/cationNull",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/rider/recommendPerson",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/rider/appeal",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/rider/cationsucces",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/rider/cationreso",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/resume/chmentResume",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/resume/vaResume",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/resume/editPersonalInfo",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/resume/personAdvantage",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/resume/experience",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/resume/projectExperience",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/resume/education",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/resume/skillAdvantage",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},{
			"path": "pages/user/resume/certificate",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/other/feedHistory",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		},
		{
			"path": "pages/user/resume/onlineResume",
			"style": {
				"navigationStyle": "custom",
				"enablePullDownRefresh": false
			}
		}
	],
	"tabBar": {
		"color": "#969ca1",
		"selectedColor": "#15CD7B",
		"borderStyle": "black",
		"list": [
			{
				"pagePath": "pages/index/index",
				"text": "首页",
				"iconPath": "static/tabbar/home.png",
				"selectedIconPath": "static/tabbar/home-active.png"
			},
			{
				"pagePath": "pages/community/community",
				"text": "社区",
				"iconPath": "static/tabbar/square.png",
				"selectedIconPath": "static/tabbar/square-active.png"
			},
			{
				"pagePath": "pages/earnMoney/earnMoney",
				"text": "赚现金",
				"iconPath": "static/tabbar/message.png",
				"selectedIconPath": "static/tabbar/message-active.png"
			},
			{
				"pagePath": "pages/message/message",
				"text": "消息",
				"iconPath": "static/tabbar/message.png",
				"selectedIconPath": "static/tabbar/message-active.png"
			},
			{
				"pagePath": "pages/user/user",
				"text": "我的",
				"iconPath": "static/tabbar/mine.png",
				"selectedIconPath": "static/tabbar/mine-active.png"
			}
		]
	},
	"subPackages": [
		{
			"root": "uni_modules/uni-feedback",
			"pages": [
				{
					"path": "pages/opendb-feedback/opendb-feedback",
					"style": {
						"navigationBarTitleText": "意见反馈",
						"enablePullDownRefresh": false
					}
				}
			]
		},
		{
			"root": "uni_modules/uni-id-pages/pages",
			"pages": [
				{
					"path": "userinfo/userinfo",
					"style": {
						"navigationBarTitleText": "个人资料"
					}
				},
				{
					"path": "userinfo/realname-verify/realname-verify",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "实名认证"
					}
				},
				{
					"path": "login/login-withoutpwd"
				},
				{
					"path": "login/login-withpwd"
				},
				{
					"path": "userinfo/deactivate/deactivate",
					"style": {
						"navigationBarTitleText": "注销账号"
					}
				},
				{
					"path": "userinfo/bind-mobile/bind-mobile",
					"style": {
						"navigationBarTitleText": "绑定手机号码"
					}
				},
				{
					"path": "login/login-smscode",
					"style": {
						"navigationBarTitleText": "手机验证码登录"
					}
				},
				{
					"path": "register/register",
					"style": {
						"navigationBarTitleText": "注册"
					}
				},
				{
					"path": "retrieve/retrieve",
					"style": {
						"navigationBarTitleText": "重置密码"
					}
				},
				{
					"path": "common/webview/webview",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": ""
					}
				},
				{
					"path": "userinfo/change_pwd/change_pwd",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "修改密码"
					}
				},
				{
					"path": "register/register-by-email",
					"style": {
						"navigationBarTitleText": "邮箱验证码注册"
					}
				},
				{
					"path": "retrieve/retrieve-by-email",
					"style": {
						"navigationBarTitleText": "通过邮箱重置密码"
					}
				},
				{
					"path": "userinfo/set-pwd/set-pwd",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "设置密码"
					}
				},
				{
					"path": "userinfo/cropImage/cropImage"
				},
				{
					"path": "register/register-admin",
					"style": {
						"enablePullDownRefresh": false,
						"navigationBarTitleText": "注册管理员账号"
					}
				}
			]
		}
	],
	"globalStyle": {
		// #ifdef H5
		"h5": {
			"titleNView": false
		},
		// #endif
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "uni-starter",
		"navigationBarBackgroundColor": "#FFFFFF",
		"backgroundColor": "#F8F8F8",
		"enablePullDownRefresh": false,
		// "maxWidth":375,
		"rpxCalcMaxDeviceWidth": 375,
		"rpxCalcBaseDeviceWidth": 375
		// "rpxCalcIncludeWidth":0
	},
	"condition": {
		"list": [],
		"current": 1
	},
	"uniIdRouter": {
		"loginPage": "uni_modules/uni-id-pages/pages/login/login-withoutpwd",
		"needLogin": [
			"/uni_modules/uni-id-pages/pages/userinfo/userinfo"
		],
		"resToLogin": true
	}
}