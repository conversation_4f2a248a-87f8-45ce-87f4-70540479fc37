<template>
  <view class="container">
    <u-navbar :autoBack="true" title="被推荐人信息" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <view class="form-container">
      <!-- <view class="form-title">被推荐人信息</view> -->
      
      <view class="form-item">
        <text class="label">姓名</text>
        <input 
          class="input" 
          v-model="form.name" 
          placeholder="请输入"
          placeholder-class="placeholder"
        />
      </view>
      
      <view class="form-item">
        <text class="label">身份证号</text>
        <input 
          class="input" 
          v-model="form.idCard" 
          placeholder="请输入"
          placeholder-class="placeholder"
          type="idcard"
          @blur="validateIdCard"
        />
        <text class="error-msg" v-if="errors.idCard">{{ errors.idCard }}</text>
      </view>
      
      <view class="form-item">
        <text class="label">手机号</text>
        <input 
          class="input" 
          v-model="form.phone" 
          placeholder="请输入"
          placeholder-class="placeholder"
          type="number"
          @blur="validatePhone"
        />
        <text class="error-msg" v-if="errors.phone">{{ errors.phone }}</text>
      </view>
      
      <view class="form-item">
        <text class="label">住址</text>
        <input 
          class="input" 
          v-model="form.address" 
          placeholder="请输入"
          placeholder-class="placeholder"
        />
      </view>
      
      <view class="help-text">遇到了问题？联系客服</view>
      
      <button 
        class="submit-btn" 
        type="primary" 
        @click="handleSubmit"
        :disabled="isSubmitting"
      >{{ isSubmitting ? '提交中...' : '提交' }}</button>
    </view>
  </view>
</template>

<script>
import { jobApi } from '@/utils/api.js'
import { onLoad } from 'uview-ui/libs/mixin/mixin';
export default {
  data() {
    return {
        id: '',
      form: {
        name: '',
        idCard: '',
        phone: '',
        address: ''
      },
      errors: {
        idCard: '',
        phone: ''
      },
      isSubmitting: false
    }
  },
  onLoad(options) {
    this.id=options.id;
  },
  methods: {
    // 验证身份证号
    validateIdCard() {
      // 身份证正则表达式（支持15位和18位）
      const idCardReg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
      
    //   if (!this.form.idCard) {
    //     this.errors.idCard = '请输入身份证号';
    //     return false;
    //   } else
       if (this.form.idCard&&!idCardReg.test(this.form.idCard)) {
        this.errors.idCard = '请输入有效的身份证号';
        return false;
      } else {
        this.errors.idCard = '';
        return true;
      }
    },
    
    // 验证手机号
    validatePhone() {
      // 手机号正则表达式（支持11位数字，1开头）
      const phoneReg = /^1[3-9]\d{9}$/;
      
      if (!this.form.phone) {
        this.errors.phone = '请输入手机号';
        return false;
      } else if (!phoneReg.test(this.form.phone)) {
        this.errors.phone = '请输入有效的手机号';
        return false;
      } else {
        this.errors.phone = '';
        return true;
      }
    },
    
    // 提交表单
    handleSubmit() {
      // 验证所有字段
      const isNameValid = !!this.form.name;
      const isIdCardValid = this.form.idCard?this.validateIdCard():true;
      const isPhoneValid = this.validatePhone();
      const isAddressValid = !!this.form.address;
      
      if (!isNameValid) {
        uni.showToast({ title: '请输入姓名', icon: 'none' });
        return;
      }
      if (!isIdCardValid) {
        return;
      }
      if (!isPhoneValid) {
        return;
      }
    //   if (!isAddressValid) {
    //     uni.showToast({ title: '请输入住址', icon: 'none' });
    //     return;
    //   }
      
      // 提交逻辑
      this.isSubmitting = true;
      const params = { 
        job_id:this.id,
        recommended_name: this.form.name,
        recommended_phone: this.form.phone,
        recommended_address:this.form.address,
        recommended_idcard: this.form.idCard,
      };
      jobApi.addRecommendJobPerson(params).then(res => { 
        uni.hideLoading();
        console.log(res);
        if(res.code == 200){
            uni.showToast({
                title: res.msg,
                icon: 'none',
                duration: 500,
            });
            uni.navigateBack();
        }
        
    })

    },
    
    
  }
}
</script>

<style lang="scss">
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.form-container {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  color: #333;
}

.form-item {
  display: flex;
  flex-direction: column;
  padding: 25rpx 0;
  border-bottom: 1rpx solid #eee;
  position: relative;
}

.label {
  width: 180rpx;
  font-size: 30rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.input {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  height: 60rpx;
  line-height: 60rpx;
}

.placeholder {
  color: #ccc;
  font-size: 30rpx;
}

.error-msg {
  color: #ff0000;
  font-size: 24rpx;
  margin-top: 10rpx;
}

.help-text {
  margin-top: 40rpx;
  text-align: center;
  font-size: 26rpx;
  color: #999;
}

.submit-btn {
  margin-top: 50rpx;
  background-color: #007aff;
  color: #fff;
  border-radius: 50rpx;
  font-size: 32rpx;
  height: 90rpx;
  line-height: 90rpx;
  
  &[disabled] {
    background-color: #cccccc;
  }
}
</style>