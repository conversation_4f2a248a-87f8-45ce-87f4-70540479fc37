<template>
  <view class="container">
    <!-- 搜索框 -->
    <view class="search-box">
        <view><u-icon name="arrow-left" color="#606266" size="28" @click="goBack"></u-icon></view>
      
      <u-search
        v-model="keyword"
        placeholder="请输入职位/公司名称"
        :show-action="true"
        action-text="搜索"
        @search="doSearch"
        @custom="doSearch"
      ></u-search>
    </view>
    <view class="recommend-list">
        <scroll-view class="list-container" scroll-y="true" refresher-enabled="true"
                    :refresher-triggered="refreshing" @refresherrefresh="onRefresh" @scrolltolower="onLoadMore"
                    :lower-threshold="50" >

                <view  v-for="(item, index) in recommendList" 
            :key="index" class="item-list">
            <view  class="cell-title" @click="gohomepage(item)">
                <view style="display: flex;">
                    <u-avatar class="avatar" :src="item.avatarUrl"></u-avatar>
                    <text class="username"> {{ item.username }} </text>
                </view>
                <view>
                <u-button color="#059f9f" size="mini" @click="follow(item)" shape="circle" :hairline="true" v-if="item.is_gz==0" >+ 关注</u-button>
                <u-button color="#ddd" size="mini" shape="circle" :hairline="false" v-else >已关注</u-button>
                </view>
            </view>
            
                <view @click="seeAll(item)">
                    <!-- 内容部分 -->
                    <view 
                    class="content"
                    :class="{'content-collapsed': !item.expanded && item.showExpand}"
                    
                    >
                    <text>{{ item.content }}</text>
                            <text v-if="item.showExpand">...</text>
                    </view>
                    <view 
                    class="expand-qw" 
                    v-if="item.showExpand"
                    >
                    <text>全文</text> 
                    </view>
                </view>
            </view>
        </scroll-view>
    </view>
    
  </view>
</template>
<script>

import {communityApi } from '@/utils/api'
export default {

  data() {
    return {
      keyword: '', // 搜索关键词
      refreshing:false,
      recommendList:[],
      recommendpage: 1,
      recommendtotle: 0,
    }
  },
  methods:{
    
    //返回
    goBack() {
      uni.navigateBack({
        delta: 1 // 返回的页面数，这里设置为1表示返回上一页
      });
    },
    // 执行搜索
    doSearch() {
        console.log('执行搜索')
        this.getData();
        // this.$emit('search', this.keyword)
    //   this.loadData()
    },
    		//推荐列表
	async getData(){
		await communityApi.getCommunityList({name:this.keyword,page:this.recommendpage}).then(res => {
			uni.hideLoading();
			res.data.data.map(item => {
				item['expanded']=false;
				item['showExpand']= this.checkNeedExpand(item.content) // 是否需要显示展开按钮
				if(item.content.length > 75){
					item['content'] = item.content.substring(0, 67)
				}
				
			})
      this.recommendtotle=res.data.pageTotal;
			this.recommendList = [...this.recommendList,...res.data.data];
			console.log('和技术',this.recommendList)
			// this.$nextTick(() => {
			// 		this.$refs.uReadMore.init();
			// 	})
		})

	},
    // 检查是否需要显示展开按钮
    checkNeedExpand(content) {
      // 这里可以根据实际需求实现更精确的判断
      // 例如根据内容长度或行数

	  console.log('content',content.length);
      return content.length > 50; // 简单根据长度判断
    },
    	//查看全部
	seeAll(item){
        uni.navigateTo({
            url: '/pages/community/communityInfo?id=' + item.id
        });
	},
    // 跳转个人主页
    gohomepage(item){
      uni.navigateTo({
        url: '/pages/community/homepage?id=' + item.user_id
      })
    },
    //关注
    async follow(item){
      await communityApi.mark({source_id:item.user_id,type:2}).then(res => {
            uni.hideLoading();
            console.log(res);
      })
    },
    //下拉刷新
	onRefresh(){
		
      this.recommendpage=1;
			this.getData()
		
	},
    //上拉加载
	onLoadMore(){
		
      if(this.recommendtotle>this.recommendpage){
        this.recommendpage++;
			  this.getData()
      }
	
	},
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.search-box {
    display: flex;
    align-items: center;
  background-color: #fff;
  padding: 20rpx 6rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

::v-deep .u-tabs__wrapper__nav__line{
	display: none;
}
::v-deep .u-tabs__wrapper__nav{
	justify-content: center;
}
.content {
    font-size: 28rpx;
    color: #333;
    line-height: 1.6;
    word-break: break-word;
    
    &-collapsed {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3; /* 显示的行数 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
  .expand-qw{
	color: #14b19e;
    font-size: 26rpx;
	position: absolute;
	right: 94rpx;
	bottom: 26rpx;
  }
.expand-btn {
    display: flex;
    align-items: center;
	  justify-content: end;
    margin-top: 10rpx;
    
    
    text {
      margin-right: 8rpx;
    }
  }
  
  .action-item {
    display: flex;
    align-items: center;
    color: #666;
    font-size: 26rpx;
    
    text {
      margin-left: 8rpx;
    }
  }


  
  .list-container{
    height: calc(100vh - 220rpx);
  }

	.item-list{
		border: 1px solid #eee;
		margin-bottom: 20rpx;
		padding: 20rpx;
		border-radius: 20rpx;
		position: relative;
	}

  .cell-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 20rpx;
    margin-bottom: 20rpx;
	.avatar{
		margin-right: 16rpx;
	}
    .username {
      font-size: 32rpx;
      font-weight: bold;
      margin-right: 10rpx;
    }

    .company {
      font-size: 28rpx;
      color: #666;
    }
  }

  .cell-content {
    font-size: 28rpx;
    color: #333;
    margin-top: 10rpx;
  }

  .cell-footer {
    display: flex;
    align-items: center;
    margin-top: 10rpx;

    u-button {
      margin-right: 10rpx;
    }

    u-icon {
      margin-right: 10rpx;
    }
  }
  .pages-title{
    height: 50rpx;
    display: flex;
    padding-left: 8px;
    align-items: center;
  }


</style>
