<template>
    <view class="collection-page">
        <!-- 顶部导航栏 -->
        <view class="navbar">
            <u-navbar height="44px" title="我的收藏" @rightClick="handleBack" :autoBack="true" :leftIconSize="30"
                :leftIconColor="'#333'" safeAreaInsetTop placeholder fixed>
            </u-navbar>
        </view>

        <!-- Tab切换 -->
        <view class="tab-container">
            <view class="tab-wrapper">
                <view class="tab-item" :class="{ 'active': currentTab === 'job' }" @click="switchTab('job')">
                    <text class="tab-text">职位</text>
                </view>
                <view class="tab-item" :class="{ 'active': currentTab === 'company' }" @click="switchTab('company')">
                    <text class="tab-text">公司</text>
                </view>
            </view>
        </view>

        <!-- 职位收藏页面 -->
        <view v-if="currentTab === 'job'" class="job-content">
            <scroll-view class="job-scroll-view" :style="{ height: scrollViewHeight + 'px' }" scroll-y="true"
                refresher-enabled="true" :refresher-triggered="jobRefreshing" @refresherrefresh="onJobRefresh"
                @scrolltolower="onJobLoadMore" lower-threshold="50">

                <view class="job-list">
                    <view v-for="(item, index) in jobList" :key="item.id" class="job-item" @click="goDetail(item)">
                        <view class="job-header">
                            <view class="job-info">
                                <text class="job-title">{{ item.name }}</text>
                                <text class="salary">{{ item.min_salary }}-{{ item.max_salary }}</text>
                            </view>
                            <view class="apply-btn" @click.stop="applyJob(item)">
                                <text class="apply-text">打招呼</text>
                            </view>
                        </view>

                        <view class="job-details">
                            <text class="company-info">{{ item.company_name }}</text>
                            <text class="location">{{ item.address }}</text>
                        </view>

                        <view class="job-tags">
                            <text v-for="tag in item.lable" :key="tag" class="tag">
                                {{ tag }}
                            </text>
                        </view>

                        <view class="job-footer">
                            <view class="recruiter-info">
                                <image class="avatar" :src="item.avatarUrl" mode="aspectFill"></image>
                                <text class="recruiter-name">{{ item.username }}.{{ item.job_position_name }}</text>
                            </view>
                            <text class="publish-date">{{ item.createtime }}</text>
                        </view>
                    </view>
                </view>

                <!-- 加载状态 -->
                <view class="load-status">
                    <view v-if="jobLoading" class="loading">
                        <text class="loading-text">加载中...</text>
                    </view>
                    <view v-else-if="page >= totalPage && jobList.length == 0" class="no-more">
                        <text class="no-more-text">没有更多数据了</text>
                    </view>
                </view>
            </scroll-view>
        </view>

        <!-- 公司收藏页面 -->
        <view v-if="currentTab === 'company'" class="company-content">
            <scroll-view class="company-scroll-view" :style="{ height: scrollViewHeight + 'px' }" scroll-y="true"
                refresher-enabled="true" :refresher-triggered="companyRefreshing" @refresherrefresh="onCompanyRefresh"
                @scrolltolower="onCompanyLoadMore" lower-threshold="50">

                <view class="company-list">
                    <view v-for="(item, index) in companyList" :key="item.id" class="company-item">
                        <view class="company-header">
                            <view class="company-logo">
                                <!-- <image class="logo-img" :src="item.logo" mode="aspectFill"></image> -->
                            </view>
                            <view class="company-info">
                                <text class="company-name">{{ item.company_name }}</text>
                                <!-- <text class="company-desc">{{ item.description }}</text> -->
                                <text class="company-location">{{ item.location }}</text>
                            </view>
                            <text class="collect-date">{{ item.created_at }}</text>
                        </view>
                    </view>
                </view>

                <!-- 加载状态 -->
                <view class="load-status">
                    <view v-if="companyLoading" class="loading">
                        <text class="loading-text">加载中...</text>
                    </view>
                    <view v-else-if="pageLate >= totalPageLate && companyList.length == 0" class="no-more">
                        <text class="no-more-text">没有更多数据了</text>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</template>

<script>
import { collect } from "@/utils/api"
export default {
    data() {
        return {
            currentTab: '', // 当前选中的tab，默认选中职位
            jobList: [], // 职位收藏列表
            companyList: [], // 公司收藏列表
            page: 1, // 职位页码
            pageLate: 1, // 公司页码
            totalPage: 0, // 职位总页数
            totalPageLate: 0, // 公司总页数
            jobLoading: false, // 职位加载状态
            companyLoading: false, // 公司加载状态
            jobRefreshing: false, // 职位下拉刷新状态
            companyRefreshing: false, // 公司下拉刷新状态
            scrollViewHeight: 0 // 滚动视图高度
        }
    },
    onLoad() {
        // this.loadCollectionData()
        this.switchTab('job')
        this.calculateScrollViewHeight()
    },
    onReady() {
        this.calculateScrollViewHeight()
    },
    methods: {
        goDetail(item) {
            uni.navigateTo({
                url: '/pages/index/jobinfo?id=' + item.id
            })
        },
        // 计算滚动视图高度
        calculateScrollViewHeight() {
            const systemInfo = uni.getSystemInfoSync()
            const windowHeight = systemInfo.windowHeight
            // 导航栏高度44px + tab切换区域高度约110rpx(转换为px约55px) + 内边距
            const navHeight = 44
            const tabHeight = 55
            const padding = 30
            this.scrollViewHeight = windowHeight - navHeight - tabHeight - padding
        },

        // 返回上一页
        goBack() {
            uni.navigateBack()
        },

        // 切换tab
        async switchTab(tab) {
            uni.showLoading({
                title: '加载中',
                mask: true
            });
            this.currentTab = tab
            if (this.currentTab === 'job') {
                // 重置职位数据
                this.page = 1
                this.jobList = []
                await this.loadJobList(true)
            } else {
                // 重置公司数据
                this.pageLate = 1
                this.companyList = []
                await this.loadCompanyList(true)
            }
        },

        // 加载职位列表
        async loadJobList(isRefresh = false) {
            if (this.jobLoading) return

            this.jobLoading = true

            try {
                const res = await collect.collectList({ page: this.page })
                console.log('职位列表:', res)

                if (res.code === 200) {
                    this.totalPage = res.data.totlePage

                    if (isRefresh) {
                        this.jobList = res.data.list
                    } else {
                        this.jobList = [...this.jobList, ...res.data.list]
                    }
                    uni.hideLoading()
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                    uni.hideLoading()
                }
            } catch (error) {
                console.error('加载职位数据失败:', error)
                uni.showToast({
                    title: '加载失败',
                    icon: 'none'
                })
                uni.hideLoading()
            } finally {
                this.jobLoading = false
            }
        },

        // 加载公司列表
        async loadCompanyList(isRefresh = false) {
            if (this.companyLoading) return

            this.companyLoading = true

            try {
                const res = await collect.collectCompanyList({ page: this.pageLate })
                console.log('公司列表:', res)

                if (res.code === 200) {
                    this.totalPageLate = res.data.totlePage

                    if (isRefresh) {
                        this.companyList = res.data.list
                    } else {
                        this.companyList = [...this.companyList, ...res.data.list]
                    }
                    uni.hideLoading()
                } else {
                    uni.showToast({
                        title: res.msg,
                        icon: 'none'
                    })
                    uni.hideLoading()
                }
            } catch (error) {
                console.error('加载公司数据失败:', error)
                uni.showToast({
                    title: '加载失败',
                    icon: 'none'
                })
            } finally {
                this.companyLoading = false
            }
        },

        // 加载收藏数据
        async loadCollectionData() {
            try {
                // TODO: 替换为实际的API接口
                // const jobResponse = await this.$api.getJobCollections()
                // const companyResponse = await this.$api.getCompanyCollections()
                // this.jobList = jobResponse.data
                // this.companyList = companyResponse.data
                try {
                    const res = await collect.collectList()
                    console.log(res)
                    if (res.code == 200) {
                        this.jobList = res.data.list
                        this.companyList = this.getCompanyMockData()
                    }
                    // 模拟数据

                } catch (error) {
                    console.error('加载收藏数据失败:', error)
                }

            } catch (error) {
                console.error('加载收藏数据失败:', error)
                uni.showToast({
                    title: '加载数据失败',
                    icon: 'none'
                })
            }

            try {

            } catch (error) {
                console.error('加载收藏数据失败:', error)
            }
        },

        // 职位下拉刷新
        async onJobRefresh() {
            this.jobRefreshing = true
            this.page = 1
            this.jobList = []
            try {
                await this.loadJobList(true)
            } finally {
                this.jobRefreshing = false
            }
        },

        // 职位上拉加载
        async onJobLoadMore() {
            if (this.page >= this.totalPage || this.jobLoading) {
                return
            }

            this.page++
            await this.loadJobList(false)
        },

        // 公司下拉刷新
        async onCompanyRefresh() {
            this.companyRefreshing = true
            this.pageLate = 1
            this.companyList = []
            try {
                await this.loadCompanyList(true)
            } finally {
                this.companyRefreshing = false
            }
        },

        // 公司上拉加载
        async onCompanyLoadMore() {
            if (this.pageLate >= this.totalPageLate || this.companyLoading) {
                return
            }

            this.pageLate++
            await this.loadCompanyList(false)
        },

        // 申请职位/打招呼
        applyJob(item) {
            console.log('打招呼:', item)
            item.job_id = item.id
            console.log("item", item)
            const userInfo = encodeURIComponent(JSON.stringify(item))
            uni.navigateTo({
                url: `/pages/chat/chat?userInfo=${userInfo}`
            })
        },

        // 职位模拟数据
        getJobMockData() {
            return [
                {
                    id: 1,
                    title: '销售',
                    salary: '100-300/时',
                    company: 'xx市房租科技 30-100',
                    location: 'xx区 xx广场',
                    tags: ['1-3', '大专', '宣传物料'],
                    recruiter: {
                        name: '张女士-人事主管',
                        avatar: '/static/avatar1.png'
                    },
                    publishDate: '04月02日'
                }
            ]
        },

        // 公司模拟数据
        getCompanyMockData() {
            return [
                {
                    id: 1,
                    name: '邯郸xxxx公司',
                    description: 'xx市房租科技 30-100',
                    location: 'xx区 xx广场',
                    logo: '/static/company-logo1.png',
                    collectDate: '04月02日'
                },
                {
                    id: 2,
                    name: '邯郸xxxx公司',
                    description: 'xx市房租科技 30-100',
                    location: 'xx区 xx广场',
                    logo: '/static/company-logo2.png',
                    collectDate: '04月02日'
                }
            ]
        }
    }
}
</script>

<style lang="scss" scoped>
.collection-page {
    min-height: 100vh;
    background: linear-gradient(180deg, #E8F8F5 0%, #F8F8F8 100%);
    display: flex;
    flex-direction: column;
}

/* 顶部导航栏 */


/* Tab切换 */
.tab-container {
    padding: 30rpx;
}

.tab-wrapper {
    display: flex;
    background-color: transparent;
    gap: 40rpx;
}

.tab-item {
    position: relative;
    padding: 20rpx 0;

    .tab-text {
        font-size: 28rpx;
        color: #666;
        font-weight: 500;
    }

    &.active {
        .tab-text {
            color: #333;
            font-weight: 600;
        }

        &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 40rpx;
            height: 4rpx;
            background-color: #14B19E;
            border-radius: 2rpx;
        }
    }
}

/* 职位内容 */
.job-content {
    flex: 1;
    padding: 0 30rpx;
}

.job-scroll-view {
    width: 100%;
}

.job-list {
    padding-bottom: 40rpx;
}

.job-item {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.job-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20rpx;
}

.job-info {
    flex: 1;

    .job-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #333;
        margin-bottom: 8rpx;
        display: block;
    }

    .salary {
        font-size: 28rpx;
        font-weight: 500;
        color: #14B19E;
    }
}

.apply-btn {
    background-color: #14B19E;
    border-radius: 30rpx;
    padding: 16rpx 24rpx;

    .apply-text {
        font-size: 24rpx;
        color: #fff;
        font-weight: 500;
    }
}

.job-details {
    display: flex;
    gap: 20rpx;
    margin-bottom: 16rpx;

    .company-info,
    .location {
        font-size: 26rpx;
        color: #666;
    }
}

.job-tags {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 20rpx;

    .tag {
        background-color: #f0f8ff;
        color: #007AFF;
        font-size: 22rpx;
        padding: 8rpx 16rpx;
        border-radius: 20rpx;
        border: 1rpx solid #e6f3ff;
    }
}

.job-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1rpx solid #f0f0f0;
    padding-top: 20rpx;
}

.recruiter-info {
    display: flex;
    align-items: center;
    gap: 16rpx;

    .avatar {
        width: 60rpx;
        height: 60rpx;
        border-radius: 50%;
        background-color: #f0f0f0;
    }

    .recruiter-name {
        font-size: 26rpx;
        color: #333;
        font-weight: 500;
    }
}

.publish-date {
    font-size: 24rpx;
    color: #999;
}

/* 公司内容 */
.company-content {
    flex: 1;
    padding: 0 30rpx;
}

.company-scroll-view {
    width: 100%;
}

.company-list {
    padding-bottom: 40rpx;
}

.company-item {
    background-color: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

.company-header {
    display: flex;
    align-items: flex-start;
    gap: 20rpx;
}

.company-logo {
    width: 100rpx;
    height: 100rpx;
    border-radius: 12rpx;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    .logo-img {
        width: 80rpx;
        height: 80rpx;
        border-radius: 8rpx;
    }
}

.company-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8rpx;

    .company-name {
        font-size: 28rpx;
        font-weight: 600;
        color: #333;
    }

    .company-desc {
        font-size: 24rpx;
        color: #666;
    }

    .company-location {
        font-size: 24rpx;
        color: #999;
    }
}

.collect-date {
    font-size: 24rpx;
    color: #999;
    flex-shrink: 0;
}

/* 加载状态 */
.load-status {
    padding: 30rpx 0;
    text-align: center;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16rpx;
}

.loading-text {
    font-size: 26rpx;
    color: #999;
}

.no-more {
    display: flex;
    align-items: center;
    justify-content: center;
}

.no-more-text {
    font-size: 26rpx;
    color: #ccc;
}

/* 全局页面样式 */
page {
    background-color: #f8f8f8;
}
</style>
