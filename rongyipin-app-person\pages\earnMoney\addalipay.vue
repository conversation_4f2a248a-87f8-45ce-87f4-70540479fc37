<template>
  <view class="container">
    <!-- 头部导航栏 -->
     <u-navbar :autoBack="true" title="添加银行卡" :is-fixed="true" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>
    <view class="form-container">
      <view class="form-title">添加银行卡</view>
      
      <view class="form-item">
        <text class="label">姓 名</text>
        <u-input 
          v-model="form.cardholder" 
          placeholder="请输入您的姓名"
          placeholder-class="placeholder"
          :border="true"
          clearable
        />
      </view>
      
      <view class="form-item">
        <text class="label">账 号</text>
        <u-input 
          v-model="form.cardNumber" 
          placeholder="请输入您的账号"
          placeholder-class="placeholder"
          :border="true"
          type="number"
          clearable
          @input="formatCardNumber"
        />
      </view>
      
    
      
      
      <u-button 
        type="primary" 
        shape="circle" 
        @click="handleSubmit"
        :disabled="!canSubmit"
        class="submit-btn"
      >
        确定
      </u-button>
    </view>
    
   
  </view>
</template>

<script>
import {rider} from '@/utils/api.js'
export default {
  data() {
    return {
      form: {
        cardholder: '',
        cardNumber: '',
        bankName: ''
      },
      showBankPicker: false,
      
    }
  },
  computed: {
    canSubmit() {
      return this.form.cardholder && 
             this.form.cardNumber 
    }
  },
  methods: {
    // 格式化银行卡号 (每4位加空格)
    // formatCardNumber() {
    //   this.form.cardNumber = this.form.cardNumber.replace(/\s/g, '')
    //     .replace(/(\d{4})(?=\d)/g, '$1 ');
    // },
    
    
    // 提交表单
    handleSubmit() {
      if (!this.canSubmit) return;
      
      uni.showLoading({ title: '绑定中...' });
      
      // 模拟API请求
    //   setTimeout(() => {
    //     uni.hideLoading();
     
        
       
    //   }, 1500);
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 30rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.form-container {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 40rpx;
  color: #333;
}

.form-item {
  margin-bottom: 40rpx;
  position: relative;
  display: flex;
  align-items: center;
  .label {
    display: block;
    font-size: 30rpx;
    color: #666;
    // margin-bottom: 20rpx;
  }
  
  ::v-deep .u-input {
    padding-right: 60rpx !important;
  }
}

.bank-select-icon {
  position: absolute;
  right: 20rpx;
  bottom: 20rpx;
}

.hint-text {
  font-size: 26rpx;
  color: #999;
  margin: 40rpx 0;
  line-height: 1.6;
}

.submit-btn {
  margin-top: 30rpx;
  height: 90rpx;
  font-size: 34rpx;
}

.placeholder {
  color: #ccc;
  font-size: 30rpx;
}
</style>