<template>

  <view class="userinfo-container">
    <!-- 标题 -->
    <view class="title">填写个人信息</view>
    <view class="subtitle">将根据你的选择，为你优化推荐职位</view>

    <!-- 姓名 -->
    <view class="form-item">
      <text class="label"> <text style="color: red;">*</text>姓名</text>
      <u--input v-model="name" placeholder="请输入姓名" border="surround" />
    </view>

    <!-- 性别 -->
    <view class="form-item">
      <text class="label"><text style="color: red;">*</text>性别</text>
      <view class="gender-buttons">
        <u-button size="small" :class="{ active: gender === 1 }" @click="gender = 1" :plain="gender !== 1"
          style="flex: 1; margin-right: 10px;">
          男
        </u-button>
        <u-button size="small" :class="{ active: gender === 2 }" @click="gender = 2" :plain="gender !== 2" style="flex: 1;">
          女
        </u-button>
      </view>
    </view>

    <!-- 出生日期 -->
    <view class="form-item">
      <text class="label"><text style="color: red;">*</text>出生日期</text>
      <u--input :value="birthDate" @focus="show = true" border="surround"></u--input>
      <u-datetime-picker class="datetime-picker" :show="show" mode="date" :value="currentDateTimestamp"
        :minDate="minDate" :maxDate="maxDate" @confirm="confirm" @cancel="show = false"
        itemHeight="70"></u-datetime-picker>

    </view>

    <!-- >第一次工作（年月） -->
    <view class="form-item">
      <text class="label"><text style="color: red;">*</text>第一次工作（年月）</text>
      <u--input :value="first_word_time" placeholder="请输入姓名" @focus="first_word_show = true" border="surround" />
      <u-datetime-picker class="datetime-picker" :show="first_word_show" mode="year-month" :value="currentDateTimestamp"
        :minDate="minDate" :maxDate="maxDate" @confirm="firstWordConfirm" @cancel="first_word_show = false"
        itemHeight="70" @close="first_word_show = false"></u-datetime-picker>

    </view>

    <!-- 学历 -->
    <view class="form-item">
      <text class="label"><text style="color: red;">*</text>学历</text>
      <view class="education-buttons">
        <u-button v-for="(edu, index) in educations" size="small" :key="index" :class="{ active: selectedEducation === edu.id }"
          @click="selectedEducation = edu.id" :plain="selectedEducation !== edu.id"
          style="margin-right: 10px; margin-bottom: 10px; flex: 1 1 calc(30% - 10px);">
          {{ edu.name }}
        </u-button>
      </view>
    </view>
    <!-- 学校 -->
    <view class="form-item">
      <text class="label"><text style="color: red;">*</text>学校</text>
      <u--input v-model="schoolName" placeholder="请输入学校关键字" border="surround" @confirm="onSearchSchool" @blur="onSearchSchool" @change="change"/>
      <!-- <u-search placeholder="输入学校关键字" v-model="keyword" actionText="" @search="search"></u-search> -->
      <view class="school-list" v-if="schoolflag">
        <view class="school-item" v-for="(item, index) in schoolList" :key="index" @click="selectSchool(item)">{{ item.name }}</view>
      </view>
      
    </view>
    <!-- 优势 -->
    <view class="form-item">
      <text class="label"> <text style="color: red;">*</text>优势</text>
      <u--textarea v-model="advantage" placeholder="请输入您的优势...   如:踏实认真...." count maxlength="500" height="200"></u--textarea>
      <!-- <u--input v-model="advantage" placeholder="请输入姓名" border="surround" /> -->
    </view>

    <!-- 下一步按钮 -->
    <u-button type="primary" color="#4ecdc4" @click="nextStep" style="margin-top: 30px;">
      下一步
    </u-button>
  </view>

</template>
<script>
import { dictApi, userApi, userinfoApi } from '@/utils/api.js'
export default {
  data() {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    const currentDay = currentDate.getDate();
    // 格式化当前年月为 YYYY-MM 格式
    const currentYearMonth = `${currentYear}-${(currentMonth + 1).toString().padStart(2, '0')}`;
    const currentdate = `${currentYear}-${(currentMonth + 1).toString().padStart(2, '0')}-${currentDay}`
    return {
      show: false,
      first_word_show: false,//第一次工作（年月）
      name: '',
      gender: 1,
      birthDate: currentdate, // 默认为当前年月
      //   birthDateTimestamp: new Date('2000-01-01').getTime(),
      first_word_time: currentYearMonth, // 默认为当前年月
      selectedEducation: 1,
      educations: [],
      // 设置时间选择器的最小和最大日期（当前年月的前100年到现在）
      minDate: new Date(currentYear - 100, currentMonth, 1).getTime(), // 100年前的当前月份1日
      maxDate: currentDate.getTime(), // 当前时间
      currentDateTimestamp: currentDate.getTime(), // 当前时间戳，用于时间选择器默认值
      schoolData: {},
      selectSchooldata: {},
      schoolflag:false,
      schoolAll: [],
      schoolList:[],//学校列表
      advantage:'',
    };
  },
  onLoad() {
    this.eduData();
    this.schoolserch();
  },
  methods: {
    change(){
      clearTimeout(this.debounceTimer);
       this.debounceTimer = setTimeout(() => {
        // 1秒内没有新的输入，执行请求接口的代码
        this.onSearchSchool();
    }, 1000);

    },
    selectSchool(item){
      this.schoolName=item.name;
      this.selectSchooldata=item;
      this.schoolflag=false;

    },
    // 获取学校列表
    async schoolserch(){
      await userinfoApi.getschools({
        name: this.schoolName
      }).then(res => {
        uni.hideLoading();
        this.schoolAll=res.data;
        // console.log(res);
        // const list=res.data.filter(item=>item.name.indexOf(this.schoolName)>-1);
        // console.log(list);
        
      })
    },
     onSearchSchool(){
      
      this.schoolList=[];
      if(this.selectedEducation!=1&&this.selectedEducation!=2){
        this.schoolflag=true;
        this.schoolList=this.schoolAll.filter(item=>item.name.indexOf(this.schoolName)>-1);
      } 
    },
    async eduData() {
      await dictApi.getAllDictionary().then(res => {
        uni.hideLoading();
        const resData = res.data.job_edu.data;
        resData.shift();

        this.educations = resData;
        console.log(this.educations);
      }).catch(err => {
        uni.hideLoading();
        console.log(err);
      })
    },
    //出生日期
    confirm(e) {
      this.show = false;
      console.log(this.toYeardate(e.value));
      const date = this.toYeardate(e.value);
      this.birthDate = date;
    },
    //第一次工作
    firstWordConfirm(e) {
      this.first_word_show = false;

      const date = this.toYearMonth(e.value);
      this.first_word_time = date;
    },

    toYearMonth(timestamp) {
      const date = new Date(timestamp);

      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以要加1
      // const day = date.getDate().toString().padStart(2, '0');
      // const hours = date.getHours().toString().padStart(2, '0');
      // const minutes = date.getMinutes().toString().padStart(2, '0');
      // const seconds = date.getSeconds().toString().padStart(2, '0');

      const formattedDate = `${year}-${month}`;
      return formattedDate;
    },
    toYeardate(timestamp) {
      const date = new Date(timestamp);

      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以要加1
      const day = date.getDate().toString().padStart(2, '0');
      // const hours = date.getHours().toString().padStart(2, '0');
      // const minutes = date.getMinutes().toString().padStart(2, '0');
      // const seconds = date.getSeconds().toString().padStart(2, '0');

      const formattedDate = `${year}-${month}-${day}`;
      return formattedDate;
    },
    toDate(timestamp) {
      const date = new Date(timestamp);

      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，所以要加1
      const day = date.getDate().toString().padStart(2, '0');
      // const hours = date.getHours().toString().padStart(2, '0');
      // const minutes = date.getMinutes().toString().padStart(2, '0');
      // const seconds = date.getSeconds().toString().padStart(2, '0');

      const formattedDate = `${year}-${month}-${day}`;
      return formattedDate;// 输出："2021-10-01 00:00:00"
    },

    //下一步
    nextStep() {
      // 验证姓名是否填写
      if (!this.name || this.name.trim() === '') {
        uni.showToast({
          title: '姓名是必填的',
          icon: 'none',
          duration: 2000
        });
        return;
      }
      // if(!this.selectSchool.name || this.selectSchool.name.trim() === ''){
      //    uni.showToast({
      //     title: '是必填的',
      //     icon: 'none',
      //     duration: 2000
      //   });
      //   return;
      // }
      if(!this.advantage || this.advantage.trim() === ''){
         uni.showToast({
          title: '个人优势是必填的',
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 处理下一步逻辑
      console.log('Next step clicked', this.name, this.gender, this.selectedWorkYear, this.selectedEducation);
      if(this.schoolList.length ==1){
        this.selectSchool=this.schoolList[0];
      }
      if(this.selectedEducation==1||this.selectedEducation==2){
        this.selectSchool={
          name:this.schoolName,
          id:0
        };
      }
      const params = {
        username: this.name,
        sex: this.gender,
        birthday: this.birthDate,
        first_word_time: this.first_word_time,
        degree: this.selectedEducation,
        shools_name: this.selectSchool.name,
        shools_id: this.selectSchool.id,
        advantage: this.advantage,
      }
      userApi.userSave(params).then(res => {
        uni.hideLoading();
        if (res.code == 200) {
          userinfoApi.userinfo().then(ress => {
            if (ress.code == 200) {
              console.log(ress, 'qqqqq');
              uni.setStorageSync("userInfo", ress.data);
              uni.showToast({
                title: ress.msg,
                icon: 'none',
              });

              uni.hideLoading();
              uni.navigateTo({
                 url: '/pages/user/workInfo?work=no',
              })
            } else {
              uni.showToast({
                title: ress.msg,
                icon: 'none',
              });
            }
          })


        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none',
          });
        }
      }).catch(err => {
        uni.hideLoading();
        console.log(err);
      })

    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .u-button {
  border-radius: 10px;
  background: #f9f6f6;
}

::v-deep uni-button:after {
  border: none;
}

::v-deep .u-border {
  border: 1rpx solid #eee;
}

.userinfo-container {
  padding: 20px;
}

.title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
}

.subtitle {
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 20px;
}

.label {
  display: block;
  font-size: 16px;
  margin-bottom: 5px;
}

.birth-date {
  border: 1px solid #ddd;
  border-radius: 15px;
  padding: 10px;
}

.input {
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  width: 100%;
}

.gender-buttons {
  display: flex;
}

.gender-buttons {
  flex: 1;
  //   background-color: #f5f5f5;
  border: none;
  padding: 10px;
  margin-right: 10px;
  border-radius: 5px;
}

.gender-buttons .active {
  background-color: #4ecdc4;
  color: white;
}

.gender-buttons:last-child {
  margin-right: 0;
}

/* .birth-date {
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
  width: 100%;
} */

.work-years-buttons,
.education-buttons {
  display: flex;
  flex-wrap: wrap;
}

.work-years-buttons,
.education-buttons {
  //   background-color: #f5f5f5;
  border: none;
  padding: 10px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
  flex: 1 1 calc(50% - 10px);
}

.work-years-buttons .active,
.education-buttons .active {
  background-color: #4ecdc4;
  color: white;
}

.next-step-button {
  background-color: #4ecdc4;
  color: white;
  border: none;
  padding: 15px 0;
  width: 100%;
  border-radius: 5px;
  font-size: 16px;
}
.school-list{
  height: 200rpx;
  overflow-y: auto;
}

::v-deep .u-slide-up-enter-active {
  height: 300px;
}
</style>
