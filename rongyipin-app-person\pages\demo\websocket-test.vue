<template>
	<view class="test-container">
		<view class="header">
			<text class="title">WebSocket连接测试</text>
		</view>

		<view class="status-section">
			<view class="status-item">
				<text class="label">连接状态:</text>
				<text :class="['status', statusClass]">{{ statusText }}</text>
			</view>
			<view class="status-item">
				<text class="label">当前服务器:</text>
				<text class="value">{{ currentUrl }}</text>
			</view>
			<view class="status-item">
				<text class="label">重连次数:</text>
				<text class="value">{{ reconnectAttempts }}</text>
			</view>
		</view>

		<view class="button-section">
			<button class="test-btn connect-btn" @click="testConnect" :disabled="isConnecting">
				{{ isConnecting ? '连接中...' : '测试连接' }}
			</button>
			<button class="test-btn disconnect-btn" @click="testDisconnect" :disabled="!isConnected">
				断开连接
			</button>
			<button class="test-btn send-btn" @click="testSendMessage" :disabled="!isConnected">
				发送测试消息
			</button>
		</view>

		<view class="log-section">
			<view class="log-header">
				<text class="log-title">连接日志</text>
				<button class="clear-btn" @click="clearLogs">清空</button>
			</view>
			<scroll-view class="log-content" scroll-y="true">
				<view class="log-item" v-for="(log, index) in logs" :key="index" :class="log.type">
					<text class="log-time">{{ log.time }}</text>
					<text class="log-message">{{ log.message }}</text>
				</view>
			</scroll-view>
		</view>

		<view class="config-section">
			<text class="config-title">服务器配置</text>
			<view class="config-item" v-for="(config, index) in serverConfigs" :key="index"
				  :class="{ active: currentConfigIndex === index }" @click="switchServer(index)">
				<text class="config-url">{{ config.url }}</text>
				<text class="config-status">{{ index === currentConfigIndex ? '当前' : '备用' }}</text>
			</view>
		</view>
	</view>
</template>

<script>
import io from 'socket.io-client'

export default {
	data() {
		return {
			socket: null,
			isConnected: false,
			isConnecting: false,
			reconnectAttempts: 0,
			currentConfigIndex: 0,
			logs: [],
			serverConfigs: [
				{
					url: 'ws://*************/',
					options: {}
				}
			]
		}
	},
	computed: {
		statusText() {
			if (this.isConnected) return '已连接'
			if (this.isConnecting) return '连接中'
			return '未连接'
		},
		statusClass() {
			if (this.isConnected) return 'connected'
			if (this.isConnecting) return 'connecting'
			return 'disconnected'
		},
		currentUrl() {
			return this.serverConfigs[this.currentConfigIndex]?.url || '未知'
		}
	},
	onLoad() {
		this.addLog('info', '页面加载完成')
	},
	onUnload() {
		if (this.socket) {
			this.socket.disconnect()
		}
	},
	methods: {
		// 测试连接
		testConnect() {
			this.addLog('info', '开始测试连接...')
			this.connectToServer()
		},

		// 连接到服务器
		connectToServer() {
			if (this.socket) {
				this.socket.disconnect()
			}

			this.isConnecting = true
			this.isConnected = false

			const config = this.serverConfigs[this.currentConfigIndex]
			const token = uni.getStorageSync('token')

			if (!token) {
				this.addLog('error', '未找到token，请先登录')
				this.isConnecting = false
				return
			}

			const socketOptions = {
				extraHeaders: {
					'Authorization': token
				}
			}

			this.addLog('info', `正在连接: ${config.url}`)

			try {
				this.socket = io(config.url, socketOptions)
				this.setupSocketEvents()
			} catch (error) {
				this.addLog('error', `创建连接失败: ${error.message}`)
				this.isConnecting = false
			}
		},

		// 设置Socket事件监听
		setupSocketEvents() {
			this.socket.on('connect', () => {
				this.addLog('success', 'WebSocket连接成功!')
				this.isConnected = true
				this.isConnecting = false
				this.reconnectAttempts = 0
			})

			this.socket.on('disconnect', (reason) => {
				this.addLog('warning', `连接断开: ${reason}`)
				this.isConnected = false
				this.isConnecting = false
			})

			this.socket.on('connect_error', (error) => {
				this.addLog('error', `连接错误: ${error.message}`)
				this.isConnected = false
				this.isConnecting = false
				this.reconnectAttempts++
			})

			this.socket.on('private_msg', (data) => {
				this.addLog('info', `收到消息: ${JSON.stringify(data)}`)
			})

			this.socket.on('message', (data) => {
				this.addLog('info', `收到消息: ${JSON.stringify(data)}`)
			})
		},

		// 断开连接
		testDisconnect() {
			if (this.socket) {
				this.socket.disconnect()
				this.addLog('info', '主动断开连接')
			}
		},

		// 发送测试消息
		testSendMessage() {
			if (!this.socket || !this.isConnected) {
				this.addLog('error', 'WebSocket未连接')
				return
			}

			const testMessage = {
				to: 1,
				content: `测试消息 - ${new Date().toLocaleTimeString()}`,
				type: "text",
				timestamp: Math.floor(Date.now() / 1000)
			}

			this.addLog('info', `发送测试消息: ${JSON.stringify(testMessage)}`)
			this.socket.emit('private_msg', testMessage)
		},

		// 切换服务器
		switchServer(index) {
			this.currentConfigIndex = index
			this.addLog('info', `切换到服务器: ${this.serverConfigs[index].url}`)

			if (this.socket) {
				this.socket.disconnect()
			}
		},

		// 添加日志
		addLog(type, message) {
			const log = {
				type,
				message,
				time: new Date().toLocaleTimeString()
			}
			this.logs.unshift(log)

			// 限制日志数量
			if (this.logs.length > 100) {
				this.logs = this.logs.slice(0, 100)
			}

			console.log(`[${type.toUpperCase()}] ${message}`)
		},

		// 清空日志
		clearLogs() {
			this.logs = []
			this.addLog('info', '日志已清空')
		}
	}
}
</script>

<style lang="scss" scoped>
.test-container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 30rpx;

	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
}

.status-section {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;

	.status-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0;
		}

		.label {
			font-size: 28rpx;
			color: #666;
		}

		.status {
			font-size: 28rpx;
			font-weight: bold;

			&.connected {
				color: #2ed573;
			}

			&.connecting {
				color: #ffa502;
			}

			&.disconnected {
				color: #ff4757;
			}
		}

		.value {
			font-size: 28rpx;
			color: #333;
		}
	}
}

.button-section {
	display: flex;
	flex-direction: column;
	gap: 20rpx;
	margin-bottom: 30rpx;

	.test-btn {
		height: 80rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		border: none;

		&.connect-btn {
			background-color: #1CBBB4;
			color: #fff;
		}

		&.disconnect-btn {
			background-color: #ff4757;
			color: #fff;
		}

		&.send-btn {
			background-color: #5352ed;
			color: #fff;
		}

		&:disabled {
			opacity: 0.5;
		}
	}
}

.log-section {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;

	.log-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		.log-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}

		.clear-btn {
			background-color: #ff4757;
			color: #fff;
			border: none;
			border-radius: 8rpx;
			padding: 10rpx 20rpx;
			font-size: 24rpx;
		}
	}

	.log-content {
		height: 400rpx;
		border: 1rpx solid #eee;
		border-radius: 8rpx;
		padding: 20rpx;

		.log-item {
			margin-bottom: 15rpx;
			padding: 10rpx;
			border-radius: 6rpx;

			&.info {
				background-color: #f1f2f6;
			}

			&.success {
				background-color: #d1f2eb;
			}

			&.warning {
				background-color: #fef9e7;
			}

			&.error {
				background-color: #fadbd8;
			}

			&.debug {
				background-color: #e8f4fd;
			}

			.log-time {
				font-size: 22rpx;
				color: #999;
				margin-right: 20rpx;
			}

			.log-message {
				font-size: 24rpx;
				color: #333;
			}
		}
	}
}

.config-section {
	background-color: #fff;
	border-radius: 12rpx;
	padding: 30rpx;

	.config-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}

	.config-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx;
		margin-bottom: 15rpx;
		border-radius: 8rpx;
		border: 2rpx solid #eee;
		cursor: pointer;

		&.active {
			border-color: #1CBBB4;
			background-color: #f0fffe;
		}

		.config-url {
			font-size: 26rpx;
			color: #333;
		}

		.config-status {
			font-size: 24rpx;
			color: #666;
		}
	}
}
</style>
</template>
<parameter name="add_last_line_newline">true
