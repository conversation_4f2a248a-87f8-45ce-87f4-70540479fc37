<template>
  <view class="content">
    <!-- 头部导航栏 -->
     <u-navbar :autoBack="true" title="动态发布" :is-fixed="true" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder>
    <!-- <u-navbar :autoBack="true" :is-fixed="true" :is-back="true" title="动态发布"> -->
      <view slot="right">
        <u-button text="发布" size="mini" :disabled="postContent.trim() === ''" color="#14b19e" @click="publish"></u-button>
      </view>
    </u-navbar>

    <!-- 发布内容输入区 -->
    <view class="u-m-t-30 u-m-l-30 u-m-r-30">
      <u--textarea v-model="postContent" placeholder="说点什么，带图的内容在社区更受欢迎哦~" count maxlength="500" height="200"></u--textarea>
      <!-- <u-input v-model="postContent" type="textarea" placeholder="请输入发布内容" maxlength="200"></u-input> -->
      <!-- <text class="u-font-24">{{ postContent.length }}/200</text> -->
    </view>

    <!-- 图片上传区 -->
    <view class="pic-container">
      <!-- 图片上传组件 -->
      <u-upload 
        :file-list="fileList" 
        :max-count="1" 
        width="150"
        height="150"
        border="surround"
        @afterRead="afterRead"
        @delete="onDelete"
      ></u-upload>

      <!-- 图片预览区域 -->
      <view v-if="previewImage" class="preview-container">
        <image :src="previewImage" mode="aspectFill" class="preview-image"></image>
        <button @click="onDelete" class="delete-button">删除图片</button>
      </view>
    </view>

    <!-- 提示信息 -->
    <view class="u-m-t-30 u-m-l-30 u-m-r-30 u-font-24 u-text-grey">
      请勿发布违规内容
    </view>
  </view>
</template>
<script>
import {communityApi } from '@/utils/api'
export default {
  data() {
    return {
      postContent: '',
      fileList: [],
       // 预览图片地址
      previewImage: null,
    };
  },
  methods: {
     /**
     * 处理图片读取完成事件
     * @param {Object} file - 读取完成的文件对象
     */
    afterRead(file) {
      console.log('文件读取完成：', file);
      this.fileList.push(file.file);
      // // 创建图片预览地址
      // this.previewImage = file.url || URL.createObjectURL(file.raw);
    },
    /**
     * 删除图片
     */
    onDelete() {
      this.fileList = [];
      this.previewImage = null;
    },
  
    async publish() {
      // 这里可以添加发布逻辑，例如将内容和图片上传到服务器
      if (this.postContent.trim() === '') {
        uni.showToast({
          title: '发布内容不能为空',
          icon: 'none'
        });
        return;
      }

      const params = {
        content: this.postContent,
        pics: this.fileList[0]&&this.fileList[0].url?this.fileList[0].url:null,
        type:1
      };
      
      await communityApi.addCommunityArticle(params).then(res => {
            uni.hideLoading();
            if(res.code == 200){
              // 清空内容和图片列表
              this.postContent = '';
              this.fileList = [];
              uni.navigateTo({
                url: '/pages/community/publishsucceed'
              })
              

            }else{
              uni.showToast({
                title: res.msg,
                icon: 'none',
              });
            }
            // this.postList=res.data.data;
            // console.log(res.data.data[0],'获取信息成功')
            // this.comments=res.data.data[0].comments;
        //   this.info = res.data
        })

      // 模拟发布成功
      // uni.showToast({
      //   title: '发布成功',
      //   icon: 'success'
      // });

        }
  },
  beforeDestroy() {
    // 销毁前释放预览图片内存
    if (this.previewImage && this.previewImage.startsWith('blob:')) {
      URL.revokeObjectURL(this.previewImage);
    }
  }
};
</script>

<style scoped>
.uni-textarea-placeholder{
  font-size: 24rpx;
}
.content {
  background-color: #fff;
  padding: 30rpx;
}

.u-input {
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 10px;
}
.pic-container{
      margin-top:40rpx;
  height: 200rpx;
  .uicon-close{
    font-size: 22rpx !important;
    line-height:18rpx!important;
  }
}
</style>


