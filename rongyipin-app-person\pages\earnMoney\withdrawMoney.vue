<template>
  <view class="withdraw-page">
    <!-- 头部导航栏 -->
    <u-navbar :autoBack="true" title="提现" :is-fixed="true" :leftIconSize="32" :leftIconColor="'#222'" bgColor="#fff" fixed safeAreaInsetTop placeholder></u-navbar>

    <!-- 提现金额输入 -->
    <view class="withdraw-content">
      <view class="withdraw-input">
        <text class="icon">¥</text>
        <input type="number" v-model="withdrawAmount" placeholder="请输入提现金额" />
      </view>

      <!-- 账户余额 -->
      <view class="balance-info">
        <text>账户余额 ¥{{ balance }}</text>
        <text @click="withdrawAll" class="withdraw-all">全部提现</text>
      </view>

      <!-- 确认提现按钮 -->
      <view class="confirm-btn">
        <button @click="confirmWithdraw">确认提现</button>
      </view>

      <!-- 提现规则 -->
      <view class="withdraw-rules">
        <text>1. 仅支持提现至本人名下正常使用且绑定成功的银行卡</text>
        <text>2. 每月骑手可以获得数次免费提现次数，超出次数会收取人民币1.0元/笔手续费</text>
        <text>3. 提现申请后预计1-3个工作日左右到账，建议每周一或每周三进行提现，最快可2小时到账</text>
        <text @click="showRulesDetail">《提现规则详情》</text>
      </view>
    </view>
  </view>
</template>

<script>
import { onLoad } from 'uview-ui/libs/mixin/mixin';
import { rider } from '@/utils/api.js';
export default {
  data() {
    return {
      balance: '', // 账户余额
      withdrawAmount: '' // 提现金额
    };
  },
  onLoad(options) {
    this.balance=options.balance;
    this.account_id=options.account_id;
  },
  methods: {
    withdrawAll() {
      this.withdrawAmount = this.balance;

    },
    confirmWithdraw() {
      if (!this.withdrawAmount || this.withdrawAmount <= 0) {
        uni.showToast({
          title: '请输入有效的提现金额',
          icon: 'none'
        });
        return;
      }

      if (parseFloat(this.withdrawAmount) > this.balance) {
        uni.showToast({
          title: '提现金额不能超过账户余额',
          icon: 'none'
        });
        return;
      }
      const params = { 
        amount: this.withdrawAmount,
        account_id:this.account_id,
        type:3
     };
      // 这里可以添加实际的提现逻辑，例如调用接口进行提现操作
       rider.withdrawalApply(params).then(res => {
             uni.hideLoading();
             console.log(res);
             if (res.code === 200) { 
                uni.showToast({
                  title: '提现成功',
                  icon: ''
                });
                uni.navigateback({
                    delta: 2
                });
             }
       }).catch(err => { 
      });
    },
    showRulesDetail() {
      // 这里可以打开提现规则详情页面
      uni.navigateTo({
        url: '/pages/earnMoney/withdrawRules'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.withdraw-page {
  padding: 20rpx;
  background-color: #f5f5f5;
}

.withdraw-content {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.withdraw-input {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;

  .icon {
    font-size: 40rpx;
    color: #FF5722;
    margin-right: 10rpx;
  }

  input {
    font-size: 32rpx;
    color: #333;
    flex: 1;
  }
}

.balance-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;

  .withdraw-all {
    color: #FF5722;
    font-size: 28rpx;
  }
}

.confirm-btn {
  button {
    width: 100%;
    height: 80rpx;
    background-color: #FF5722;
    color: #fff;
    font-size: 32rpx;
    border-radius: 10rpx;
  }
}

.withdraw-rules {
  font-size: 24rpx;
  color: #666;
  margin-top: 20rpx;

  text:last-child {
    color: #FF5722;
    margin-top: 10rpx;
  }
}
</style>

