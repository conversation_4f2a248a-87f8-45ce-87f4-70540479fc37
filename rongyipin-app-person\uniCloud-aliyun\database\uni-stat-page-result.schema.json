// 页面统计结果表
{
	"bsonType": "object",
	"description": "存储汇总的页面访问日志的数据",
	"required": [],
	"permission": {
		"read": "'READ_UNI_STAT_PAGE_RESULT' in auth.permission",
		"create": false,
		"update": false,
		"delete": false
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"appid": {
			"bsonType": "string",
			"description": "应用ID"
		},
		"platform_id": {
			"bsonType": "string",
			"description": "应用平台ID，对应uni-stat-app-platforms._id",
			"foreignKey": "uni-stat-app-platforms._id"
		},
		"channel_id": {
			"bsonType": "string",
			"description": "渠道\/场景值ID，对应uni-stat-app-channels._id",
			"foreignKey": "uni-stat-app-channels._id"
		},
		"version_id": {
			"bsonType": "string",
			"description": "应用版本ID，对应opendb-app-versions._id",
			"foreignKey": "opendb-app-versions._id"
		},
		"page_id": {
			"bsonType": "string",
			"description": "页面表ID，对应页面表ID，对应uni-stat-pages._id",
			"foreignKey": "uni-stat-pages._id"
		},
		"visit_times": {
			"bsonType": "int",
			"description": "访问次数"
		},
		"visit_devices": {
			"bsonType": "int",
			"description": "访问设备数"
		},
		"exit_times": {
			"bsonType": "int",
			"description": "退出次数"
		},
		"duration": {
			"bsonType": "int",
			"description": "访问总时长,单位秒"
		},
		"share_count": {
			"bsonType": "int",
			"description": "分享次数"
		},
		"entry_devices": {
			"bsonType": "int",
			"description": "当前页作为入口页的设备数"
		},
		"entry_users": {
			"bsonType": "int",
			"description": "当前页作为入口页的用户数"
		},
		"entry_count": {
			"bsonType": "int",
			"description": "当前页作为入口页的总次数"
		},
		"entry_duration": {
			"bsonType": "int",
			"description": "当前页作为入口时，本页面的总访问时长,单位秒"
		},
		"bounce_times": {
			"bsonType": "int",
			"description": "跳出次数"
		},
		"bounce_rate": {
			"bsonType": "double",
			"description": "跳出率"
		},
		"dimension": {
			"bsonType": "string",
			"description": "统计范围 day:按天统计，hour:按小时统计",
			"enum": [{
				"text": "月",
				"value": "month"
			}, {
				"text": "周",
				"value": "week"
			}, {
				"text": "天",
				"value": "day"
			}, {
				"text": "小时",
				"value": "hour"
			}]
		},
		"stat_date": {
			"bsonType": "int",
			"description": "统计日期，格式yyyymmdd，例:20211201"
		},
		"start_time": {
			"bsonType": "timestamp",
			"description": "开始时间"
		},
		"end_time": {
			"bsonType": "timestamp",
			"description": "结束时间"
		}
	}
}
